<?php
/**
 * The template for displaying single job posts
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <div class="content-area">
            <div class="main-content">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class('job-single'); ?>>
                        <header class="entry-header">
                            <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                            
                            <?php
                            // Get ACF fields
                            $job_location = krystelis_get_field('location');
                            $apply_url = krystelis_get_field('apply_url');
                            ?>
                            
                            <?php if ($job_location) : ?>
                                <div class="job-meta">
                                    <div class="job-location">
                                        <strong><?php esc_html_e('Location:', 'krystelis-custom'); ?></strong>
                                        <span><?php echo esc_html($job_location); ?></span>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </header>

                        <?php if (has_post_thumbnail()) : ?>
                            <div class="post-thumbnail">
                                <?php the_post_thumbnail('large'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="entry-content">
                            <?php
                            the_content();

                            wp_link_pages(array(
                                'before' => '<div class="page-links">' . esc_html__('Pages:', 'krystelis-custom'),
                                'after'  => '</div>',
                            ));
                            ?>
                        </div>

                        <footer class="entry-footer">
                            <div class="job-actions">
                                <?php if ($apply_url) : ?>
                                    <a href="<?php echo esc_url($apply_url); ?>" class="job-apply-btn" target="_blank" rel="noopener">
                                        <?php esc_html_e('Apply for this Position', 'krystelis-custom'); ?>
                                    </a>
                                <?php endif; ?>
                                
                                <a href="<?php echo esc_url(get_post_type_archive_link('job')); ?>" class="back-to-jobs">
                                    &larr; <?php esc_html_e('Back to All Jobs', 'krystelis-custom'); ?>
                                </a>
                            </div>
                        </footer>
                    </article>

                    <?php
                    // Related jobs
                    $related_jobs = new WP_Query(array(
                        'post_type' => 'job',
                        'posts_per_page' => 3,
                        'post__not_in' => array(get_the_ID()),
                        'orderby' => 'date',
                        'order' => 'DESC'
                    ));
                    
                    if ($related_jobs->have_posts()) :
                    ?>
                        <section class="related-jobs">
                            <h3><?php esc_html_e('Other Open Positions', 'krystelis-custom'); ?></h3>
                            <div class="jobs-grid">
                                <?php while ($related_jobs->have_posts()) : $related_jobs->the_post(); ?>
                                    <?php
                                    $rel_location = krystelis_get_field('location');
                                    $rel_apply_url = krystelis_get_field('apply_url');
                                    ?>
                                    <div class="job-card">
                                        <h4><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                                        
                                        <?php if ($rel_location) : ?>
                                            <div class="job-location">
                                                <strong><?php esc_html_e('Location:', 'krystelis-custom'); ?></strong>
                                                <span><?php echo esc_html($rel_location); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="job-excerpt">
                                            <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                                        </div>
                                        
                                        <div class="job-actions">
                                            <a href="<?php the_permalink(); ?>" class="view-job">
                                                <?php esc_html_e('View Details', 'krystelis-custom'); ?>
                                            </a>
                                            
                                            <?php if ($rel_apply_url) : ?>
                                                <a href="<?php echo esc_url($rel_apply_url); ?>" class="apply-job" target="_blank" rel="noopener">
                                                    <?php esc_html_e('Apply', 'krystelis-custom'); ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        </section>
                    <?php 
                    endif;
                    wp_reset_postdata();
                    ?>

                <?php endwhile; ?>
            </div>

            <?php
            // Only show sidebar on jobs if it has widgets
            if (is_active_sidebar('sidebar-1')) :
                get_sidebar();
            endif;
            ?>
        </div>
    </div>
</main>

<style>
.job-single .job-meta {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 5px;
    margin: 1rem 0;
}

.job-single .job-location {
    margin-bottom: 0.5rem;
}

.job-single .job-location strong {
    margin-right: 0.5rem;
}

.job-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.job-apply-btn {
    background: #0078d4;
    color: white;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.job-apply-btn:hover {
    background: #106ebe;
    color: white;
}

.back-to-jobs {
    color: #0078d4;
    text-decoration: none;
    font-weight: 600;
}

.back-to-jobs:hover {
    text-decoration: underline;
}

.related-jobs {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.related-jobs h3 {
    margin-bottom: 2rem;
    color: #333;
}

.related-jobs .jobs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.related-jobs .job-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.related-jobs .job-card h4 {
    margin-bottom: 1rem;
}

.related-jobs .job-card h4 a {
    color: #333;
    text-decoration: none;
}

.related-jobs .job-card h4 a:hover {
    color: #0078d4;
}

.related-jobs .job-location {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.related-jobs .job-excerpt {
    margin-bottom: 1.5rem;
    color: #666;
    line-height: 1.6;
}

.related-jobs .job-actions {
    display: flex;
    gap: 1rem;
    margin-top: 0;
}

.related-jobs .view-job,
.related-jobs .apply-job {
    padding: 0.5rem 1rem;
    text-decoration: none;
    border-radius: 3px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.related-jobs .view-job {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.related-jobs .view-job:hover {
    background: #e9ecef;
}

.related-jobs .apply-job {
    background: #0078d4;
    color: white;
}

.related-jobs .apply-job:hover {
    background: #106ebe;
    color: white;
}

@media (max-width: 768px) {
    .job-actions {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .related-jobs .jobs-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>
