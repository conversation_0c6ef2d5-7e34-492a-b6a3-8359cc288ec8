<?php
/**
 * Template part for displaying testimonials section
 *
 * @package Krystelis_Custom
 */

// Query testimonials
$testimonials_query = new WP_Query(array(
    'post_type' => 'testimonial',
    'posts_per_page' => 6,
    'post_status' => 'publish',
    'orderby' => 'date',
    'order' => 'DESC'
));
?>

<section class="testimonials-section">
    <!-- Container 1: Testimonial Heading -->
    <div class="testimonial-heading-container">
        <div class="section-header">
            <h2 class="section-title">What Our Clients Say</h2>
            <p class="section-subtitle">Hear from businesses that trust us with their success</p>
        </div>
    </div>

    <!-- Container 2: Testimonial Box -->
    <div class="testimonial-slider-container">
        <?php if ($testimonials_query->have_posts()) : ?>
            <div class="testimonials-slider" id="testimonials-slider">
                <div class="testimonials-wrapper">
                    <?php while ($testimonials_query->have_posts()) : $testimonials_query->the_post(); ?>
                        <?php
                        // Get ACF fields with fallbacks
                        $author_name = krystelis_get_field('author_name', get_the_ID(), 'Anonymous');
                        $quote = krystelis_get_field('quote', get_the_ID(), get_the_content());
                        $organization = krystelis_get_field('organization', get_the_ID(), '');
                        ?>
                        <div class="testimonial-slide">
                            <div class="testimonial-card">
                                <div class="quote-icon">
                                    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3 21C3 17.134 3 15.201 4.318 13.884C5.636 12.566 7.568 12.566 11.432 12.566C15.296 12.566 17.229 12.566 18.546 13.884C19.864 15.201 19.864 17.134 19.864 21H3Z" fill="currentColor"/>
                                        <path d="M8 8C8 5.791 9.791 4 12 4S16 5.791 16 8 14.209 12 12 12 8 10.209 8 8Z" fill="currentColor"/>
                                    </svg>
                                </div>
                                <blockquote class="testimonial-quote">
                                    "<?php echo esc_html($quote); ?>"
                                </blockquote>
                                <div class="testimonial-author">
                                    <h4 class="author-name"><?php echo esc_html($author_name); ?></h4>
                                    <?php if ($organization) : ?>
                                        <p class="author-organization"><?php echo esc_html($organization); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>

                <!-- Navigation buttons -->
                <div class="slider-navigation">
                    <button class="slider-btn prev-btn" id="prev-btn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                    <button class="slider-btn next-btn" id="next-btn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>

                <!-- Dots indicator -->
                <div class="slider-dots" id="slider-dots"></div>
            </div>
        <?php else : ?>
            <div class="no-testimonials">
                <p>No testimonials found. Please add some testimonials in the WordPress admin.</p>
            </div>
        <?php endif; ?>

        <?php wp_reset_postdata(); ?>
    </div>
</section>

<style>
.testimonials-section {
    padding: 4rem 0;
    background-color: #fff;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Container 1: Testimonial Heading */
.testimonial-heading-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    margin-bottom: 3rem;
}

/* Container 2: Testimonial Box */
.testimonial-slider-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.testimonials-slider {
    position: relative;
    width: calc(100% - 20px);
    margin: 0 10px;
}

.testimonials-wrapper {
    overflow: hidden;
    border-radius: 10px;
    height: 300px;
}

.testimonial-slide {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.testimonial-slide.active {
    display: block;
}

.testimonial-card {
    background: #f8f9fa;
    padding: 3rem 2rem;
    text-align: center;
    border-radius: 10px;
}

.quote-icon {
    color: #0078d4;
    margin-bottom: 1.5rem;
}

.testimonial-quote {
    font-size: 1.25rem;
    line-height: 1.6;
    color: #333;
    font-style: italic;
    margin-bottom: 2rem;
    border: none;
    padding: 0;
}

.testimonial-author {
    margin-top: 2rem;
}

.author-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.author-organization {
    color: #666;
    font-size: 0.9rem;
}

.slider-navigation {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.slider-btn {
    background: #0078d4;
    color: white;
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.slider-btn:hover {
    background: #106ebe;
}

.slider-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dot.active {
    background: #0078d4;
}

.no-testimonials {
    text-align: center;
    padding: 2rem;
    color: #666;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@media (max-width: 768px) {
    .testimonials-section {
        padding: 2rem 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .testimonial-card {
        padding: 2rem 1rem;
    }

    .testimonial-quote {
        font-size: 1.1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('testimonials-slider');
    if (!slider) return;

    const slides = slider.querySelectorAll('.testimonial-slide');
    const totalSlides = slides.length;

    if (totalSlides <= 1) return;

    let currentSlide = 0;

    // Create dots
    const dotsContainer = slider.querySelector('#slider-dots');
    for (let i = 0; i < totalSlides; i++) {
        const dot = document.createElement('span');
        dot.className = 'dot';
        dot.setAttribute('data-slide', i);
        dotsContainer.appendChild(dot);
    }

    const dots = dotsContainer.querySelectorAll('.dot');

    // Show first slide
    showSlide(0);

    // Next button
    const nextBtn = slider.querySelector('#next-btn');
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        });
    }

    // Previous button
    const prevBtn = slider.querySelector('#prev-btn');
    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(currentSlide);
        });
    }

    // Dot navigation
    dots.forEach(function(dot) {
        dot.addEventListener('click', function() {
            currentSlide = parseInt(this.getAttribute('data-slide'));
            showSlide(currentSlide);
        });
    });

    // Auto-play
    setInterval(function() {
        currentSlide = (currentSlide + 1) % totalSlides;
        showSlide(currentSlide);
    }, 5000);

    function showSlide(index) {
        slides.forEach(function(slide) {
            slide.classList.remove('active');
        });
        dots.forEach(function(dot) {
            dot.classList.remove('active');
        });

        slides[index].classList.add('active');
        dots[index].classList.add('active');
    }
});
</script>
