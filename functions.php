<?php
/**
 * Krystelis Custom Theme functions and definitions
 *
 * @package Krystelis_Custom
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include custom post types
require get_template_directory() . '/inc/custom-post-types.php';

// Include enqueue functions
require get_template_directory() . '/inc/enqueue.php';

/**
 * Theme setup
 */
function krystelis_theme_setup() {
    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Add support for responsive embedded content
    add_theme_support('responsive-embeds');

    // Add support for editor styles
    add_theme_support('editor-styles');

    // Add support for wide alignment
    add_theme_support('align-wide');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', 'krystelis-custom'),
        'footer'  => esc_html__('Footer Menu', 'krystelis-custom'),
    ));

    // Add support for HTML5 markup
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));

    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 250,
        'width'       => 250,
        'flex-width'  => true,
        'flex-height' => true,
    ));

    // Add support for custom background
    add_theme_support('custom-background', array(
        'default-color' => 'ffffff',
        'default-image' => '',
    ));

    // Add support for selective refresh for widgets
    add_theme_support('customize-selective-refresh-widgets');
}
add_action('after_setup_theme', 'krystelis_theme_setup');

/**
 * Set the content width in pixels, based on the theme's design and stylesheet
 */
function krystelis_theme_content_width() {
    $GLOBALS['content_width'] = apply_filters('krystelis_theme_content_width', 800);
}
add_action('after_setup_theme', 'krystelis_theme_content_width', 0);

/**
 * Register widget area
 */
function krystelis_theme_widgets_init() {
    register_sidebar(array(
        'name'          => esc_html__('Sidebar', 'krystelis-custom'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here.', 'krystelis-custom'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));

    register_sidebar(array(
        'name'          => esc_html__('Footer', 'micro-theme'),
        'id'            => 'footer-1',
        'description'   => esc_html__('Add widgets here to appear in your footer.', 'micro-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'krystelis_theme_widgets_init');

/**
 * Custom excerpt length
 */
function krystelis_theme_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'krystelis_theme_excerpt_length', 999);

/**
 * Custom excerpt more
 */
function krystelis_theme_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'krystelis_theme_excerpt_more');

/**
 * Add custom classes to body
 */
function krystelis_theme_body_classes($classes) {
    // Add a class if sidebar is active
    if (is_active_sidebar('sidebar-1')) {
        $classes[] = 'has-sidebar';
    } else {
        $classes[] = 'no-sidebar';
    }

    return $classes;
}
add_filter('body_class', 'krystelis_theme_body_classes');

/**
 * Improve WordPress SEO
 */
function krystelis_theme_remove_version() {
    return '';
}
add_filter('the_generator', 'krystelis_theme_remove_version');

/**
 * Add security headers
 */
function krystelis_theme_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
    }
}
add_action('send_headers', 'krystelis_theme_security_headers');

/**
 * Optimize WordPress performance
 */
function krystelis_theme_optimize_performance() {
    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');

    // Remove unnecessary meta tags
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
}
add_action('init', 'krystelis_theme_optimize_performance');

/**
 * Helper function to get image URL with fallback
 */
function krystelis_theme_get_image_url($image_path) {
    $image_url = get_template_directory_uri() . '/assets/images/' . $image_path;

    // Check if image exists
    $image_file = get_template_directory() . '/assets/images/' . $image_path;
    if (!file_exists($image_file)) {
        // Log error for debugging
        error_log('Krystelis Theme: Image not found - ' . $image_file);
        return false;
    }

    return $image_url;
}

/**
 * Helper function to safely get ACF field with fallback
 */
function krystelis_get_field($field_name, $post_id = null, $fallback = '') {
    if (function_exists('get_field')) {
        $value = get_field($field_name, $post_id);
        return $value ? $value : $fallback;
    }
    return $fallback;
}

/**
 * Enqueue scripts and styles.
 */
function micro_theme_scripts() {
    // Main stylesheet
    wp_enqueue_style('micro-theme-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Enqueue custom styles
    wp_enqueue_style('micro-theme-custom', get_template_directory_uri() . '/assets/css/custom.css', array(), '1.0.0');
    
    // Enqueue header styles
    wp_enqueue_style('micro-theme-header', get_template_directory_uri() . '/assets/css/header-styles.css', array(), '1.0.0');
    
    // Enqueue careers styles
    if (is_page('careers')) {
        wp_enqueue_style('micro-theme-careers', get_template_directory_uri() . '/assets/css/careers.css', array(), '1.0.0');
    }
    
    // TEMP: Always load webinars.css
    wp_enqueue_style('micro-theme-webinars', get_template_directory_uri() . '/assets/css/webinars.css', array(), '1.0.0');

    // Enqueue JavaScript files
    wp_enqueue_script('micro-theme-navigation', get_template_directory_uri() . '/assets/js/navigation.js', array(), '1.0.0', true);
    wp_enqueue_script('micro-theme-custom', get_template_directory_uri() . '/assets/js/custom.js', array('jquery'), '1.0.0', true);
    
    if (is_page('careers')) {
        wp_enqueue_script('micro-theme-careers', get_template_directory_uri() . '/assets/js/careers-form.js', array('jquery'), '1.0.0', true);
    }

    // wp_enqueue_script('mobile-hero-fix', get_template_directory_uri() . '/assets/js/mobile-hero-fix.js', array(), '1.0.0', true);
    // wp_enqueue_style('mobile-hero-fix', get_template_directory_uri() . '/assets/css/mobile-hero-fix.css', array(), '1.0.0');
}
add_action('wp_enqueue_scripts', 'micro_theme_scripts');
