<?php
/**
 * The template for displaying all single posts
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <div class="content-area">
            <div class="main-content">
                <?php while (have_posts()) : the_post(); ?>
                    <div class="custom-article-header-fullwidth">
                        <div class="custom-meta-bar">
                            <span><time datetime="<?php echo esc_attr(get_the_date('c')); ?>"><?php echo get_the_date(); ?></time></span>
                            <span>|</span>
                            <span><?php echo esc_html(get_the_author()); ?> Content Team</span>
                            <span>|</span>
                            <?php if (has_category()) : ?>
                                <span><?php the_category(' | '); ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="custom-article-header-flex">
                            <div class="custom-title-col">
                                <h1><?php the_title(); ?></h1>
                            </div>
                            <div class="custom-image-col">
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="post-thumbnail">
                                        <?php the_post_thumbnail('large'); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="entry-content-fullwidth">
                        <div class="custom-content-wrap">
                            <?php
                            the_content();
                            wp_link_pages(array(
                                'before' => '<div class="page-links">' . esc_html__('Pages:', 'krystelis-custom'),
                                'after'  => '</div>',
                            ));
                            ?>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>

            <?php get_sidebar(); ?>
        </div>
    </div>
</main>

<?php get_footer(); ?>
