<?php
/**
 * Template part for displaying Get In Touch CTA section
 *
 * @package Krystelis_Custom
 */
?>

<!-- Get In Touch CTA Section -->
<section class="cta-section">
    <div class="cta-background">
        <div class="cta-content">
            <p class="cta-subtitle">INTERESTED IN LEARNING MORE?</p>
            <h2 class="cta-title">Get In Touch</h2>
            <a href="<?php echo esc_url(home_url('/contact')); ?>" class="cta-button">CONTACT US</a>
        </div>
    </div>
</section>

<style>
/* CTA Section Styling */
.cta-section {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 4rem 1rem;
    margin: 0 30px;
}

.cta-background {
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;
    padding: 7rem 2rem;
    border-radius: 20px;
    background: linear-gradient(180deg, #39A1FF 0%, #0072DA 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background-image:
        url('<?php echo get_template_directory_uri(); ?>/assets/images/Bg-Shape-Orange.png'),
        url('<?php echo get_template_directory_uri(); ?>/assets/images/Bg-Shape-blue.png');
    background-position:
        top right,
        bottom left;
    background-repeat: no-repeat, no-repeat;
    background-size: 300px auto, 250px auto;
}

.cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 600px;
    margin: 0 auto;
}

.cta-subtitle {
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 2px;
    margin-bottom: 1rem;
    opacity: 0.9;
    font-family: "Maven Pro", sans-serif;
    text-transform: uppercase;
}

.cta-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.2;
    font-family: "Maven Pro", sans-serif;
    color: white;
}

.cta-button {
    display: inline-block;
    background: white;
    color: #0072DA;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: "Maven Pro", sans-serif;
    letter-spacing: 1px;
    text-transform: uppercase;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cta-button:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: #0072DA;
    text-decoration: none;
}

/* CTA Section Responsive */
@media (max-width: 768px) {
    .cta-section {
        padding: 2rem 0.5rem;
        margin: 0 5px;
    }

    .cta-background {
        padding: 4rem 1.5rem;
        border-radius: 15px;
        background-size: 200px auto, 150px auto;
    }

    .cta-title {
        font-size: 2.5rem;
    }

    .cta-subtitle {
        font-size: 0.9rem;
    }

    .cta-button {
        padding: 0.875rem 2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .cta-background {
        padding: 3rem 1rem;
        border-radius: 12px;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-subtitle {
        font-size: 0.8rem;
        letter-spacing: 1px;
    }

    .cta-button {
        padding: 0.75rem 1.5rem;
        font-size: 0.85rem;
    }
}

/* Ensure full-width layout with minimal margins as per user preferences */
@media (min-width: 1200px) {
    .cta-section {
        margin: 0 30px; /* 30px left and right gaps as preferred */
    }
}
</style>
