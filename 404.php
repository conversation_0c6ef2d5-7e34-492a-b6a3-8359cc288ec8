<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @package Micro_Theme
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <div class="content-area">
            <div class="main-content">
                <section class="error-404 not-found">
                    <header class="page-header">
                        <h1 class="page-title"><?php esc_html_e('Oops! That page can&rsquo;t be found.', 'micro-theme'); ?></h1>
                    </header>

                    <div class="page-content">
                        <p><?php esc_html_e('It looks like nothing was found at this location. Maybe try one of the links below or a search?', 'micro-theme'); ?></p>

                        <?php get_search_form(); ?>

                        <div class="widget widget_recent_entries">
                            <h2 class="widget-title"><?php esc_html_e('Most Used Categories', 'micro-theme'); ?></h2>
                            <ul>
                                <?php
                                wp_list_categories(array(
                                    'orderby'    => 'count',
                                    'order'      => 'DESC',
                                    'show_count' => 1,
                                    'title_li'   => '',
                                    'number'     => 10,
                                ));
                                ?>
                            </ul>
                        </div>

                        <?php
                        // Only show the widget if site has multiple published authors.
                        if (is_multi_author()) :
                        ?>
                            <div class="widget widget_authors">
                                <h2 class="widget-title"><?php esc_html_e('Try looking in the monthly archives.', 'micro-theme'); ?></h2>
                                <ol>
                                    <?php
                                    wp_get_archives(array(
                                        'type'  => 'monthly',
                                        'limit' => 12,
                                    ));
                                    ?>
                                </ol>
                            </div>
                        <?php endif; ?>

                        <div class="widget">
                            <h2 class="widget-title"><?php esc_html_e('Recent Posts', 'micro-theme'); ?></h2>
                            <ul>
                                <?php
                                $recent_posts = wp_get_recent_posts(array(
                                    'numberposts' => 5,
                                    'post_status' => 'publish',
                                ));
                                foreach ($recent_posts as $post_item) :
                                ?>
                                    <li>
                                        <a href="<?php echo esc_url(get_permalink($post_item['ID'])); ?>">
                                            <?php echo esc_html($post_item['post_title']); ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </section>
            </div>

            <?php get_sidebar(); ?>
        </div>
    </div>
</main>

<?php get_footer(); ?>
