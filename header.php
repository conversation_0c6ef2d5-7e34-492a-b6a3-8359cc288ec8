<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @package Krystelis_Custom
 */
?>
<!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">

    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e('Skip to content', 'krystelis-custom'); ?></a>

    <header id="masthead" class="site-header">
        <!-- Header Box Container -->
        <div class="header-box">
            <!-- Blue Top Bar -->
            
            <!-- Main Header Content -->
            <div class="header-main">
                <!-- <div class="header-top-bar"></div> -->
                <div class="header-container">
                <!-- Logo Section -->
                <div class="site-branding">
                    <?php
                    if (has_custom_logo()) {
                        the_custom_logo();
                    } else {
                        echo '<a href="https://www.linkedin.com/company/krystelis-ltd/" target="_blank" class="logo-link">';
                        echo '<img src="' . get_template_directory_uri() . '/assets/images/krystelis-logo.png" alt="Krystelis" class="site-logo" />';
                        echo '</a>';
                    }
                    ?>
                </div>

                <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                    <span class="screen-reader-text">Menu</span>
                    <div class="menu-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </button>

                <!-- Mobile Sidebar Menu -->
<div id="mobile-sidebar-menu" class="mobile-sidebar-menu">
    <button class="sidebar-close" aria-label="Close Menu">&times;</button>
    <?php
    wp_nav_menu(array(
        'theme_location' => 'primary',
        'menu_id'        => 'mobile-menu',
        'container'      => false,
        'menu_class'     => 'sidebar-menu-list',
        'fallback_cb'    => 'krystelis_theme_fallback_menu',
    ));
    ?>
</div>
<div id="sidebar-overlay" class="sidebar-overlay"></div>

                <!-- Navigation Menu -->
                <nav id="site-navigation" class="main-navigation">
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_id'        => 'primary-menu',
                        'container'      => false,
                        'menu_class'     => 'nav-menu',
                        'fallback_cb'    => 'krystelis_theme_fallback_menu',
                    ));
                    ?>
                </nav>

                <!-- Social Section -->
                <div class="header-social">
                    <span class="connect-text">Connect with us</span>
                    <span class="social-divider">—</span>
                    <div class="social-icons">
                        <a href="https://www.linkedin.com/company/krystelis-ltd/" target="_blank" class="social-link">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="social-icon linkedin-icon">
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M100.3 448H7.4V148.9h92.9zM53.8 108.1C24.1 108.1 0 83.5 0 53.8a53.8 53.8 0 0 1 107.6 0c0 29.7-24.1 54.3-53.8 54.3zM447.9 448h-92.7V302.4c0-34.7-.7-79.2-48.3-79.2-48.3 0-55.7 37.7-55.7 76.7V448h-92.8V148.9h89.1v40.8h1.3c12.4-23.5 42.7-48.3 87.9-48.3 94 0 111.3 61.9 111.3 142.3V448z"/></svg>                            </svg>
                        </a>
                        <a href="https://x.com/KrystelisLtd" target="_blank" class="social-link">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="social-icon twitter-icon">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" fill="#0077B5"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>


    <div id="content" class="site-content">
    <?php
    if (is_page('careers') && isset($_GET['form_submitted']) && $_GET['form_submitted'] === 'true') {
        echo '<div class="success-message" style="
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            text-align: center;
        ">Thanks for contacting us! We will be in touch with you shortly.</div>';
    }
    ?>

<?php
/**
 * Fallback menu if no menu is assigned
 */
function krystelis_theme_fallback_menu() {
    echo '<ul id="primary-menu" class="menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">' . esc_html__('Home', 'krystelis-custom') . '</a></li>';

    // Show pages in menu
    $pages = get_pages(array('sort_column' => 'menu_order'));
    foreach ($pages as $page) {
        echo '<li><a href="' . esc_url(get_permalink($page->ID)) . '">' . esc_html($page->post_title) . '</a></li>';
    }

    echo '</ul>';
}
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const menuToggle = document.querySelector('.menu-toggle');
    const navigation = document.querySelector('.main-navigation');
    const navLinks = document.querySelectorAll('.nav-menu a, #primary-menu a, .menu a');

    // Function to remove active class from all links
    function removeActiveClass() {
    navLinks.forEach(link => {
        link.classList.remove('active-clicked');
    });
}

    // Function to set active class based on current page URL
    function setActiveMenu() {
    // If WordPress has already set the current-menu-item, do nothing
    if (document.querySelector('.main-navigation .current-menu-item a')) {
        return;
    }

        // Prioritize WordPress's own class if available
        let wpActiveLink = document.querySelector('.main-navigation .current-menu-item a');
        if (wpActiveLink) {
            wpActiveLink.classList.add('active-clicked');
            if (wpActiveLink.parentElement) {
                wpActiveLink.parentElement.classList.add('current-menu-item');
            }
            return; // Exit if WP has already marked an item
        }

        navLinks.forEach(link => {
            const linkPath = new URL(link.href).pathname;

            // Check if the current path starts with the link's path
            if (currentPath.startsWith(linkPath)) {
                // For the homepage link, we want an exact match or for it to be the only option.
                // This prevents it from being active on every other page.
                const isHomepage = linkPath === '/' || link.href === '<?php echo esc_js(home_url('/')); ?>';
                if (isHomepage && currentPath !== linkPath && currentPath.length > 1) {
                    // It's the homepage link but we are on a different page.
                    // We check if we have a better match.
                    if (bestMatchLength > 1) return;
                }

                if (linkPath.length > bestMatchLength) {
                    bestMatchLink = link;
                    bestMatchLength = linkPath.length;
                }
            }
        });

        if (bestMatchLink) {
            bestMatchLink.classList.add('active-clicked');
            if (bestMatchLink.parentElement) {
                bestMatchLink.parentElement.classList.add('current-menu-item');
            }
        } else {
            // Fallback for home page if no other match is found
             navLinks.forEach(link => {
                const homeUrl = "<?php echo esc_js(home_url('/')); ?>".replace(/\/$/, '');
                const linkUrl = link.href.replace(/\/$/, '');
                if (linkUrl === homeUrl) {
                    link.classList.add('active-clicked');
                    if (link.parentElement) link.parentElement.classList.add('current-menu-item');
                }
            });
        }
    }

    // Set active menu on page load
    setActiveMenu();

    // Add click event to all navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            removeActiveClass();
            this.classList.add('active-clicked');
            if (this.parentElement) this.parentElement.classList.add('current-menu-item');
        });
    });

    // Mobile menu toggle functionality
    if (menuToggle && navigation) {
        menuToggle.addEventListener('click', function() {
            navigation.classList.toggle('toggled');
            const isExpanded = navigation.classList.contains('toggled');
            menuToggle.setAttribute('aria-expanded', isExpanded);
            const menuIcon = menuToggle.querySelector('.menu-icon');
            if (menuIcon) {
                menuIcon.classList.toggle('active');
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!navigation.contains(event.target) && !menuToggle.contains(event.target)) {
                navigation.classList.remove('toggled');
                menuToggle.setAttribute('aria-expanded', 'false');
                const menuIcon = menuToggle.querySelector('.menu-icon');
                if (menuIcon) {
                    menuIcon.classList.remove('active');
                }
            }
        });

        // Close menu when window is resized to desktop
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                navigation.classList.remove('toggled');
                menuToggle.setAttribute('aria-expanded', 'false');
                const menuIcon = menuToggle.querySelector('.menu-icon');
                if (menuIcon) {
                    menuIcon.classList.remove('active');
                }
            }
        });
    }

    // Handle browser back/forward navigation
    window.addEventListener('popstate', function() {
        // Small delay to ensure the URL has changed
        setTimeout(setActiveMenu, 10);
    });
});

document.addEventListener('DOMContentLoaded', function() {
    // ...existing code...

    // Sidebar menu logic
    const sidebarMenu = document.getElementById('mobile-sidebar-menu');
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    const menuToggle = document.querySelector('.menu-toggle');
    const sidebarClose = document.querySelector('.sidebar-close');

    if (menuToggle && sidebarMenu && sidebarOverlay && sidebarClose) {
        menuToggle.addEventListener('click', function(e) {
            if (window.innerWidth <= 1023) {
                e.preventDefault();
                sidebarMenu.classList.add('active');
                sidebarOverlay.classList.add('active');
            }
        });
        sidebarClose.addEventListener('click', function() {
            sidebarMenu.classList.remove('active');
            sidebarOverlay.classList.remove('active');
        });
        sidebarOverlay.addEventListener('click', function() {
            sidebarMenu.classList.remove('active');
            sidebarOverlay.classList.remove('active');
        });
        // Close sidebar on resize to desktop
        window.addEventListener('resize', function() {
            if (window.innerWidth > 1023) {
                sidebarMenu.classList.remove('active');
                sidebarOverlay.classList.remove('active');
            }
        });
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const menuItems = document.querySelectorAll('.nav-menu li.menu-item-has-children, #primary-menu li.menu-item-has-children, .menu li.menu-item-has-children');

    menuItems.forEach(function(item) {
        item.addEventListener('click', function(e) {
            if (window.innerWidth <= 1023) {
                // Only toggle if clicking the parent link or its arrow
                if (e.target === item.querySelector('a')) {
                    e.preventDefault();
                }
                // Toggle open class
                item.classList.toggle('open');
                // Close other open submenus
                menuItems.forEach(function(other) {
                    if (other !== item) other.classList.remove('open');
                });
            }
        });
    });

    // Close submenu when clicking outside (mobile)
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 1023) {
            menuItems.forEach(function(item) {
                if (!item.contains(e.target)) {
                    item.classList.remove('open');
                }
            });
        }
    });
});

document.addEventListener('DOMContentLoaded', function() {
    const sidebarMenu = document.getElementById('mobile-sidebar-menu');
    if (sidebarMenu) {
        const parentLinks = sidebarMenu.querySelectorAll('li.menu-item-has-children > a');
        parentLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const parentLi = this.parentElement;
                // Close all other open submenus
                sidebarMenu.querySelectorAll('li.menu-item-has-children.open').forEach(function(openLi) {
                    if (openLi !== parentLi) openLi.classList.remove('open');
                });
                // Toggle this submenu
                parentLi.classList.toggle('open');
            });
        });
    }
});

document.addEventListener("DOMContentLoaded", function() {
    document.body.classList.add('header-bounce-visible');
});

</script>

<style>
.header-box {
    opacity: 0;
    transform: translateY(-120px);
}

.header-bounce-visible .header-box {
    opacity: 1;
    animation: header-bounce-in 1.1s cubic-bezier(0.23, 1, 0.32, 1) both;
}

@keyframes header-bounce-in {
    0% {
        opacity: 0;
        transform: translateY(-120px);
    }
    60% {
        opacity: 1;
        transform: translateY(20px);
    }
    80% {
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>