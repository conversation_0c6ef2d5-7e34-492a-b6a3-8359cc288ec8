/**
 * Custom CSS for Krystelis Theme
 *
 * @package Krystelis_Custom
 */

/* Additional custom styles can be added here */

/* Homepage Layout Styles */
.site-content {
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Homepage Section Spacing */
.hero-section + .services-section {
    margin-top: 0;
}

.services-section + .testimonials-section {
    margin-top: 0;
}

.testimonials-section + .contact-section {
    margin-top: 0;
}

/* Ensure full-width sections */
.hero-section,
.services-section,
.testimonials-section,
.contact-section {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
}

/* Container consistency for homepage sections */
.hero-section .container,
.services-section .container,
.testimonials-section .container,
.contact-section .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Form validation styles */
.contact-form input.error,
.contact-form textarea.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

/* Button loading state */
.contact-form button[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Optimized transitions for homepage sections */
.hero-section,
.services-section,
.testimonials-section,
.contact-section {
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    /* Optimize rendering performance */
    will-change: auto;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

/* Responsive adjustments for homepage */
@media (max-width: 768px) {
    .hero-section .container,
    .services-section .container,
    .testimonials-section .container,
    .contact-section .container {
        padding: 0 15px;
    }
}

/* Navigation Menu Color Override - Highest Priority */
.site-header nav a,
.site-header .main-navigation a,
.site-header .nav-menu a,
.site-header #primary-menu a,
.header-main nav a,
.header-main .main-navigation a,
.header-main .nav-menu a,
.header-main #primary-menu a,
.header-container nav a,
.header-container .main-navigation a,
.header-container .nav-menu a,
.header-container #primary-menu a {
    color: #171c24 !important;
    text-decoration: none !important;
    font-family: "Maven Pro", sans-serif !important;
    font-size: 18.0018px !important;
}

.site-header nav a:hover,
.site-header .main-navigation a:hover,
.site-header .nav-menu a:hover,
.site-header #primary-menu a:hover,
.header-main nav a:hover,
.header-main .main-navigation a:hover,
.header-main .nav-menu a:hover,
.header-main #primary-menu a:hover,
.header-container nav a:hover,
.header-container .main-navigation a:hover,
.header-container .nav-menu a:hover,
.header-container #primary-menu a:hover,
.site-header .current-menu-item a,
.header-main .current-menu-item a,
.header-container .current-menu-item a {
    color: #007acc !important;
}

/* Form error styling */
.contact-form .error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

/* Optimized animation classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
    /* Optimize for scroll animations */
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
    will-change: auto; /* Remove will-change after animation */
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0078d4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Global scroll performance optimizations */
* {
    /* Prevent layout thrashing during scroll */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Optimize elements that commonly animate during scroll */
.slide-in,
.zoom-in,
.animated {
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000px;
    -webkit-perspective: 1000px;
}

/* Optimize images for scroll performance */
img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: optimize-contrast;
    -ms-interpolation-mode: bicubic;
}

/* Optimize containers for scroll and prevent double scroll bars */
.container,
.hero-container,
.services-container,
.testimonials-container,
.why-how-container,
.why-choose-container {
    contain: layout style;
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
    box-sizing: border-box;
}

/* Global fix for double scroll bar issue */
* {
    box-sizing: border-box;
}

*:not(html):not(body) {
    overflow-x: hidden;
}

/* Custom post type archive styling */
.archive-service .service-grid,
.archive-testimonial .testimonial-grid,
.archive-job .job-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.archive-service .service-card,
.archive-testimonial .testimonial-card,
.archive-job .job-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.archive-service .service-card:hover,
.archive-testimonial .testimonial-card:hover,
.archive-job .job-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Job specific styling */
.job-meta {
    margin: 1rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 5px;
}

.job-location,
.job-apply {
    margin-bottom: 0.5rem;
}

.job-apply-btn {
    display: inline-block;
    background: #0078d4;
    color: white;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.job-apply-btn:hover {
    background: #106ebe;
    color: white;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .archive-service .service-grid,
    .archive-testimonial .testimonial-grid,
    .archive-job .job-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .archive-service .service-card,
    .archive-testimonial .testimonial-card,
    .archive-job .job-card {
        padding: 1.5rem;
    }
}

@import url('https://fonts.googleapis.com/css?family=Maven+Pro:400,700&display=swap');

/* Custom full-width article style for 'Harnessing the Power of Videos in Medical Communications' */
.custom-article-header-fullwidth, .entry-content-fullwidth {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    background: #fff;
}
.custom-article-header-fullwidth { margin-bottom: 40px; }
.custom-article-header-fullwidth .custom-meta-bar {
    text-align: center;
    margin-bottom: 32px;
    padding-top: 32px;
    font-size: 1.05rem;
    color: #525252;
    letter-spacing: 0.08em;
    font-family: 'Maven Pro', Arial, sans-serif;
}
.custom-article-header-fullwidth .custom-meta-bar span {
    margin: 0 12px;
}
.custom-article-header-flex {
    display: flex;
    align-items: flex-start;
    gap: 40px;
    width: 100%;
    padding: 0 32px 0px 32px;
    box-sizing: border-box;
}
.custom-article-header-flex .custom-title-col {
    flex: 0 0 65%;
    max-width: 65%;
}
.custom-article-header-flex .custom-title-col h1 {
    font-size: 3.2rem;
    color: #0072DA;
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 0.5em;
    font-family: 'Maven Pro', Arial, sans-serif;
    text-align: left;
}
.custom-article-header-flex .custom-image-col {
    flex: 0 0 30%;
    max-width: 35%;
    text-align: right;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
}
.custom-article-header-flex .post-thumbnail {
    max-width: 420px;
    margin-left: auto;
}
.custom-article-header-flex img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.08);
}
.entry-content-fullwidth { width:100vw; max-width:100vw; }
.entry-content-fullwidth .custom-content-wrap {
    width: 100vw;
    padding: 0 32px 40px 32px;
    box-sizing: border-box;
}
.entry-content-fullwidth p {
    font-size: 1.45rem;
    font-family: Georgia, serif;
    color: #525252;
    margin-bottom: 1.7em;
}
.entry-content-fullwidth p:not(:nth-of-type(1)):not(:nth-of-type(2)) {
    font-size: 1.45rem;
    font-family: inherit;
    color: #525252;
    margin-bottom: 1.2em;
}
.entry-content-fullwidth h2, .entry-content-fullwidth h3 {
    color: #0072DA;
    font-family: 'Maven Pro', Arial, sans-serif;
    font-weight: 700;
    margin-top: 2.5em;
    margin-bottom: 1em;
}
.entry-content-fullwidth h2 {
    font-size: 2.4rem;
}
.entry-content-fullwidth h3 {
    font-size: 1.45rem;
}
@media (max-width: 900px) {
    .custom-article-header-flex { flex-direction: column; gap: 24px; }
    .custom-article-header-flex .custom-title-col, .custom-article-header-flex .custom-image-col { max-width: 100%; flex: 1 1 100%; }
    .custom-article-header-flex .custom-title-col h1 { text-align: center; }
    .custom-article-header-flex .custom-image-col { justify-content: center; text-align: center; }
}
@media (max-width: 600px) {
    .custom-article-header-fullwidth, .entry-content-fullwidth, .entry-content-fullwidth .custom-content-wrap { padding: 0 8px !important; }
    .custom-article-header-flex { padding: 0 8px 24px 8px; }
}
body.single-post .container,
body.single-post .content-area {
    max-width: 100vw !important;
    width: 100vw !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: visible !important;
}
body.single-post .content-area {
    display: block !important;
}
body, html {
    overflow-x: hidden !important;
}

/* Custom styled list for article content */
.entry-content-fullwidth ul {
    margin: 2em 0 2em 2.5em;
    padding: 0;
    list-style-type: disc;
}

.entry-content-fullwidth ul li {
    font-family: Georgia, serif;
    font-size: 1.45rem;
    color: #525252;
    margin-bottom: 1rem;
    line-height: 1.5;
    letter-spacing: 0.01em;
}

.entry-content-fullwidth ul li strong {
    font-family: 'Maven Pro', Arial, sans-serif;
    font-size: 24px;
    font-weight: 500;
    color: #525252;
}

.entry-content-fullwidth ul li,
.entry-content-fullwidth p {
    font-size: 1.45rem;
}