# Krystelis Custom WordPress Theme

A custom WordPress theme replicating krystelis.com, featuring custom post types, ACF compatibility, and responsive design.

## Description

Krystelis Custom Theme is a comprehensive WordPress theme built specifically to replicate the krystelis.com website. It features custom post types for services, testimonials, and jobs, with full ACF compatibility and a modern, responsive design.

## Features

- **Lightweight & Fast**: Minimal code and optimized for performance
- **Responsive Design**: Looks great on all devices
- **Accessibility Ready**: Built with accessibility best practices
- **SEO Optimized**: Clean, semantic HTML structure
- **Customizable**: Easy to modify and extend
- **Widget Ready**: Sidebar and footer widget areas
- **Menu Support**: Primary and footer navigation menus
- **Custom Logo Support**: Upload your own logo
- **Translation Ready**: Ready for internationalization

## Installation

1. Download the theme files
2. Upload to `/wp-content/themes/micro-theme/` directory
3. Activate the theme through the 'Appearance > Themes' menu in WordPress
4. Customize through 'Appearance > Customize'

## Theme Structure

```
micro-theme/
├── style.css           # Main stylesheet with theme header
├── index.php           # Main template file
├── functions.php       # Theme functions and features
├── header.php          # Header template
├── footer.php          # Footer template
├── sidebar.php         # Sidebar template
├── single.php          # Single post template
├── page.php            # Static page template
├── archive.php         # Archive pages template
├── search.php          # Search results template
├── 404.php             # 404 error page template
├── comments.php        # Comments template
├── searchform.php      # Search form template
├── js/
│   └── navigation.js   # Navigation JavaScript
└── README.md           # This file
```

## Customization

### Menus
The theme supports two menu locations:
- **Primary Menu**: Main navigation in the header
- **Footer Menu**: Links in the footer

### Widget Areas
- **Sidebar**: Main sidebar widget area
- **Footer**: Footer widget area

### Custom Logo
Upload your logo through Appearance > Customize > Site Identity

### Colors & Styling
Modify the `style.css` file to customize colors, fonts, and layout.

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Changelog

### Version 1.0.0
- Initial release
- Basic theme structure
- Responsive design
- Widget support
- Menu support
- Custom logo support

## Support

For support and questions, please create an issue in the theme repository.

## License

This theme is licensed under the GPL v2 or later.

## Credits

- Built with WordPress best practices
- Uses modern CSS Grid and Flexbox
- Follows WordPress Coding Standards
#   k r y s t e l i s - c u s t o m - t h e m e 
 
 