<?php
/**
 * Template part for displaying CTA (Call to Action) section
 *
 * @package Krystelis_Custom
 */
?>

<section class="cta-section">
    <div class="cta-background">
        <div class="container">
            <div class="cta-content">
                <p class="cta-subtitle">INTERESTED IN LEARNING MORE?</p>
                <h2 class="cta-title">Get In Touch</h2>
                <a href="<?php echo esc_url(home_url('/contact')); ?>" class="cta-button">CONTACT US</a>
            </div>
        </div>
    </div>
</section>

<style>
.cta-section {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 4rem 1rem;
    opacity: 0;
    transform: translateY(120px);
}

.cta-section.cta-visible {
    opacity: 1;
    animation: cta-bounce-in 1.2s cubic-bezier(0.23, 1, 0.32, 1) both;
}

.cta-background {
    max-width: 1000px;       /* Restricts the max width */
    margin: 0 auto;           /* Centers it horizontally */
    width: 70%;              /* Responsive for smaller screens */
    padding: 7rem 2rem;       /* Maintain inner spacing */
    border-radius: 20px;
    background: linear-gradient(180deg, #39A1FF 0%, #0072DA 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}


.cta-background::before {
    content: '';
    position: absolute;
    
    
}

.cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    margin: 0 auto;
}

.cta-subtitle {
    white-space: nowrap;         /* Prevent line break */
    overflow: hidden;            /* Hide overflow just in case */
    text-overflow: ellipsis;     /* Optional: add "..." if it gets cut */
    font-size: 1.4rem;
    font-family: 'Maven Pro', sans-serif;
    font-weight: 400;
    letter-spacing: 10px;
    text-transform: uppercase;
    margin-bottom: 1rem;
    font-family: 'Georgia', serif;
    opacity: 0.95;
}

.cta-title {
    font-size: 3.4rem;
    font-weight: 600;
    margin-bottom: 4rem;
    font-family: 'Maven Pro', sans-serif;
    color: white;
    line-height: 1.2;
}

.cta-button {
    display: inline-block;
    background: white;
    color:rgb(71, 67, 67);
    padding: 1.5rem 2rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.3rem;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 20px 0072DA;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 10px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}

.cta-button:hover {
    background: #ffffff;
    color: #0072DA;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 136, 255, 0.4); /* Brighter effect */
    text-decoration: none;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 20px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .cta-background {
        width: 100%;
        padding: 3rem 0;
        min-height: 250px;
        margin: 20px 0;
        border-radius: 10px;
    }

    .cta-title {
        font-size: 2.5rem;
    }

    .cta-subtitle {
        font-size: 1rem;
        overflow: hidden;
        text-overflow: ellipsis;
        font-family: 'Maven Pro', sans-serif;
        font-weight: 400;
        letter-spacing: 6px;
        text-transform: uppercase;
        margin-bottom: 1rem;
        font-family: 'Georgia', serif;
        opacity: 0.95;
    }

    .cta-button {
        padding: 0.8rem 2rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .cta-background {
        width: 100%;
        padding: 2.5rem 0;
        min-height: 200px;
        margin: 10px 0;
        border-radius: 10px;
    }

    .cta-title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    .cta-subtitle {
        margin-bottom: 0.8rem;
    }
}

/* Ensure full-width layout with minimal margins as per user preferences */
.cta-section .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2px; /* Minimal 2px gaps from both sides as preferred */
}

@media (min-width: 1200px) {
    .cta-section .container {
        max-width: 1400px; /* Allow content to stretch more on larger screens */
        padding: 0 2px;
    }
}

/* Ultra-wide screens (2560px and above) */
@media (min-width: 2560px) {
  .cta-background {
    max-width: 2200px;
    padding: 7rem 2rem;
  }
  .cta-title {
    font-size: 4.5rem;
  }
  .cta-subtitle {
    font-size: 2rem;
    letter-spacing: 16px;
  }
  .cta-button {
    font-size: 2rem;
    padding: 2rem 4rem;
  }
}

/* Super ultra-wide screens (3425px and above) */
@media (min-width: 3425px) {
  .cta-background {
    max-width: 3000px;
    padding: 10rem 4rem;
  }
  .cta-title {
    font-size: 6rem;
  }
  .cta-subtitle {
    font-size: 2.5rem;
    letter-spacing: 20px;
  }
  .cta-button {
    font-size: 2.5rem;
    padding: 2.5rem 5rem;
  }
}

/* Large desktop (1024px - 2559px) */
@media (min-width: 1024px) and (max-width: 2559px) {
  .cta-background {
    max-width: 1200px;
    padding: 5rem 2rem;
  }
  .cta-title {
    font-size: 3.4rem;
  }
  .cta-subtitle {
    font-size: 1.4rem;
    letter-spacing: 10px;
  }
  .cta-button {
    font-size: 1.3rem;
    padding: 1.5rem 2rem;
  }
}

/* Tablet landscape (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .cta-background {
    max-width: 900px;
    padding: 3.5rem 1rem;
  }
  .cta-title {
    font-size: 2.2rem;
  }
  .cta-subtitle {
    font-size: 1.1rem;
    letter-spacing: 6px;
  }
  .cta-button {
    font-size: 1rem;
    padding: 1rem 2rem;
  }
}

/* Small tablet and large mobile (375px - 767px) */
@media (min-width: 375px) and (max-width: 767px) {
  .cta-background {
    max-width: 100%;
    padding: 2.5rem 0.5rem;
  }
  .cta-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  .cta-subtitle {
    font-size: 0.9rem;
    letter-spacing: 4px;
  }
  .cta-button {
    font-size: 0.9rem;
    padding: 0.8rem 1.5rem;
  }
}

/* Extra small mobile (320px - 374px) */
@media (min-width: 320px) and (max-width: 374px) {
  .cta-background {
    max-width: 100%;
    padding: 2rem 0.2rem;
  }
  .cta-title {
    font-size: 1.1rem;
    margin-bottom: 0.7rem;
  }
  .cta-subtitle {
    font-size: 0.7rem;
    letter-spacing: 2px;
  }
  .cta-button {
    font-size: 0.8rem;
    padding: 0.6rem 1rem;
  }
}

/* Prevent horizontal scroll on all widths */
html, body, .cta-section, .cta-background, .container, .cta-content {
  max-width: 100vw !important;
  overflow-x: hidden !important;
  box-sizing: border-box;
}

@media (max-width: 1024px) {
  .services-section,
  .cta-section {
    padding-left: 50px !important;
    padding-right: 50px !important;

  }
}

@media (min-width: 320px) and (max-width: 479px) {
    .services-section, .cta-section {
        padding-left: 0px !important;
        padding-right: 0px !important;
        
    }
}

@keyframes cta-bounce-in {
    0% {
        opacity: 0;
        transform: translateY(120px);
    }
    60% {
        opacity: 1;
        transform: translateY(-20px);
    }
    80% {
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
document.addEventListener("DOMContentLoaded", function() {
    var ctaSection = document.querySelector('.cta-section');
    if (!ctaSection) return;

    function onEntry(entries, observer) {
        entries.forEach(function(entry) {
            if (entry.isIntersecting) {
                ctaSection.classList.add('cta-visible');
                observer.unobserve(ctaSection);
            }
        });
    }

    var observer = new IntersectionObserver(onEntry, {
        threshold: 0.2 // Adjust as needed
    });

    observer.observe(ctaSection);
});
</script>