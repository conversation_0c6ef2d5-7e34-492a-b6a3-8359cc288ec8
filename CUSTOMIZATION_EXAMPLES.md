# Easy Customization Examples

## 🎨 Quick Content Changes

### 1. Change Hero Section Text

**Current hero text** (lines 13-16 in `template-parts/hero.php`):

<augment_code_snippet path="template-parts/hero.php" mode="EXCERPT">
````php
$hero_title = krystelis_get_field('hero_title', get_option('page_on_front'), 'Making clinical research crystal clear');
$hero_subtitle = krystelis_get_field('hero_subtitle', get_option('page_on_front'), 'Great people are at the heart of the most valuable partnerships – our exceptional team stands ready to support you');
$hero_cta_text = krystelis_get_field('hero_cta_text', get_option('page_on_front'), 'READ MORE');
$hero_cta_url = krystelis_get_field('hero_cta_url', get_option('page_on_front'), '#services');
````
</augment_code_snippet>

**To change the text**, replace the default values (text in quotes):

```php
// Change this:
'Making clinical research crystal clear'
// To your title:
'Your Company Name - Your Tagline'

// Change this:
'Great people are at the heart...'
// To your subtitle:
'Your company description and value proposition'

// Change this:
'READ MORE'
// To your button text:
'GET STARTED' or 'CONTACT US'
```

### 2. Add Your Background Image

**Option A: Replace the default image**
1. Save your image as `krystelis-hero-bg.jpg`
2. Upload to `/assets/images/krystelis-hero-bg.jpg`
3. Image will automatically appear

**Option B: Change the image name**
Edit line 10 in `template-parts/hero.php`:
```php
// Change this line:
$hero_bg_url = $hero_bg_image ? $hero_bg_image['url'] : get_template_directory_uri() . '/assets/images/krystelis-hero-bg.jpg';

// To use your image name:
$hero_bg_url = $hero_bg_image ? $hero_bg_image['url'] : get_template_directory_uri() . '/assets/images/YOUR-IMAGE-NAME.jpg';
```

### 3. Change Colors

**Hero button color** (line 121 in `template-parts/hero.php`):
```css
.btn-orange {
    background-color: #ff6b35; /* Change this color */
    color: white;
}
```

**Popular color options:**
- Blue: `#0078d4`
- Green: `#28a745`
- Purple: `#6f42c1`
- Red: `#dc3545`

### 4. Update Contact Information

**In `page-contact.php`**, find and update:

```php
// Phone number (around line 45)
<p>+****************</p>

// Email (around line 55)
<p><EMAIL></p>

// Address (around line 65)
<p>123 Research Drive<br>Clinical City, CC 12345<br>United States</p>

// Business hours (around line 75)
<p>Monday - Friday: 8:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 2:00 PM<br>Sunday: Closed</p>
```

### 5. Change Company Name Throughout

**Find and replace "Krystelis" with your company name in these files:**
- `template-parts/hero.php`
- `page-about.php`
- `page-contact.php`
- `header.php` (logo alt text)

---

## 📝 Content Examples

### Sample Services to Create

**Service 1: Clinical Trial Management**
- Title: Clinical Trial Management
- Excerpt: Comprehensive oversight and management of clinical trials from protocol development through study completion.
- Content: [Detailed description of your clinical trial management services]

**Service 2: Data Analysis**
- Title: Data Analysis & Reporting
- Excerpt: Expert data management and statistical analysis services to ensure data integrity and meaningful insights.
- Content: [Detailed description of your data analysis services]

**Service 3: Regulatory Affairs**
- Title: Regulatory Consulting
- Excerpt: Navigate complex regulatory requirements with our expert guidance and submission support.
- Content: [Detailed description of your regulatory services]

### Sample Testimonials

**Testimonial 1:**
- Title: Dr. Sarah Johnson
- Content: "Krystelis provided exceptional support throughout our Phase II trial. Their attention to detail and regulatory expertise was invaluable."
- Company: BioPharma Solutions
- Position: Clinical Development Director

**Testimonial 2:**
- Title: Michael Chen
- Content: "The team's data management capabilities and statistical analysis helped us achieve our study endpoints efficiently."
- Company: MedTech Innovations
- Position: VP of Clinical Research

---

## 🚀 Quick Setup Steps

### 1. Customize Hero Section
1. Edit `template-parts/hero.php` lines 13-16
2. Add your background image to `/assets/images/`
3. Update company name and tagline

### 2. Create Your Pages
1. WordPress Admin → Pages → Add New
2. Create: About, Services, Contact
3. Set up navigation menu

### 3. Add Your Content
1. Create 4-6 services
2. Add 2-3 testimonials
3. Update contact information

### 4. Upload Your Images
1. Logo: Replace `/assets/images/krystelis-logo.png`
2. Hero background: Add to `/assets/images/`
3. Service icons: 64x64px PNG files
4. Testimonial photos: Square format

### 5. Test Everything
1. Check homepage layout
2. Test navigation menu
3. Verify contact form
4. Test on mobile devices

---

## 🎯 Pro Tips

### Easy Text Changes
- Most text can be changed by editing the default values in quotes
- Look for lines with `krystelis_get_field()` function
- The third parameter is the default text you can change

### Image Sizes
- **Hero background**: 1920x1080px or larger
- **Logo**: PNG with transparent background
- **Service icons**: 64x64px
- **Testimonial photos**: Square format (200x200px)

### Color Consistency
- Use the same colors throughout your site
- Main colors: Primary, Secondary, Accent
- Test color contrast for accessibility

### Mobile Testing
- Always test on mobile devices
- Check that text is readable
- Ensure buttons are easily clickable

**Need help with any specific customization? Just ask!** 🤝
