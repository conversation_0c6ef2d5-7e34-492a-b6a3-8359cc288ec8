/**
 * Custom JavaScript for Krystelis Theme
 *
 * @package Krystelis_Custom
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {

        // Initialize testimonials slider
        initTestimonialsSlider();

        // Smooth scrolling for anchor links
        initSmoothScrolling();

        // Contact form enhancements
        initContactForm();

        // Initialize menu click functionality
        // initMenuClickHandling();

    });

    /**
     * Initialize testimonials slider
     */
    function initTestimonialsSlider() {
        var slider = $('#testimonials-slider');
        if (slider.length === 0) return;

        var slides = slider.find('.testimonial-slide');
        var currentSlide = 0;
        var totalSlides = slides.length;
        
        if (totalSlides <= 1) return;

        // Create dots
        var dotsContainer = slider.find('#slider-dots');
        for (var i = 0; i < totalSlides; i++) {
            dotsContainer.append('<span class="dot" data-slide="' + i + '"></span>');
        }
        
        var dots = dotsContainer.find('.dot');
        
        // Show first slide
        showSlide(0);
        
        // Next button
        slider.find('#next-btn').on('click', function() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        });
        
        // Previous button
        slider.find('#prev-btn').on('click', function() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            showSlide(currentSlide);
        });
        
        // Dot navigation
        dots.on('click', function() {
            currentSlide = parseInt($(this).data('slide'));
            showSlide(currentSlide);
        });
        
        // Auto-play (optional)
        setInterval(function() {
            currentSlide = (currentSlide + 1) % totalSlides;
            showSlide(currentSlide);
        }, 5000);
        
        function showSlide(index) {
            slides.removeClass('active');
            dots.removeClass('active');
            
            slides.eq(index).addClass('active');
            dots.eq(index).addClass('active');
        }
    }

    /**
     * Initialize smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        $('a[href*="#"]:not([href="#"])').on('click', function() {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && 
                location.hostname === this.hostname) {
                
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 80
                    }, 1000);
                    return false;
                }
            }
        });
    }

    /**
     * Initialize contact form enhancements
     */
    function initContactForm() {
        var form = $('.contact-form');
        if (form.length === 0) return;

        // Add loading state
        form.on('submit', function() {
            var submitBtn = form.find('button[type="submit"]');
            var originalText = submitBtn.text();
            
            submitBtn.text('Sending...').prop('disabled', true);
            
            // Re-enable after a delay (in case of errors)
            setTimeout(function() {
                submitBtn.text(originalText).prop('disabled', false);
            }, 5000);
        });

        // Form validation enhancements
        form.find('input, textarea').on('blur', function() {
            validateField($(this));
        });

        function validateField(field) {
            var value = field.val().trim();
            var fieldType = field.attr('type');
            var isValid = true;
            var errorMessage = '';

            // Remove existing error styling
            field.removeClass('error');
            field.next('.error-message').remove();

            // Required field validation
            if (field.prop('required') && value === '') {
                isValid = false;
                errorMessage = 'This field is required.';
            }

            // Email validation
            if (fieldType === 'email' && value !== '') {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address.';
                }
            }

            // Show error if invalid
            if (!isValid) {
                field.addClass('error');
                field.after('<span class="error-message">' + errorMessage + '</span>');
            }

            return isValid;
        }
    }

    /**
     * Initialize animations on scroll (optional)
     */
    function initScrollAnimations() {
        var animatedElements = $('.animate-on-scroll');

        if (animatedElements.length === 0) return;

        $(window).on('scroll', function() {
            animatedElements.each(function() {
                var element = $(this);
                var elementTop = element.offset().top;
                var windowBottom = $(window).scrollTop() + $(window).height();

                if (elementTop < windowBottom - 100) {
                    element.addClass('animated');
                }
            });
        });
    }

    // Remove the entire initMenuClickHandling function to prevent conflicts
    // function initMenuClickHandling() { ... }

})(jQuery);
