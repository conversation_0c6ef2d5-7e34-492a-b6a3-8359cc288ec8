<?php
/**
 * The template for displaying single webinar posts
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <div class="content-area">
            <div class="main-content">
                <?php while (have_posts()) : the_post(); ?>
                    <?php
                    // Webinar fields from custom meta box
                    $speaker = get_post_meta(get_the_ID(), '_webinar_speaker', true);
                    $webinar_date_raw = get_post_meta(get_the_ID(), '_webinar_date', true);
                    $time_from = get_post_meta(get_the_ID(), '_webinar_time_from', true);
                    $time_to = get_post_meta(get_the_ID(), '_webinar_time_to', true);
                    $time_zone = get_post_meta(get_the_ID(), '_webinar_time_zone', true);
                    $registration_link = get_post_meta(get_the_ID(), '_webinar_link', true);
                    // The following fields are left empty as they are not handled by the meta box
                    $webinar_heading = '';
                    $webinar_subheading = '';
                    $webinar_h3_link_text = '';
                    $webinar_h3_link_url = '';

                    // Format date
                    $webinar_date = '';
                    if ($webinar_date_raw) {
                        $timestamp = strtotime($webinar_date_raw);
                        if ($timestamp) {
                            $webinar_date = date_i18n('F j, Y', $timestamp);
                        }
                    }

                    // Format time
                    $webinar_time = '';
                    if ($time_from && $time_to) {
                        // Convert to 12-hour format
                        $time_from_formatted = date('g:i', strtotime($time_from));
                        $time_to_formatted = date('g:i', strtotime($time_to));
                        $webinar_time = $time_from_formatted . ' - ' . $time_to_formatted;
                        if ($time_zone) {
                            $webinar_time .= ' ' . esc_html($time_zone);
                        }
                    } elseif ($time_from) {
                        $time_from_formatted = date('g:i', strtotime($time_from));
                        $webinar_time = $time_from_formatted;
                        if ($time_zone) {
                            $webinar_time .= ' ' . esc_html($time_zone);
                        }
                    }
                    ?>
                    
                    <article id="post-<?php the_ID(); ?>" <?php post_class('webinar-single'); ?> >
                        <div class="webinar-header-3col-fixed">
                            <div class="webinar-header-col left">
                                <div class="webinar-header-label">SPEAKERS</div>
                                <div class="webinar-header-value webinar-speakers">
                                    <?php echo esc_html($speaker); ?>
                                </div>
                            </div>
                            <div class="webinar-header-col center">
                                <div class="webinar-header-label">DATE</div>
                                <div class="webinar-header-value webinar-date">
                                    <?php echo esc_html($webinar_date); ?>
                                </div>
                            </div>
                            <div class="webinar-header-col right">
                                <div class="webinar-header-label">TIME</div>
                                <div class="webinar-header-value webinar-time">
                                    <?php echo esc_html($webinar_time); ?>
                                </div>
                            </div>
                        </div>
                        <hr class="webinar-divider-fixed" />

                        <?php if ($webinar_heading) : ?>
                            <h2 class="webinar-heading-main-fixed"><?php echo esc_html($webinar_heading); ?></h2>
                        <?php endif; ?>

                        <?php if ($webinar_subheading) : ?>
                            <p class="webinar-subheading-main-fixed"><?php echo esc_html($webinar_subheading); ?></p>
                        <?php endif; ?>

                        <?php if ($webinar_h3_link_text && $webinar_h3_link_url) : ?>
                            <h3 class="webinar-link-main-fixed">
                                <a href="<?php echo esc_url($webinar_h3_link_url); ?>">
                                    <?php echo esc_html($webinar_h3_link_text); ?>
                                </a>
                            </h3>
                        <?php endif; ?>

                        <h1 class="webinar-title-main-fixed"><?php the_title(); ?></h1>

                        <?php 
                        // Show registration block above image if image exists
                        if (has_post_thumbnail() && $registration_link) : ?>
                            <div class="webinar-registration-block-fixed">
                                <div class="wp-block-heading">REGISTRATION</div>
                                <div class="wp-block-heading">
                                    <a href="<?php echo esc_url($registration_link); ?>" class="wp-block-headingg" target="_blank" rel="noopener">
                                        Click here to register
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php 
                        // Show featured image if exists
                        if (has_post_thumbnail()) : ?>
                            <div class="webinar-featured-image">
                                <?php the_post_thumbnail('large'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="webinar-content-main-fixed">
                            <?php
                            $content = get_the_content();

                            // Registration block HTML
                            $registration_html = '';
                            if ($registration_link) {
                                $registration_html = '<div class="webinar-registration-block-fixed">
                                    <div class="wp-block-heading">REGISTRATION</div>
                                    <div class="wp-block-heading">
                                        <a href="' . esc_url($registration_link) . '" class="wp-block-headingg" target="_blank" rel="noopener">
                                            Click here to register
                                        </a>
                                    </div>
                                </div>';
                            }

                            // Insert registration block before first <img> tag
                            if ($registration_html && preg_match('/<img[^>]*>/', $content)) {
                                $content = preg_replace('/(<img[^>]*>)/', $registration_html . '$1', $content, 1);
                            }

                            // Output the content (with registration block inserted if image found)
                            echo apply_filters('the_content', $content);

                            ?>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>
        </div>
    </div>
</main>

<style>
.webinar-header-3col-fixed {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    align-items: flex-start;
    margin-bottom: 1.2rem;
    margin-top: 2.5rem;
    width: 100%;
}
.webinar-header-col.left {
    text-align: left;
}
.webinar-header-col.center {
    text-align: center;
}
.webinar-header-col.right {
    text-align: right;
}
.webinar-header-label {
    color: #0072DA;
    font-family: "Georgia", sans-serif;
    font-size: 1.5rem;
    font-weight: 300;
    letter-spacing: 10px;
    text-transform: uppercase;
    margin-bottom: 0.2rem;
}
.webinar-header-value {
    font-family: "Maven Pro", sans-serif;
    font-size: 1.35rem;
    font-weight: 700;
    color: #ff6a18;
    word-break: break-word;
}
.webinar-speakers {
    color: #ff6a18;
}
.webinar-date {
    color: #ff6a18;
}
.webinar-time {
    color: #ff6a18;
}
.webinar-divider-fixed {
    border: none;
    border-top: 2px solid #bdbdbd;
    margin: 1.5rem 0 2.2rem 0;
    width: 100%;
}
.webinar-title-main-fixed {
    font-family: "Maven Pro", sans-serif;
    font-size: 3.375rem;
    font-weight: 600;
    color: #0072DA;
    margin-bottom: 1.5rem;
    margin-top: 0;
    line-height: 60px;
    text-align: left;
}
.webinar-content-main-fixed {
    font-family: Georgia, serif;
    font-size: 1.25rem;
    color: #525252;
    margin-bottom: 2.2rem;
    text-align: left;
}
.wp-block-heading {
    margin-top: 2.5rem;
    margin-bottom: 2.5rem;
    padding: 0;
}
.webinar-registration-block-fixed {
    margin-top: 2.5rem;
    margin-bottom: 2.5rem;
    padding: 0;
}

.wp-block-heading{
    color: #0072DA;
    font-family: "Georgia", serif;
    font-size: 1.3rem;
    font-weight: 400;
    letter-spacing: 10px;
    text-transform: uppercase;
    margin-left: 4px;
    text-align: left;
    padding-left: 2px;
    display: block;
    background: none;
    box-shadow: none;
    border-radius: 0;
    padding: 0;
}

.webinar-registration-block-fixed .wp-block-heading:last-child {
    margin: 0;
    padding: 0;
    background: none;
    box-shadow: none;
    border-radius: 0;
}

.wp-block-headingg {
    display: block;
    width: 100%;
    background: linear-gradient(90deg, #ff7f3f 0%, #ff6b2b 100%);
    color: #fff;
    font-family: "Maven Pro", sans-serif;
    font-size: 1.5rem;
    font-weight: 800;
    padding: 6px 2rem;
    text-align: left;
    text-decoration: none;
    letter-spacing: 0.04em;
    border-radius: 6px;
    border: none;
    box-shadow: 0 4px 18px rgba(255,127,63,0.12);
    transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
    cursor: pointer;
    outline: none;
    margin: 0;
}
.wp-block-headingg:hover,
.wp-block-headingg:focus {
    background: linear-gradient(90deg, #ff6b2b 0%, #ff7f3f 100%);
    color: #fff;
    text-decoration: underline;
    box-shadow: 0 6px 24px rgba(255,107,43,0.18);
    transform: translateY(-2px) scale(1.02);
}
@media (max-width: 900px) {
    .webinar-header-3col-fixed {
        grid-template-columns: 1fr;
        gap: 1.2rem;
        text-align: left;
    }
    .webinar-header-col.left,
    .webinar-header-col.center,
    .webinar-header-col.right {
        text-align: left;
    }
    .webinar-title-main-fixed {
        font-size: 2rem;
    }
}

/* Add spacing above images in content */
.webinar-content-main-fixed .wp-block-image {
    margin-top: 60px;
}
.webinar-content-main-fixed figure {
    margin-top: 60px;
}
.webinar-content-main-fixed img {
    margin-top: 60px;
}
@media (min-width: 1024px) {
    .container {
        width: 100%;
        padding: 0 40px;
    }
}
</style>

<?php get_footer(); ?> 
