<?php
/**
 * Template for Careers Page
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <?php while (have_posts()) : the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            <div class="entry-content">
                <?php the_content(); ?>

                <!-- Top Section -->
                <section class="top-section">
                    <h4 class="top-subtitle">Making clinical research crystal clear</h4>
                </section>

                <!-- Careers Hero Section -->
                <section class="careers-hero">
                    <div class="careers-hero__content">
                        <div class="careers-hero__text">
                            <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                            <p class="careers-hero__paragraph">
                                There are a million ways to earn a living, but we all want to do work that has a purpose, excites us, with people we like, and where we can grow as individuals.
                            </p>
                            <h3 class="careers-hero__highlight">Behind every great employee is a great team.</h3>
                            <p class="careers-hero__paragraph">
                             We help our clients improve the lives of patients by providing services that are recognised for quality, value, and collaboration. We are a team that believes passionately in our mission and strives every day to achieve it. Challenges that come our way are opportunities to learn and improve.
                                   <br> 
                                   <br>
                             Krystelis is a workplace where you can learn, make a difference, and grow, regardless of where you are on your career journey.                            </p> </br>
                                   </br>
                        </div>
                        <div class="careers-hero__image">
                            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/careers-hero.png" alt="careers-hero">
                        </div>
                    </div>
                </section>

                <!-- Company Values Section -->
                <section class="company-values" id="company-value">
                    <h2 class="company-values__heading">Our company values guide how we work with<br> each other and our clients</h2>
                </section>

                <!-- Mobile Values Section - Visible only on mobile -->
<div class="mobile-values-section">
    <?php
    $values = [
        [
            'icon' => '<svg class="icon" viewBox="0 0 90 90"><path d="M84.2393 45.604C78.2873 43.7563 72.3489 41.8677 66.3878 40.0495C65.736 39.852 64.9213 39.8543 64.2695 40.0518C58.9534 41.6679 53.66 43.3522 48.3598 45.0138C48.1199 45.0887 47.8755 45.1432 47.6039 45.2158C47.3912 44.41 47.536 43.8607 48.2466 43.3273C50.8899 41.3411 52.7593 38.7488 53.8411 35.6141C54.0515 35.0034 54.3231 34.8128 54.9704 34.8105C58.6208 34.7923 61.4338 32.3567 61.9724 28.8179C62.5767 24.8524 59.5622 21.1251 55.5656 20.8959C55.346 20.8823 55.1288 20.8618 54.821 20.8369C54.821 19.8426 54.8255 18.887 54.821 17.9314C54.7667 9.95258 49.5683 3.34256 41.8533 1.44944C41.1472 1.27693 40.4298 1.14981 39.7169 1H36.3268C35.3966 1.20656 34.4529 1.37 33.5341 1.62423C26.3011 3.63538 21.3064 10.1614 21.225 17.6862C21.2136 18.7258 21.225 19.7655 21.225 20.7688C20.4012 20.9118 19.6657 20.9708 18.9709 21.1728C15.7459 22.1103 13.5869 25.3722 14.026 28.5818C14.5239 32.2183 17.3075 34.7628 20.9262 34.8037C21.6572 34.8128 22.0012 34.9853 22.2434 35.7139C23.0128 38.0338 24.3254 40.0381 26.0318 41.7837C26.8217 42.5918 27.9487 43.266 28.3221 44.233C28.7204 45.268 28.4126 46.5801 28.4126 47.7695C28.4126 47.7967 28.4081 47.8262 28.4126 47.8535C28.4805 48.3483 28.2565 48.5345 27.7699 48.6298C23.1418 49.5241 20.8267 50.2891 18.7106 51.3446C13.5733 53.9051 4.01167 60.0611 4.00261 68.9933C4.00035 72.3936 3.99809 75.7962 4.00261 79.1966C4.00261 80.6493 4.61818 81.2667 6.06432 81.269C9.08785 81.2736 12.1114 81.269 15.1349 81.269C16.8866 81.269 27.1045 81.2758 32.828 81.269C32.8913 81.269 32.9547 81.269 33.0181 81.269C33.9595 81.269 34.7697 81.269 35.3898 81.2667C40.7399 81.2667 46.0921 81.2622 51.4422 81.2872C51.8382 81.2872 52.3225 81.4824 52.6167 81.7502C55.9163 84.7829 59.7365 86.819 64.0885 87.8382C64.7018 87.9812 65.3965 88.0697 65.9963 87.929C75.6711 85.659 82.0146 79.7368 84.9906 70.244C85.4931 68.6414 85.6719 66.9367 86 65.282V46.9137C85.6198 46.1942 84.9974 45.8355 84.237 45.5994L84.2393 45.604ZM54.8617 24.3008C56.3531 24.2463 57.4507 24.8319 58.1659 26.0509C58.9037 27.3084 58.8584 28.5977 58.055 29.8189C57.3149 30.9448 56.2354 31.4215 54.8617 31.426V24.3008ZM21.1797 31.4034C19.8331 31.4601 18.7853 30.9698 18.0385 29.9075C17.2011 28.718 17.1106 27.4242 17.8167 26.1439C18.5183 24.8728 19.6227 24.2531 21.1797 24.3008V31.4011V31.4034ZM25.0768 14.304C26.8013 8.1775 32.355 4.14384 38.7415 4.41623C44.7705 4.67501 50.0073 9.25798 51.2 15.3051C51.3064 15.843 51.2272 16.2562 50.8334 16.6874C47.5903 20.2467 43.5665 22.108 38.7777 22.1829C34.2289 22.2533 29.68 22.2011 25.1311 22.2011C24.9953 22.2011 24.8573 22.1784 24.597 22.158C24.7034 19.4772 24.3549 16.869 25.0768 14.304ZM24.9116 32.6654C24.4771 30.4159 24.6174 28.0552 24.4861 25.606C25.0813 25.606 25.4434 25.606 25.8055 25.606C30.4924 25.5833 35.1816 25.6491 39.864 25.5129C43.9014 25.3949 47.4975 23.8944 50.7021 21.4384C50.8809 21.3022 51.0642 21.1705 51.2475 21.0389C51.2634 21.0275 51.2973 21.0457 51.3878 21.0639C51.3992 21.2886 51.424 21.5292 51.424 21.7721C51.4263 24.4914 51.4376 27.2108 51.424 29.9279C51.3833 36.8239 46.2415 42.6939 39.6875 43.3454C32.5066 44.0582 26.2627 39.6409 24.9116 32.6632V32.6654ZM44.1051 50.3413C44.1006 50.5343 43.863 50.7658 43.6774 50.8997C40.4321 53.2673 35.3921 53.2332 32.2396 50.8112C32.0268 50.6478 31.8232 50.3254 31.8141 50.0689C31.7734 48.6548 31.7937 47.2383 31.7937 45.6789C35.9534 47.2202 39.9862 47.2315 44.121 45.7175C44.121 47.3132 44.1368 48.8273 44.1006 50.3413H44.1051ZM48.138 77.8732C43.3017 77.8732 38.4654 77.8732 33.6314 77.8732C33.5182 77.8732 33.4051 77.8732 33.2874 77.8732C33.095 77.8732 32.9027 77.8664 32.7103 77.8732C25.509 77.8709 12.0752 77.8732 8.36591 77.8732H7.39503C7.39503 74.6681 7.3724 71.556 7.39956 68.444C7.45614 61.9679 14.5555 56.9264 18.421 54.9107C20.5936 53.778 23.0671 53.0039 27.8061 52.0937C28.1162 52.0347 28.5733 52.214 28.8223 52.4387C32.2622 55.5077 36.2317 56.6858 40.7466 55.7574C42.0525 55.4895 43.304 54.947 44.6279 54.5134C44.6279 57.4167 44.6211 60.3562 44.6279 63.2958C44.6415 68.3305 46.0333 72.9543 48.7739 77.1695C48.9029 77.3693 49.0116 77.5827 49.1768 77.8709H48.138V77.8732ZM82.6053 63.3049C82.5601 73.3379 75.9404 81.8932 66.2543 84.3992C65.6613 84.5536 64.9598 84.5513 64.3668 84.3992C54.6829 81.8932 48.0543 73.3243 48.0203 63.3026C48.0045 58.4608 48.0181 53.6168 48.0181 48.6797C52.4583 47.2883 56.842 45.915 61.2279 44.5417C62.3051 44.2034 63.3688 43.813 64.4619 43.5497C65.0028 43.4203 65.6432 43.384 66.166 43.5452C71.4527 45.1614 76.7235 46.8252 81.9965 48.4777C82.1821 48.5367 82.3632 48.6139 82.6076 48.7047C82.6076 49.4583 82.6076 50.221 82.6076 50.9814C82.6076 55.09 82.6234 59.1963 82.6053 63.3049ZM77.7939 50.734C73.8651 49.4946 69.9341 48.2598 65.994 47.0635C65.5663 46.9342 65.0299 46.9432 64.5999 47.0726C60.6847 48.2621 56.7831 49.4878 52.8792 50.7136C51.768 51.0632 51.4127 51.5762 51.4105 52.7883C51.4059 54.6882 51.4105 56.5859 51.4105 58.4858C51.4105 60.102 51.4037 61.7159 51.4105 63.3321C51.4444 71.388 56.5998 78.4362 64.2673 80.8332C64.8941 81.0284 65.6817 81.0398 66.3109 80.8514C73.8154 78.5973 79.1269 71.497 79.2084 63.6408C79.2468 60.0135 79.2197 56.3861 79.2174 52.7565C79.2174 51.592 78.8712 51.0722 77.7962 50.734H77.7939ZM75.825 59.3189C75.825 59.5141 75.911 64.1175 75.4335 66.4192C74.3155 71.808 71.0114 75.4671 65.865 77.4101C65.5346 77.535 65.082 77.51 64.7402 77.3943C59.13 75.5238 54.9681 69.924 54.8278 63.9722C54.7508 60.7444 54.8165 57.5143 54.7893 54.2842C54.7848 53.7825 54.9771 53.6191 55.423 53.4806C58.5506 52.5227 61.6624 51.5194 64.7923 50.566C65.1408 50.4594 65.5799 50.4821 65.9352 50.5887C68.9836 51.5149 72.0184 52.4909 75.0669 53.4125C75.6553 53.5896 75.8612 53.8484 75.8431 54.4726C75.7956 56.0865 75.8273 57.7027 75.8273 59.3166L75.825 59.3189ZM72.0003 61.1984C69.6557 63.6068 67.2772 65.9834 64.8806 68.3373C64.2559 68.9501 63.2896 68.9161 62.6536 68.3009C61.2935 66.9867 59.9515 65.6497 58.6479 64.2786C57.9713 63.5659 58.0527 62.5649 58.7339 61.9225C59.3812 61.3119 60.3498 61.2687 61.0287 61.9225C61.9385 62.8032 62.7849 63.7498 63.7377 64.7553C65.7157 62.7601 67.5397 60.9101 69.3774 59.0737C70.1468 58.3042 71.0838 58.2089 71.7989 58.7922C72.5503 59.4029 72.7019 60.4833 72.0026 61.2006L72.0003 61.1984Z" fill="white"></path>
</svg>',
            'title' => 'Integrity',
            'description' => 'We do great work because we want to, not because someone is watching us'
        ],
        [
            'icon' => '<svg class="icon" viewBox="0 0 90 90">        <path d="M55.1754 20.0269C52.2406 18.3692 49.2258 18.4091 46.218 20.1468C45.3872 20.6265 44.6717 20.6195 43.8221 20.1163C42.41 19.2839 40.8402 18.8606 39.261 18.8606C38.136 18.8606 37.0087 19.0746 35.9284 19.5072C33.3631 20.5348 31.4638 22.6464 30.7178 25.2988C29.9058 28.1839 30.4212 31.0103 32.2899 33.9378C34.8388 37.9282 38.7221 41.0367 44.5117 43.722C44.7635 43.8396 45.4343 43.7996 45.9238 43.5739C49.8259 41.7798 52.9749 39.5718 55.5567 36.823C58.3033 33.9002 59.6236 30.9868 59.7083 27.6572C59.6024 24.2336 58.0773 21.6658 55.1731 20.0246L55.1754 20.0269ZM54.2858 32.969C52.3418 35.5509 49.6635 37.7071 45.8602 39.7599C45.6273 39.8845 45.3425 39.948 45.053 39.948C44.7635 39.948 44.4646 39.8822 44.2246 39.7528C40.4448 37.7283 37.783 35.5956 35.8484 33.0419C34.8011 31.6593 33.4643 29.5101 33.9397 26.8201C34.3186 24.6756 35.6884 23.1566 37.7971 22.5429C39.8541 21.9457 41.7675 22.4888 43.3279 24.1137C43.4102 24.2007 43.4926 24.29 43.575 24.3794C43.7703 24.5933 43.9539 24.7956 44.1304 24.9155C45.2272 25.6562 46.1733 24.7767 46.531 24.3629C48.0231 22.6346 49.9295 21.9833 52.0453 22.4818C54.5635 23.0767 56.2392 25.2447 56.218 27.8759C56.1969 29.5995 55.5638 31.2619 54.2811 32.9643L54.2858 32.969ZM29.3668 8.82952C28.8255 8.23697 28.7338 7.6256 29.0821 6.90137C29.3339 6.37466 29.7222 6.02195 30.8119 5.91378C30.859 5.9373 30.9084 5.96081 30.9602 5.98432C31.2779 6.13246 31.6709 6.31822 31.9439 6.55336C32.7983 7.29641 33.6149 8.14762 34.4034 8.97296L34.6222 9.20105C35.3989 10.0123 35.4318 11.0704 34.7023 11.7711C33.9821 12.4624 32.9654 12.4154 32.1699 11.6489C31.2826 10.7929 30.28 9.82417 29.3692 8.82952H29.3668ZM38.8397 61.3176C34.8082 57.2543 30.7084 53.17 27.2275 49.711C26.0225 48.5118 24.714 47.8487 23.3419 47.7359C22.3958 47.6606 21.4261 47.7076 20.3976 47.7594C19.9293 47.7829 19.4445 47.8064 18.9526 47.8205L17.9688 47.844V46.8611C17.9688 46.6754 17.9712 46.4685 17.9735 46.2498C17.9806 45.7466 17.9876 45.1752 17.9641 44.6132C17.8064 40.8039 14.568 37.6248 10.7459 37.5261C10.6776 37.5261 10.607 37.5237 10.5388 37.5237C8.55712 37.5237 6.70255 38.2644 5.2928 39.6259C3.8501 41.0179 3.04284 42.9037 3.02401 44.9353C3.00283 47.2209 3.00754 49.5488 3.01225 51.7991C3.01695 54.1058 3.02166 56.4925 3.00048 58.8415C2.97694 61.2776 3.82185 63.3069 5.58699 65.0469C7.66043 67.0903 9.7527 69.183 11.7744 71.2076C13.7443 73.178 15.7824 75.2167 17.7993 77.206C18.5336 77.9302 18.8561 78.725 18.8396 79.7761C18.8043 82.1745 18.8137 84.6176 18.8231 86.9784L18.8278 88.0131C18.8302 88.916 19.1691 89.9906 20.7601 89.9929C27.2157 90.0024 33.2054 90.0024 39.0751 89.9929C40.2824 89.9929 40.9767 89.2922 40.9791 88.0789C40.9885 80.1382 40.9885 73.0958 40.9791 66.5494C40.9767 64.5037 40.2565 62.7449 38.8444 61.3199L38.8397 61.3176ZM37.51 80.7049C37.51 82.3086 37.51 83.9146 37.51 85.53V86.4894H22.4122L22.351 85.5958C22.3463 85.5182 22.3393 85.4406 22.3322 85.363C22.3157 85.1702 22.2993 84.9703 22.2993 84.754C22.2993 84.1662 22.2922 83.5807 22.2875 82.9928C22.2734 81.502 22.2569 79.9595 22.3204 78.4334C22.3675 77.3118 22.0357 76.51 21.1743 75.6635C18.9408 73.4696 16.6908 71.2193 14.5162 69.0443C12.4475 66.9774 10.3105 64.8376 8.19232 62.7543C6.99203 61.5715 6.44366 60.2406 6.46485 58.5594C6.50015 55.7988 6.49544 52.9889 6.49073 50.2707C6.48838 48.5212 6.48603 46.7718 6.49073 45.0223C6.50015 42.9225 8.00169 41.2342 10.061 41.0108C12.1086 40.7898 13.9796 42.1442 14.4174 44.1664C14.5091 44.5897 14.5091 45.0059 14.5091 45.3703L14.5138 47.5877C14.5209 49.9133 14.528 52.3187 14.5068 54.6866C14.4856 57.031 15.2834 58.9615 16.945 60.5839C17.7311 61.3528 18.5148 62.1406 19.275 62.9024C19.8751 63.5044 20.4753 64.1087 21.0801 64.7059C21.9415 65.5571 22.9747 65.6253 23.7161 64.8847C24.4621 64.1369 24.3868 63.1258 23.5184 62.2464C22.7582 61.4751 21.9886 60.7109 21.2214 59.9443C20.6118 59.3377 20.0022 58.731 19.3974 58.1197C17.6017 56.3114 17.5028 53.8895 19.155 52.2317C19.9669 51.4182 21.0095 50.9926 22.0945 51.0419C23.163 51.0866 24.1868 51.5686 25.0529 52.4293C26.4791 53.8495 27.903 55.2745 29.3268 56.6971C31.525 58.8956 33.7985 61.1671 36.0438 63.3915C37.0464 64.3862 37.5312 65.5501 37.5265 66.9515C37.5076 71.5203 37.51 76.1878 37.5123 80.7025L37.51 80.7049ZM43.2431 2.57949C43.149 1.29562 43.5256 0.512606 44.5023 0H45.4931C46.4675 0.512606 46.8464 1.29562 46.7522 2.57949C46.6957 3.33664 46.7099 4.08674 46.7216 4.81332C46.7287 5.26715 46.7358 5.69275 46.7287 6.12306C46.7099 7.18119 45.9803 7.9501 44.9989 7.9501C44.0174 7.9501 43.2879 7.18119 43.269 6.12306C43.262 5.69745 43.269 5.25304 43.2761 4.82508C43.2879 4.09379 43.302 3.33664 43.2455 2.57479L43.2431 2.57949ZM55.2766 11.757C54.5682 11.054 54.5941 10.0193 55.3378 9.24102C56.4416 8.08883 57.3124 7.22351 58.1597 6.43109C58.5315 6.08543 58.9081 5.91378 59.3176 5.91378C59.5647 5.91378 59.8213 5.97492 60.0943 6.09954C60.6262 6.33939 60.9886 6.72267 61.1181 7.82783C61.0969 7.87485 61.0733 7.92188 61.0498 7.97126C60.8945 8.30046 60.6991 8.71195 60.4497 9.00118C59.7154 9.85003 58.8681 10.6566 58.0467 11.4372L57.7996 11.6724C57.0159 12.4178 55.978 12.453 55.2743 11.7547L55.2766 11.757ZM80.0497 37.5378C76.1617 37.2768 72.7703 39.9457 72.1513 43.7596C72.0336 44.4792 72.0101 45.2246 71.9866 46.0123C71.9748 46.3862 71.963 46.7624 71.9395 47.1339L71.8948 47.9052L71.1652 48.018C71.0758 48.0392 70.8945 48.0721 70.678 48.0227C70.4944 47.9804 70.3109 47.9357 70.1296 47.8863C69.3318 47.6724 68.5669 47.5642 67.835 47.5642C65.8957 47.5642 64.1776 48.319 62.6337 49.8427C60.0378 52.401 57.4183 55.0252 54.886 57.5624C53.6998 58.7498 52.516 59.9373 51.3275 61.1224C49.76 62.6884 48.9998 64.5484 49.0045 66.8128C49.014 71.2734 49.0116 75.7364 49.0116 80.197C49.0116 82.7647 49.0116 85.3301 49.0116 87.8978C49.0116 89.3463 49.6565 89.9906 51.0992 89.9929C57.0841 89.9976 63.0691 89.9976 69.0564 89.9929C70.5133 89.9929 71.1628 89.3534 71.1652 87.919V86.7903C71.1746 84.4295 71.1817 81.9864 71.1534 79.588C71.144 78.6662 71.4264 77.9702 72.0713 77.3306C74.639 74.7888 77.2372 72.1905 79.7531 69.6768C81.3653 68.0661 82.9775 66.4554 84.592 64.847C86.1759 63.2716 86.9525 61.5339 86.9667 59.5352C86.9972 55.2157 87.0231 50.0896 86.9667 44.8389C86.9243 41.0061 83.8883 37.7965 80.0544 37.5355L80.0497 37.5378ZM81.9866 62.5615C80.3674 64.1651 78.7294 65.8088 77.1455 67.3959C74.5095 70.0366 71.7842 72.7689 69.0612 75.4166C68.0044 76.4442 67.5996 77.4247 67.6679 78.7956C67.7455 80.3592 67.7291 81.9511 67.7102 83.4913C67.7032 84.1662 67.6961 84.8434 67.6961 85.5276V86.487H52.4783V80.3428C52.4807 75.9221 52.483 71.351 52.4713 66.8551C52.4689 65.5313 52.8925 64.4614 53.7704 63.589C57.5195 59.8503 61.264 56.1069 65.0084 52.3634C66.4888 50.882 68.0209 50.9103 69.0423 51.1924C70.5274 51.6039 71.5441 52.662 71.9018 54.1693C72.2337 55.5684 71.8242 56.8946 70.718 58.0044C70.1744 58.55 69.6283 59.0955 69.0823 59.6387C68.2304 60.4875 67.3784 61.3387 66.5311 62.1923C66.0887 62.6367 65.1802 63.7912 66.2793 64.8894C67.1783 65.7876 68.2633 65.3502 68.9788 64.6401C70.0402 63.5867 71.0969 62.5285 72.156 61.4704L73.168 60.457C74.7284 58.8956 75.4839 57.0639 75.4792 54.8512C75.4721 51.4534 75.4745 48.1356 75.4792 45.3374C75.4839 42.8308 77.1596 41.0014 79.466 40.9873H79.4919C80.5604 40.9873 81.5442 41.38 82.2667 42.0972C83.0645 42.8873 83.5046 44.023 83.5046 45.2975C83.5046 46.8376 83.5046 48.3778 83.5046 49.918C83.5023 52.8408 83.4999 55.8647 83.5211 58.8368C83.5329 60.3041 83.0292 61.5221 81.9843 62.5591L81.9866 62.5615Z" fill="white"></path>
</svg>',
            'title' => 'Respect',
            'description' => 'We treat everyone fairly and respect all views and perspectives, even when we disagree'
        ],
        [
            'icon' => '<svg class="icon" viewBox="0 0 90 90"><path d="M84 13.1519V12.138C83.9954 10.4655 83.4767 9.95285 81.7738 9.95057C80.9981 9.95057 80.22 9.95057 79.4282 9.95057H78.6663V9.10979C78.6663 8.26902 78.6686 7.46925 78.6663 6.66949C78.6571 5.22262 78.0833 4.65754 76.6122 4.64843C75.5771 4.64387 74.5443 4.64387 73.5093 4.64387C72.768 4.64387 72.0267 4.64387 71.2854 4.64387C71.0949 4.64387 70.8952 4.6302 70.6863 4.61653C70.6818 4.61653 70.6772 4.61653 70.6726 4.61653C70.6726 4.58007 70.6726 4.54361 70.6726 4.50944C70.6749 4.22234 70.6795 3.97398 70.6657 3.71879C70.6152 2.67522 69.9542 1.97343 69.0224 2.00077C68.1274 2.01444 67.4962 2.6661 67.4182 3.65954C67.4021 3.86233 67.4044 4.06057 67.4067 4.25424C67.4067 4.32032 67.4067 4.38867 67.4067 4.45475V6.78341C67.4067 9.48575 67.409 12.2792 67.3906 15.0249C67.3906 15.2846 67.269 15.5854 67.1956 15.6355C65.488 16.5697 64.687 18.0052 64.749 20.0217C64.7743 20.8101 64.7674 21.6167 64.7605 22.3959C64.7605 22.6374 64.7559 22.879 64.7559 23.1182H60.8727C60.9025 22.9701 60.9346 22.822 60.9668 22.6739C61.1022 22.0427 61.2399 21.3888 61.2972 20.7326C61.373 19.869 61.3707 18.9029 61.2927 17.7842C61.093 14.9337 58.7727 12.6985 55.8947 12.5846C53.0006 12.4706 50.4737 14.5714 50.1432 17.3695C49.9252 19.2242 49.7806 21.1906 50.5357 23.1569C50.3773 23.1569 50.2213 23.1569 50.0675 23.1592C49.3721 23.1638 48.7134 23.1683 48.057 23.1478C46.6088 23.0977 45.397 23.6013 44.3505 24.6745C42.9849 26.0712 41.562 27.4679 40.2263 28.7667C39.9715 29.0151 39.4873 29.3591 39.1683 29.5209C36.7194 30.7513 34.2201 31.984 31.8011 33.1779L31.2365 33.4559C31.1837 33.4833 31.1286 33.5106 31.0736 33.5402C30.9037 33.6314 30.727 33.7225 30.6008 33.7248C28.6477 33.743 26.6762 33.7407 24.7667 33.7385H23.5297C23.5618 33.5698 23.594 33.4035 23.6261 33.2372C23.7592 32.5513 23.8877 31.9042 23.9359 31.2321C24.0048 30.3047 23.9933 29.3135 23.8992 28.1971C23.6582 25.3238 21.0832 23.0749 18.1661 23.1729C15.2078 23.2777 12.7658 25.6725 12.7222 28.5092C12.7176 28.8305 12.7039 29.1518 12.6924 29.4753C12.635 30.8835 12.5777 32.3326 13.1904 33.7453C13.1767 33.7453 13.1652 33.7453 13.1514 33.7476C12.798 33.7726 12.4629 33.7977 12.1301 33.8364C8.62096 34.2602 6.05508 37.0697 6.02754 40.5171C5.99082 45.0901 5.99082 50.0573 6.02754 55.7012C6.04131 57.9069 7.16589 59.5087 9.27964 60.3358C9.78456 60.534 10.3193 60.6593 10.8334 60.7824C10.9826 60.8188 11.1341 60.853 11.2832 60.8917V78.8557H10.6337C9.92685 78.8557 10.6888 78.8671 8.84817 78.842H8.82522C8.22621 78.8375 7.64327 79.0653 7.2095 79.4891C6.77573 79.9152 6.53704 80.4803 6.53704 81.0864V87.8286C6.53704 89.0248 7.51704 90 8.72424 90H80.8259C81.9299 90 82.8318 89.1273 82.8364 88.061V88.0336C82.8226 82.8226 82.8226 77.525 82.8226 72.4006C82.8226 69.8805 82.8226 67.3605 82.8226 64.8404C82.8226 63.5121 82.2144 62.9151 80.8489 62.906C79.2584 62.8946 77.7781 62.8946 76.3276 62.906C75.6253 62.8946 74.923 62.8968 74.1886 62.9037C73.9109 62.9037 73.6309 62.906 73.3463 62.9082V59.1122C73.3463 56.6035 73.3463 54.0926 73.3463 51.5839C73.3486 49.7565 72.589 48.4168 71.0214 47.4894C69.6605 46.6828 68.2857 45.8511 66.9546 45.0445C65.6923 44.2789 64.3864 43.4883 63.0943 42.7227C62.7294 42.5063 62.6261 42.3262 62.649 41.9571C62.6903 41.2941 62.6766 40.6242 62.6605 39.9748L62.6559 39.7971C62.6307 38.6214 61.9904 37.915 60.9759 37.9241C59.9891 37.9446 59.4153 38.6191 59.3969 39.7743C59.39 40.2801 59.39 40.7837 59.3923 41.3032C59.3923 41.4262 59.3923 41.5515 59.3923 41.6746H51.977V38.1839C51.977 35.641 51.977 33.0982 51.977 30.5553C51.977 30.0769 51.977 29.1859 51.1141 28.7097C50.1708 28.188 48.1259 28.491 47.341 29.2657C47.0495 29.5551 46.7511 29.8376 46.4528 30.1224C45.7459 30.7969 45.0138 31.4964 44.3597 32.2506C43.219 33.5698 41.7158 34.2739 40.123 35.019C39.4987 35.3106 38.8561 35.6114 38.2227 35.9577C38.2227 35.8848 38.2227 35.8142 38.225 35.7413C38.2319 35.1671 38.2387 34.6704 38.2066 34.16C38.1791 33.7111 38.2869 33.5243 38.6564 33.3898C40.6807 32.6493 42.4686 31.3642 44.2862 29.3432C44.5915 29.0037 44.9334 28.6756 45.2662 28.3588C45.6266 28.0148 45.9984 27.6593 46.338 27.2743C46.898 26.6385 47.5406 26.3811 48.4839 26.3788C53.2324 26.4153 58.0612 26.4107 62.7294 26.4061H66.0067C67.4228 26.4039 68.0034 25.8297 68.0126 24.4261C68.0172 23.5511 68.0172 22.6762 68.0172 21.8012C68.0172 21.1701 68.0172 20.5366 68.0172 19.9055C68.0172 19.3632 68.1411 18.9599 68.3683 18.7389C68.5336 18.5771 68.7585 18.5042 69.066 18.5087C69.4975 18.5179 70.069 18.6705 70.0713 19.885C70.0759 21.9038 70.0759 23.9203 70.0713 25.939C70.0713 27.7505 69.3644 28.4568 67.5536 28.4614C66.6057 28.4637 65.6579 28.4614 64.7123 28.4614C63.6267 28.4614 62.5412 28.4614 61.4556 28.4637C59.9799 28.4682 59.4015 29.0333 59.39 30.471C59.3809 31.5921 59.3832 32.6265 59.39 33.6314C59.3992 34.8185 60.0166 35.5613 60.9966 35.5658H61.0058C61.9812 35.5658 62.6261 34.8162 62.649 33.6496C62.6582 33.146 62.6559 32.6425 62.6536 32.1116C62.6536 31.9771 62.6536 31.8404 62.6536 31.7014C63.1356 31.7014 63.613 31.7014 64.0857 31.7014C65.3962 31.7014 66.6333 31.706 67.891 31.6969C70.9778 31.6764 73.3165 29.35 73.3349 26.2831C73.344 24.8408 73.3417 23.3734 73.3394 21.9562C73.3394 21.2088 73.3394 20.4614 73.3394 19.7164C73.3394 17.9414 72.5568 16.5925 71.0099 15.7084C70.8401 15.6104 70.7254 15.5102 70.6932 15.4623C70.6588 14.8449 70.6634 14.2092 70.668 13.5939C70.668 13.48 70.668 13.3661 70.6703 13.2522H75.3935C75.3935 13.5233 75.3935 13.7945 75.3935 14.061C75.3935 14.9428 75.3889 15.7745 75.3981 16.6221C75.4096 17.8571 76.0178 18.4677 77.2571 18.4837C78.7856 18.5065 80.3623 18.5065 82.0767 18.4837C83.3712 18.4677 83.9794 17.8616 83.9908 16.572C84 15.4304 83.9977 14.2707 83.9954 13.1474L84 13.1519ZM15.972 28.6983C16.0134 27.4497 17.0255 26.4449 18.2763 26.413C18.2969 26.413 18.3153 26.413 18.336 26.413C19.5248 26.413 20.5369 27.3312 20.654 28.5275C20.6884 28.8738 20.6792 29.2224 20.67 29.5938C20.6655 29.7647 20.6609 29.9379 20.6609 30.1088V30.4505H20.6907C20.6907 30.471 20.6907 30.4938 20.6907 30.5143C20.6953 30.7969 20.6976 31.0885 20.6838 31.3688C20.6195 32.7951 19.7015 33.6815 18.2855 33.6815H18.2786C16.8694 33.6792 15.9835 32.7724 15.9652 31.3187V31.2549C15.9537 30.4141 15.9445 29.546 15.972 28.6961V28.6983ZM21.9668 86.7075H9.83046V82.1687H21.9668V86.7075ZM30.5939 69.9421C30.5893 67.9871 30.5847 65.9637 30.61 63.9769C30.6214 62.988 30.2152 62.3363 29.3293 61.9262C27.4497 61.0558 25.5448 60.158 23.6995 59.2922C22.2674 58.6178 20.8353 57.9456 19.4009 57.2757C18.5173 56.8633 17.8586 56.8086 17.3858 57.1071C16.913 57.4056 16.6835 58.0185 16.6812 58.9846C16.6789 63.5622 16.6789 68.142 16.6789 72.7196V78.7509H14.6593V55.5554C15.0793 55.56 15.4993 55.5691 15.9078 55.5759C17.0645 55.601 18.2602 55.626 19.4284 55.5235C22.0815 55.2888 24.2939 55.8995 26.3939 57.442C27.6883 58.3922 29.1067 59.2011 30.4814 59.9826C30.9841 60.2674 31.4844 60.5545 31.9801 60.8462C32.4988 61.1515 32.7214 61.5183 32.7191 62.072C32.7168 64.2776 32.7145 66.4833 32.7145 68.6889C32.7145 70.236 32.7145 71.7831 32.7145 73.328C32.7145 73.3621 32.7099 73.4009 32.7054 73.4419H30.5962V72.2434C30.5962 71.4755 30.5962 70.7077 30.5939 69.9421ZM24.0369 73.4989C22.6346 73.5125 22.031 74.1049 22.0172 75.4789C22.0104 76.3037 22.0104 77.1263 22.0127 77.9739C22.0127 78.2405 22.0127 78.5094 22.0127 78.7805H19.9999V61.1447L20.273 61.2723C22.4831 62.3044 24.769 63.3708 27.0044 64.444C27.1398 64.5101 27.289 64.6718 27.3051 64.7151C27.3303 67.144 27.3303 69.5342 27.328 72.0634V73.492C27.0434 73.492 26.768 73.492 26.4949 73.492C25.648 73.492 24.8402 73.4897 24.0346 73.4966L24.0369 73.4989ZM43.3131 86.7121H25.3313V76.7959H43.3108V86.7121H43.3131ZM64.7513 68.1511H57.3727V50.5654C57.453 50.5973 57.5288 50.6292 57.6022 50.6634L58.8645 51.2535C60.634 52.0806 62.4631 52.9374 64.2464 53.8123C64.4621 53.9194 64.6848 54.227 64.6848 54.3387C64.7307 58.2646 64.7375 62.2566 64.7467 66.1164L64.7513 68.1375C64.7513 68.1375 64.7513 68.1466 64.7513 68.1511ZM76.1371 66.1415H76.3276C76.4515 66.1415 76.5801 66.1415 76.7086 66.1415C77.3902 66.1438 78.0719 66.146 78.7604 66.1438H79.4971V86.7143H68.0929V66.1415H76.1394H76.1371ZM52.0573 44.9876C52.1216 44.9785 52.1858 44.9693 52.2455 44.9693H53.1658C55.4517 44.9625 57.8133 44.958 60.1382 44.9785C60.4045 44.9807 60.7258 45.0787 60.9966 45.2405C62.5549 46.1679 64.1362 47.1203 65.6625 48.0408C66.8697 48.7677 68.0769 49.4968 69.2864 50.2191C69.8532 50.5586 70.0827 50.9049 70.0827 51.413C70.0805 53.5776 70.0782 55.7423 70.0782 57.9069C70.0782 59.5588 70.0782 61.2107 70.0759 62.8513H67.9782V62.2361C67.9782 61.4249 67.9782 60.616 67.9759 59.8049C67.9713 57.7246 67.969 55.5759 67.9896 53.4614C67.9988 52.4019 67.5742 51.7343 66.6103 51.29C64.4025 50.276 62.1671 49.2211 60.0051 48.2003C59.032 47.74 58.0566 47.2798 57.0812 46.8218L56.8976 46.7352C56.613 46.5985 56.3193 46.4572 56.0002 46.3706C55.3439 46.1952 54.9262 46.3501 54.6921 46.5096C54.4557 46.6714 54.1573 47.0109 54.0907 47.6968C54.0609 47.9952 54.0632 48.2914 54.0632 48.5785V48.7517C54.0632 53.8761 54.0632 58.9983 54.0632 64.1227V68.1625H52.0619V44.983L52.0573 44.9876ZM64.7123 71.4869V86.7075H46.7144V71.4869H64.7123ZM39.4345 38.9768C40.6486 38.3844 41.9063 37.7715 43.118 37.1221C44.3574 36.459 45.4567 35.5681 46.2784 34.8663C46.9715 34.2739 47.6049 33.6131 48.2728 32.9159C48.3921 32.7929 48.5115 32.6653 48.6354 32.5399V68.1967H45.7964C43.8295 68.1967 43.3751 68.6524 43.3751 70.637C43.3751 71.3479 43.3751 72.0588 43.3751 72.7925V73.4282H35.9873V72.7355C35.9873 71.7216 35.985 70.7054 35.9827 69.6914C35.9781 67.3081 35.9712 64.845 35.9988 62.4229C36.0217 60.4041 35.184 58.9345 33.4375 57.9319C32.0421 57.1322 30.6444 56.2777 29.2903 55.4529C28.1749 54.7716 27.0228 54.0698 25.8752 53.3976C25.3818 53.1083 25.228 52.8303 25.2693 52.2926C25.3267 51.5384 25.2992 50.7773 25.2739 50.0391C25.2418 49.1094 24.6336 48.4487 23.7615 48.394C23.3254 48.3666 22.91 48.5079 22.591 48.7882C22.2605 49.0798 22.0654 49.4968 22.0379 49.9616C22.0035 50.5586 22.0104 51.1465 22.0195 51.7685C22.0195 51.928 22.0241 52.0875 22.0241 52.2493H14.6111V49.3715C14.6111 47.5259 14.6111 45.6802 14.6111 43.8346C14.6111 43.0166 14.4344 42.447 14.0717 42.0916C13.794 41.8227 13.4199 41.6883 12.9563 41.6974C11.8891 41.7156 11.3475 42.4516 11.3475 43.8848C11.3475 47.0884 11.3475 50.292 11.3475 53.4979V57.5332C10.7003 57.5696 10.1816 57.4329 9.83964 57.1367C9.47473 56.82 9.28883 56.3119 9.28653 55.6306C9.27046 50.0436 9.26358 45.3521 9.28883 40.6265C9.3003 38.4687 10.9045 37.0127 13.2799 37.0036C16.4173 36.9922 19.6074 36.9899 22.6943 36.9899C24.0231 36.9899 25.3497 36.9899 26.6762 36.9899C26.7336 36.9899 26.791 36.9899 26.8484 36.9876C26.9585 36.9854 27.0618 36.9831 27.1559 36.9922C30.2106 37.293 32.7627 36.7211 34.9614 35.24C34.9614 35.3403 34.9614 35.4382 34.9637 35.5339C34.9706 36.156 34.9775 36.7438 34.9362 37.3135C34.9293 37.4183 34.7824 37.6348 34.6286 37.7304C32.8132 38.8401 31.1562 39.2867 29.4119 39.1363C28.0762 39.0224 26.7152 39.052 25.3979 39.0816C24.9228 39.093 24.4454 39.1021 23.9703 39.1067C22.614 39.1181 22.0287 39.7014 22.0149 41.0525C22.0035 41.9822 21.9966 43.085 22.0172 44.2174C22.0379 45.3362 22.6667 46.0311 23.6582 46.0311C24.652 46.0311 25.2556 45.3521 25.2739 44.2129C25.2831 43.7321 25.2808 43.2491 25.2785 42.7364C25.2785 42.6065 25.2785 42.4743 25.2785 42.3399C25.6756 42.3399 26.0657 42.3331 26.4513 42.3262C27.5506 42.3103 28.5903 42.2966 29.6323 42.3581C31.6473 42.4766 33.5339 42.0392 35.3975 41.0229C36.7171 40.3029 38.0965 39.6307 39.4299 38.9791L39.4345 38.9768ZM58.0589 19.9374C58.0589 20.2062 58.0635 20.4751 58.0566 20.7417C58.0222 22.143 57.0858 23.0635 55.6743 23.084C55.6583 23.084 55.6445 23.084 55.6284 23.084C54.2812 23.084 53.3747 22.2068 53.3127 20.8351C53.2737 19.9966 53.2737 19.1194 53.3127 18.2262C53.3701 16.8409 54.3799 15.8201 55.6766 15.8201C55.7019 15.8201 55.7271 15.8201 55.7524 15.8201C57.056 15.8611 58.0268 16.8978 58.0589 18.2877C58.0658 18.5771 58.0635 18.8642 58.0612 19.1536C58.0612 19.288 58.0612 19.4224 58.0612 19.5569C58.0612 19.6822 58.0612 19.8075 58.0612 19.9351L58.0589 19.9374ZM75.3476 9.8731H70.7345V7.93407H75.3476V9.8731ZM80.6699 15.2071H78.7214V13.2362H80.6699V15.2071Z" fill="white"></path>
</svg>',
            'title' => 'Growing together',
            'description' => 'We seek opportunities every day to learn, improve, and grow as individuals and as a team'
        ],
        [
            'icon' => '<svg class="icon" viewBox="0 0 90 90"><path d="M89.9953 48.1099C89.6145 46.6007 89.0469 44.8777 87.741 43.4696C87.8648 43.2627 87.9863 43.0605 88.1077 42.8584C88.4535 42.2848 88.7782 41.7418 89.0959 41.18C89.3669 40.7004 89.451 40.1973 89.3295 39.7625C89.2244 39.3863 88.9791 39.0807 88.617 38.8762C87.8461 38.4437 86.9957 38.7164 86.4561 39.5744C86.1547 40.0516 85.8721 40.5264 85.573 41.0295C85.4749 41.1917 85.3791 41.3563 85.2787 41.5232L85.2109 41.4926C85.0334 41.4127 84.8745 41.3398 84.7133 41.2693C80.3144 39.3746 75.2987 41.3069 73.2943 45.6652C73.1752 45.9237 73.0841 46.2152 72.9976 46.4997C72.8388 47.0145 72.6752 47.5457 72.3762 47.7173C71.9791 47.9454 71.36 47.8889 70.7596 47.8349C70.484 47.809 70.2013 47.7832 69.9303 47.7832H69.921C69.4888 47.7832 69.0589 47.7832 68.6267 47.7808C67.6759 47.7761 66.6924 47.7714 65.7229 47.7973C65.223 47.8114 64.8072 47.983 64.5198 48.2909C64.2488 48.5848 64.1157 48.9773 64.1343 49.4263C64.1741 50.3173 64.7651 50.8485 65.7603 50.8885C65.9916 50.8979 66.2252 50.8955 66.4495 50.8932H72.5795C72.5888 50.9261 72.6005 50.9614 72.6098 50.9943C72.6566 51.1565 72.7009 51.2999 72.736 51.4456C73.7102 55.3431 76.9434 57.9736 80.9708 58.1476C81.09 58.1523 81.2091 58.1546 81.3306 58.1546C85.0077 58.1546 88.3507 55.7169 89.5445 52.1203C89.6659 51.7559 89.7617 51.3798 89.8575 51.0154C89.9019 50.8462 89.9463 50.6769 89.9907 50.51L90 48.1593L89.993 48.1005L89.9953 48.1099ZM81.2605 43.6694C82.0945 43.6694 82.9168 43.8645 83.641 44.2735L81.0526 48.5801C81.0036 48.5189 80.9545 48.4602 80.9078 48.4014C80.6695 48.1052 80.4452 47.8278 80.2046 47.5598C79.5575 46.8382 78.7235 46.723 78.0297 47.2589C77.7049 47.5105 77.497 47.8631 77.448 48.2486C77.3919 48.6788 77.5321 49.1372 77.8381 49.5368C78.5156 50.4183 79.2211 51.2928 79.9383 52.1391C80.3681 52.6445 80.8564 52.8937 81.347 52.856C81.8492 52.8184 82.3048 52.487 82.6645 51.8993C83.3327 50.8133 83.9961 49.699 84.6386 48.6224C84.9586 48.0841 85.281 47.5481 85.6034 47.0098C85.7249 46.8053 85.8534 46.6054 85.9819 46.4009L85.9912 46.3845C87.4162 48.1804 87.171 51.2881 85.3978 53.2157C83.3444 55.4466 79.9149 55.6652 77.5905 53.7141C75.3314 51.817 74.9179 48.439 76.649 46.0248C77.733 44.5109 79.5201 43.6694 81.2559 43.6694H81.2605ZM42.941 19.2358C43.4058 19.3534 43.4689 19.4968 43.4643 19.9693C43.4479 21.5607 43.4526 23.1757 43.4549 24.7389V25.7732C43.4572 25.9472 43.4549 26.1211 43.4549 26.2951C43.4502 26.8499 43.4479 27.4235 43.4923 27.9876C43.5624 28.8574 44.1254 29.3746 44.9991 29.3746H45.0038C45.8541 29.3746 46.4054 28.8692 46.5152 27.9923C46.5479 27.7314 46.5479 27.4705 46.5456 27.2166V25.4535C46.5526 23.6834 46.555 21.8522 46.5363 20.0516C46.5316 19.5156 46.6063 19.351 47.153 19.21C49.7671 18.5353 51.8954 16.7417 52.9933 14.2852C54.082 11.8451 54.0096 9.08067 52.7901 6.69703C52.6476 6.41729 52.4467 6.14696 52.2551 5.88603C51.9327 5.44879 51.6314 5.03741 51.6454 4.67305C51.6641 4.22406 51.9981 3.76096 52.3532 3.26966C52.5308 3.02518 52.713 2.7713 52.8578 2.51272C53.1312 2.02612 53.2129 1.54187 53.0938 1.10698C52.991 0.730866 52.7387 0.420569 52.3696 0.209003C51.594 -0.232935 50.7693 0.0373991 50.2157 0.911872C49.912 1.39142 49.627 1.86862 49.3256 2.37403C49.2298 2.53388 49.134 2.69373 49.0383 2.85593C47.2605 1.9744 45.4406 1.68526 43.6231 1.99321C39.4554 2.70078 36.4185 6.16811 36.2386 10.4206C36.0657 14.5085 38.8831 18.2133 42.9363 19.2311L42.941 19.2358ZM47.3352 5.71442C46.6367 6.88274 45.9359 8.0487 45.2 9.27343L44.8215 9.90578C44.7772 9.85172 44.7351 9.8 44.6907 9.74828C44.4548 9.46149 44.2305 9.19116 43.9969 8.92788C43.3147 8.15684 42.455 8.04165 41.7542 8.62698C41.065 9.20056 41.016 10.1173 41.6281 10.9095C42.3079 11.7864 42.9947 12.6373 43.6722 13.4413C44.116 13.9678 44.6113 14.2264 45.1089 14.1865C45.6135 14.1489 46.0784 13.8056 46.4522 13.1968C47.1133 12.1178 47.7744 11.0106 48.4122 9.94104C48.7509 9.37216 49.092 8.80094 49.4331 8.23206C49.5405 8.05575 49.6527 7.8818 49.7695 7.70549C51.2249 9.81175 50.9048 12.8442 48.9752 14.7506C46.8797 16.8193 43.5998 16.9392 41.343 15.0304C39.1518 13.178 38.7056 9.86112 40.3245 7.47982C41.4108 5.88368 43.1676 5.00685 44.9641 5.00685C45.7864 5.00685 46.618 5.19021 47.396 5.57338C47.3749 5.6298 47.3562 5.67446 47.3352 5.70972V5.71442ZM24.4084 47.8067C24.1608 47.7785 23.9132 47.7832 23.6725 47.7879C23.5884 47.7879 23.502 47.7879 23.4226 47.7902H17.4701C17.386 47.5598 17.3066 47.3318 17.2271 47.1109C17.0122 46.502 16.8113 45.9284 16.5473 45.376C16.4048 45.0775 16.1969 44.7907 15.996 44.5156C15.6876 44.0949 15.398 43.6976 15.4096 43.345C15.4283 42.8913 15.7624 42.4235 16.1175 41.9298C16.2904 41.6877 16.4702 41.4385 16.6151 41.1823C17.1244 40.282 16.9748 39.4216 16.2249 38.935C15.8699 38.7046 15.4821 38.6247 15.1059 38.707C14.6714 38.801 14.2813 39.1019 13.9799 39.5768C13.6739 40.0563 13.3772 40.5523 13.0899 41.0295C12.9987 41.1823 12.9076 41.3327 12.8165 41.4855C9.69312 40.0751 7.15141 40.5288 5.55583 41.1682C3.32015 42.0638 1.47695 43.9021 0.624258 46.0859C-1.13018 50.5805 0.932627 55.5735 5.31755 57.4541C6.45758 57.943 7.60462 58.1875 8.75166 58.1875C9.79591 58.1875 10.8402 57.9853 11.8751 57.5787C14.7952 56.4315 16.6595 54.1866 17.4187 50.9002H18.3462C18.9092 50.9002 19.4745 50.9002 20.0399 50.9002C21.4322 50.9002 22.8736 50.9049 24.2893 50.8885C25.205 50.8791 25.8078 50.3314 25.8615 49.4616C25.9152 48.5754 25.3289 47.9101 24.4061 47.8067H24.4084ZM8.77035 43.6741C9.6464 43.6741 10.4804 43.8809 11.1602 44.2759L8.57411 48.5824L7.95738 47.8325C6.94583 46.596 6.08847 46.8452 5.54882 47.2707C5.20541 47.541 5.00918 47.8678 4.96479 48.2416C4.91106 48.7047 5.10029 49.2148 5.5278 49.7531C6.13753 50.5218 6.7496 51.2811 7.34998 52.0074C7.84524 52.6069 8.35452 52.896 8.87548 52.856C9.38475 52.8184 9.84497 52.4705 10.2421 51.8194C10.8846 50.7662 11.527 49.692 12.1484 48.6529C12.5222 48.0276 12.896 47.4047 13.2697 46.7817C13.3468 46.6548 13.4309 46.5326 13.5197 46.4056C14.9424 48.2768 14.6527 51.4315 12.8282 53.3309C10.7724 55.4701 7.39437 55.644 5.13533 53.7305C2.84125 51.7865 2.42308 48.4813 4.16116 46.0413C5.31988 44.4169 7.11637 43.6764 8.77269 43.6764L8.77035 43.6741ZM67.3302 29.5227C72.1566 31.8734 76.0813 31.3656 79.0015 28.0111C81.8095 24.7836 81.7745 20.7403 78.9034 16.3092L79.2374 15.7497C79.5458 15.2396 79.8565 14.7224 80.1602 14.2006C80.7489 13.1921 80.6204 12.32 79.8121 11.8122C78.9968 11.2998 78.1441 11.5842 77.5297 12.5715C77.2447 13.0299 76.9667 13.4907 76.6911 13.9443L76.4224 14.3863C71.9253 12.7972 68.4725 13.4436 65.8724 16.3585C64.646 17.7361 63.9124 19.3369 63.6952 21.1211C63.4242 23.3473 63.9334 25.4347 65.2136 27.3294L64.3867 28.1451C63.4639 29.0549 62.5925 29.9129 61.7141 30.8015L61.6627 30.8532C61.3894 31.1282 61.0483 31.4691 61.0787 31.8217C61.1137 32.2378 61.2399 32.9994 61.6791 33.4084C61.9267 33.6411 62.214 33.754 62.5154 33.754C62.9102 33.754 63.3284 33.5565 63.7138 33.1686C64.6016 32.2777 65.4846 31.3844 66.3607 30.4982L67.3325 29.5156L67.3302 29.5227ZM72.194 21.4714C72.1426 21.4079 72.0912 21.3468 72.0422 21.2857C71.7875 20.9754 71.5492 20.6815 71.2899 20.4018C70.6895 19.7554 69.8205 19.6519 69.1734 20.1503C68.4609 20.7004 68.3557 21.603 68.9187 22.3506C69.5238 23.1545 70.206 24.0055 71.068 25.0304C71.4932 25.5358 71.9861 25.7803 72.486 25.745C72.993 25.7074 73.4485 25.383 73.8013 24.8094C74.5044 23.667 75.2053 22.494 75.8804 21.3609C76.1981 20.8296 76.5158 20.2984 76.8336 19.7695C76.9293 19.6096 77.0275 19.4521 77.1279 19.2946C78.4572 20.9307 78.282 24.0619 76.6957 25.9448C74.7965 28.2015 71.4348 28.6012 69.0449 26.8593C66.655 25.1174 66.0056 21.777 67.5638 19.257C68.6267 17.5386 70.4372 16.5913 72.3272 16.5913C73.1495 16.5913 73.9882 16.7723 74.7871 17.146L72.194 21.4737V21.4714ZM27.5248 33.7281C27.1043 33.7281 26.6698 33.5377 26.282 33.1569C25.2705 32.1672 24.2636 31.14 23.3735 30.2326C23.1703 30.0257 22.9904 29.8047 22.8572 29.6449C22.8526 29.6378 22.8479 29.6331 22.8432 29.6261C22.4461 29.7671 22.0629 29.9176 21.6915 30.061C20.7898 30.4136 19.9371 30.745 19.0423 30.9049C15.7834 31.4855 12.945 30.3995 10.8308 27.7667C8.79138 25.2279 8.32882 22.3106 9.46185 19.0972C9.84497 18.0111 10.6322 17.5222 11.52 17.8231C11.9171 17.957 12.2115 18.2062 12.375 18.5424C12.5829 18.9679 12.5759 19.5297 12.354 20.1644C11.3051 23.1851 12.5876 26.2669 15.405 27.4916C18.1686 28.6952 21.3668 27.5128 22.682 24.8024C23.5861 22.9383 23.4997 21.1117 22.4157 19.2264L22.2755 19.4545C22.0443 19.8282 21.8317 20.1714 21.6238 20.517C21.3177 21.0295 21.0117 21.5419 20.7057 22.0544C20.1824 22.9335 19.6404 23.8433 19.0984 24.7319C18.7176 25.3525 18.248 25.7004 17.7341 25.738C17.2271 25.7756 16.7155 25.5029 16.26 24.9552C15.5101 24.0525 14.8606 23.2486 14.2719 22.494C13.9262 22.052 13.7743 21.5842 13.8281 21.1446C13.8771 20.7591 14.0827 20.4159 14.4238 20.1503C15.1223 19.6096 15.9797 19.7554 16.6595 20.5335C16.8837 20.7897 17.0963 21.0506 17.3206 21.328C17.358 21.375 17.3954 21.4197 17.4351 21.4667V21.462C17.5519 21.2786 17.6547 21.1141 17.7551 20.9472C18.3602 19.9387 18.9652 18.9279 19.5843 17.8959L20.0422 17.1319C18.7713 16.5113 17.4234 16.4103 15.9259 16.8216C15.8138 16.8522 15.7017 16.8969 15.5825 16.9415C15.4797 16.9815 15.3793 17.0191 15.2741 17.052C14.3654 17.3365 13.5945 17.0026 13.2651 16.1822C12.931 15.3501 13.2954 14.5391 14.1692 14.1653C16.3675 13.2274 18.6592 13.2133 20.9766 14.1183C21.1565 14.1888 21.3341 14.2687 21.5326 14.3581C21.5724 14.3769 21.6144 14.3957 21.6588 14.4145C21.7569 14.2523 21.8527 14.0901 21.9508 13.9302C22.2498 13.4319 22.5325 12.9594 22.8362 12.4845C23.4132 11.5772 24.2939 11.3162 25.0789 11.8216C25.8381 12.3106 25.997 13.1686 25.49 14.0595C25.1793 14.6073 24.8593 15.1362 24.5206 15.698C24.3991 15.9002 24.2776 16.1023 24.1538 16.3092C26.873 19.7789 27.102 23.4836 24.8359 27.3247L27.943 30.4347C28.1112 30.6016 28.2817 30.7709 28.4429 30.9449C29.1718 31.73 29.2115 32.6515 28.541 33.2932C28.2397 33.5824 27.8869 33.7258 27.5225 33.7258L27.5248 33.7281ZM56.9998 51.9839C56.9998 51.7536 56.9951 51.5279 56.9928 51.3046C56.9834 50.5852 56.9764 49.9059 57.0208 49.2359C57.0419 48.9233 57.2077 48.5519 57.4156 48.352C60.3732 45.4865 61.8192 41.9557 61.7118 37.856C61.6744 36.4597 61.1534 35.9378 59.7845 35.9355C55.3715 35.9261 50.8861 35.9284 46.6694 35.9355C44.1674 35.9378 42.1911 36.9439 40.7964 38.9209C40.766 38.9656 40.738 39.0102 40.7123 39.0549C38.8831 37.7949 36.9371 37.7925 35.4303 37.9383C32.676 38.2016 30.4356 40.1127 29.7231 42.802C29.0083 45.4959 29.9871 48.2839 32.2158 49.9082C33.1222 50.5688 33.2367 50.8438 32.9493 51.9769C29.4194 52.4635 27.5248 54.2195 27.4664 57.0592C27.4407 58.3333 27.8332 59.4781 28.6345 60.4654C29.6834 61.7583 31.1248 62.3953 33.0311 62.4094C33.1479 64.6074 33.7646 66.5914 34.9093 68.4555C34.8252 68.519 34.7528 68.5707 34.6804 68.6201C30.8141 71.2764 28.7256 74.9106 28.471 79.424C28.3612 81.4034 28.3752 83.4179 28.3892 85.3691C28.3962 86.1777 28.4009 86.984 28.3985 87.7927C28.3962 88.7094 28.5247 89.2172 28.8471 89.5416C29.1695 89.866 29.6717 90 30.5875 90C38.6004 90 46.6157 90 54.6286 90H59.4364C59.4995 90 59.5602 90 59.6233 90C59.6864 90 59.7518 90 59.8172 90C59.9293 90 60.0461 89.9977 60.1606 89.9835C61.053 89.8848 61.5973 89.3371 61.6183 88.5167C61.6604 86.8195 61.6908 84.8119 61.5856 82.7832C61.5576 82.2684 60.9385 81.622 60.4783 81.495C59.9363 81.347 59.2425 81.7395 58.9178 81.9958C58.6304 82.2238 58.579 82.7245 58.5673 83.2322C58.5463 84.1584 58.5487 85.0635 58.5533 86.0249C58.5533 86.3093 58.5533 86.5961 58.5557 86.8829H57.0302C54.6987 86.8853 52.4981 86.8876 50.2671 86.8618C50.1806 86.8618 49.947 86.6878 49.8699 86.5209C48.9729 84.6098 48.0688 82.6257 47.111 80.4584C47.0479 80.3173 47.0689 80.0728 47.1133 80.0141C47.4917 79.5392 47.9076 79.0832 48.3094 78.6413C48.4052 78.5355 48.4986 78.432 48.5944 78.3263C49.5359 79.149 50.0732 79.5016 50.6105 79.4429C51.1618 79.3794 51.587 78.9069 52.3299 77.8679L55.771 73.0583C56.9321 74.323 57.7357 75.8251 58.2216 77.6422C58.6351 79.1866 59.5205 79.3065 60.1886 79.1349C61.1371 78.8904 61.5085 78.0653 61.2072 76.8711C60.511 74.1114 59.0883 71.7583 56.9811 69.8754C56.5443 69.4851 56.084 69.1302 55.6402 68.7893C55.4977 68.6788 55.3575 68.5707 55.222 68.4649C55.3388 68.1969 55.4556 67.936 55.5748 67.6774C55.8995 66.9605 56.2079 66.2834 56.4298 65.5618C56.6587 64.8189 56.7966 64.0385 56.9321 63.2839C56.9811 63.0018 57.0325 62.7174 57.0886 62.4353C60.7119 61.7512 62.4009 60.2562 62.5575 57.5975C62.6322 56.3234 62.3215 55.1833 61.63 54.2101C60.5717 52.7174 59.0556 51.9863 56.9975 51.9863L56.9998 51.9839ZM30.8959 58.2909C30.438 57.5857 30.4286 56.8617 30.8632 56.1377C31.2696 55.463 31.8607 55.1386 32.7017 55.1386C32.7975 55.1386 32.8956 55.1433 32.996 55.1504V59.243C32.0546 59.3276 31.3678 59.015 30.8959 58.2886V58.2909ZM53.4045 71.0437L50.3372 75.3244L48.6621 73.6436C48.9074 73.4931 49.1551 73.338 49.4074 73.1805C50.5497 72.4706 51.7318 71.7348 52.9466 71.0202C53.0097 70.9826 53.2013 71.0108 53.3718 71.0366C53.3811 71.0366 53.3928 71.039 53.4045 71.0413V71.0437ZM46.4428 76.1236L44.9641 77.5317L43.6044 76.2106L45.0482 74.7343L46.4405 76.1236H46.4428ZM36.6871 71.0131C36.8296 70.999 37.0282 70.9826 37.0703 71.0037C38.4089 72.1556 39.897 72.9924 41.4972 73.4908L39.6727 75.275L36.6241 71.0178C36.6451 71.0178 36.6638 71.0131 36.6848 71.0131H36.6871ZM39.4251 79.4334C39.9741 79.4946 40.5231 79.1302 41.4972 78.2886C41.5883 78.3944 41.6794 78.5002 41.7706 78.6083C42.1584 79.0597 42.5578 79.5251 42.9223 80.0094C42.9783 80.0846 42.9947 80.3314 42.9363 80.4631C41.9341 82.7245 41.0207 84.732 40.1399 86.5961C40.0862 86.7113 39.8339 86.8665 39.7007 86.8688C37.5071 86.89 35.2855 86.8876 33.1362 86.8876H31.557L31.5476 86.8782C31.5663 86.0273 31.5757 85.1622 31.585 84.323C31.606 82.2261 31.6294 80.0564 31.8163 77.9407C31.9565 76.3328 32.7391 74.7743 34.2692 73.0536L35.2761 74.4593C36.1008 75.6112 36.9114 76.7395 37.7174 77.8702C38.3598 78.7705 38.827 79.3653 39.4274 79.4358L39.4251 79.4334ZM44.9991 70.9026C44.9664 70.9026 44.9337 70.9026 44.9033 70.9026C40.0418 70.8533 36.1942 66.9534 36.1428 62.0263C36.1171 59.5063 36.1241 57.0357 36.1288 54.4169C36.1311 53.3379 36.1335 52.2519 36.1358 51.1518C38.521 51.2787 40.4039 50.5688 41.885 48.9914C45.9312 53.07 50.6198 51.9863 53.8998 50.7098C53.8998 50.7239 53.9021 50.7357 53.9021 50.7474C53.9021 51.8781 53.9068 53.0112 53.9138 54.1443C53.9278 56.7794 53.9395 59.5016 53.8811 62.1767C53.7713 67.0874 49.8793 70.9026 44.9991 70.9026ZM39.8082 46.5702C39.1588 47.6022 38.1753 48.0441 36.5236 48.0441H36.5049C35.1219 48.164 33.9141 47.6045 33.1899 46.5161C32.4657 45.423 32.398 44.0008 33.0124 42.8043C33.7039 41.4597 35.1032 41.0953 35.8765 40.9989C36.0938 40.9707 36.3134 40.959 36.5306 40.959C37.6917 40.959 38.8107 41.3327 39.341 41.9369C39.5162 42.1343 39.5793 42.534 39.6003 42.6633C39.7568 43.6482 39.904 44.612 39.9881 45.5923C40.0185 45.9613 39.9484 46.3469 39.8082 46.5702ZM46.5947 86.843H43.4549L45.0225 83.3921L46.5947 86.843ZM46.9988 48.5448C44.3099 47.99 42.4667 45.3901 42.7144 42.4987C42.8709 40.6628 44.1464 39.3276 45.9663 39.0972C46.3447 39.0478 46.7372 39.0431 47.0712 39.0408C49.0383 39.0408 51.0029 39.0384 52.97 39.0384C53.6661 39.0384 54.3623 39.0384 55.0585 39.0384H58.593C58.4342 41.6971 57.4834 43.8645 55.6892 45.6558C53.1101 48.2298 50.1876 49.2007 47.0012 48.5448H46.9988ZM59.2074 58.2087C58.7823 58.9633 58.1095 59.2806 57.0372 59.2195V55.1786C57.8572 55.0375 58.5136 55.2844 59.0392 55.9285C59.5509 56.5585 59.6186 57.4752 59.2051 58.2087H59.2074ZM50.0498 61.2341C50.6782 61.9369 50.5754 62.8302 49.7835 63.5636C48.4986 64.7531 46.8937 65.3878 45.0154 65.4489H44.9991C43.1068 65.3855 41.4972 64.732 40.2147 63.5049C39.4835 62.8067 39.397 61.9181 39.9904 61.2411C40.5651 60.5853 41.4739 60.5618 42.2541 61.1847C44.1674 62.715 45.8751 62.7174 47.7908 61.1917C48.5804 60.5618 49.4681 60.5806 50.0521 61.2341H50.0498ZM40.787 57.1226C40.7029 56.3351 40.7029 55.4936 40.787 54.6191C40.8665 53.7705 41.4762 53.2392 42.3476 53.2698C43.1863 53.298 43.7399 53.8316 43.8264 54.699C43.8544 54.9741 43.8474 55.2491 43.8404 55.5147C43.838 55.6323 43.8334 55.7498 43.8334 55.865C43.8334 55.9802 43.8357 56.0954 43.8404 56.2129C43.8474 56.4809 43.8544 56.7559 43.8264 57.0333C43.7329 57.9054 43.1956 58.4226 42.3546 58.4555C42.3266 58.4555 42.3009 58.4555 42.2728 58.4555C41.4412 58.4555 40.8758 57.9501 40.787 57.1226ZM46.2396 57.1391C46.1508 56.3633 46.1485 55.5218 46.2302 54.6379C46.3073 53.8128 46.896 53.2862 47.7651 53.2627C48.578 53.2486 49.2088 53.8786 49.2695 54.7813C49.2859 55.0375 49.2812 55.2938 49.2766 55.5429C49.2766 55.6464 49.2719 55.7498 49.2719 55.8509C49.2719 55.9731 49.2719 56.093 49.2766 56.2152C49.2812 56.4738 49.2859 56.7442 49.2649 57.0098C49.1948 57.8913 48.6808 58.4038 47.8188 58.4508C47.7791 58.4508 47.7417 58.4531 47.7043 58.4531C46.889 58.4531 46.3307 57.9571 46.2372 57.1367L46.2396 57.1391Z" fill="white"></path>
</svg>',
            'title' => 'Proactiveness',
            'description' => 'We don\'t wait to be told to do something. We lead from the front, use our initiative and follow-through'
        ],
        [
            'icon' => '<svg class="icon" viewBox="0 0 90 90">        <path d="M74.5528 87.4897C75.1658 88.9174 74.5411 89.8661 73.0005 89.8802C69.4849 89.9108 65.9694 89.9319 62.4538 89.9413C53.7224 89.9648 44.9933 89.9836 36.2619 90C34.8059 90.0023 34.4912 89.7487 34.0849 88.375C33.3334 85.8365 32.3377 83.4084 30.8136 81.2221C30.6304 80.9591 30.4331 80.7008 30.2288 80.4519C28.6413 78.524 28.4839 78.531 26.1214 79.2684C22.9135 80.2687 19.6469 81.0906 16.4554 82.1403C13.2263 83.2017 9.4101 81.8233 8.64217 77.1291C8.43551 75.861 8.51066 74.499 8.71262 73.2216C9.01557 71.303 8.99678 71.0471 7.36698 70.089C5.52348 69.0041 5.27689 68.3489 5.84756 66.2589C5.90157 66.057 5.77945 65.7071 5.61741 65.5615C3.54846 63.7275 3.47096 63.3212 4.81895 60.8861C6.14346 58.4932 6.06361 58.1809 3.6377 56.9245C2.07365 56.1167 0.671647 55.1633 0 53.435V52.2045C0.657557 51.1079 1.29633 49.9995 1.97737 48.9193C3.94064 45.8031 6.04013 42.7668 7.8531 39.5661C9.1236 37.3258 9.4923 37.6922 7.17676 35.6092C5.46711 34.0711 4.91993 32.1761 5.84521 30.1425C7.22138 27.1179 8.03628 23.9266 9.00383 20.7681C11.7022 11.9574 17.1575 5.65699 26.2882 3.23592C30.769 2.0477 35.393 1.38783 39.963 0.5448C41.4496 0.270052 42.9713 0.176121 44.4767 0H49.3989C50.1786 0.112717 50.9559 0.225434 51.7356 0.338152C52.0245 0.380421 52.318 0.413296 52.6022 0.4767C53.5674 0.688045 54.0958 1.37374 53.9455 2.20738C53.7952 3.04806 53.0625 3.50128 52.102 3.42378C50.1504 3.2641 48.1895 2.98466 46.2403 3.03397C41.5482 3.15608 36.9007 3.80421 32.3283 4.84684C29.4867 5.49496 26.6357 6.23702 23.9257 7.29139C18.3059 9.48233 14.9265 13.9229 12.665 19.3193C12.1906 20.4464 11.7891 21.6253 11.5237 22.8158C10.9295 25.4858 10.0489 28.0407 8.91459 30.5252C8.09969 32.3076 8.22181 32.5306 9.69426 33.8269C10.2156 34.2848 10.7182 34.8014 11.0939 35.3791C11.9323 36.673 12.0075 38.0186 11.31 39.4557C9.31147 43.584 6.90669 47.4469 4.10738 51.0773C2.66076 52.9512 2.5175 52.7282 4.73676 54.1066C5.18296 54.3837 5.67143 54.595 6.12702 54.8557C8.53884 56.2365 9.26685 58.4368 8.14196 60.9871C7.86015 61.6234 7.51493 62.234 7.1932 62.8657C7.92825 63.5161 8.76664 64.0586 9.33026 64.8053C10.0371 65.74 9.10011 66.529 8.71262 67.3368C8.86997 67.4495 8.95686 67.527 9.05549 67.581C11.5589 69.0041 12.3362 70.7582 11.6646 73.6043C11.3593 74.8935 11.3616 76.1452 11.7914 77.4062C12.355 79.057 13.5738 79.7192 15.2858 79.32C21.5091 77.8664 27.4859 75.6309 33.5072 73.5433C35.0243 73.0173 36.5508 72.4725 37.9786 71.7515C38.9767 71.249 39.9325 70.5351 40.6863 69.7156C41.1936 69.1637 41.4049 68.2784 41.5881 67.5059C41.8159 66.5431 42.3443 65.9983 43.2461 66.0711C44.0751 66.1368 44.5988 66.7709 44.5776 67.7243C44.5424 69.4174 43.8684 70.8428 42.6966 72.0427C40.9094 73.872 38.6573 74.9123 36.3042 75.7647C34.766 76.3213 33.2113 76.8379 31.5768 77.4015C31.7717 77.6575 31.9173 77.8641 32.0794 78.0567C34.1154 80.4871 35.6325 83.2064 36.5601 86.2404C36.7292 86.7946 36.9406 87.0224 37.57 87.0177C48.3797 86.9566 59.1895 86.9261 70.0016 86.8885C70.2036 86.8885 70.4032 86.8603 70.6239 86.8463C70.6169 86.7335 70.6404 86.6349 70.6004 86.5715C67.6109 81.7458 66.5541 76.3354 65.8473 70.8216C65.7322 69.9222 66.2723 69.2107 67.0849 69.0886C67.942 68.9594 68.6043 69.4737 68.8016 70.4506C69.3605 73.2286 69.8349 76.0277 70.4901 78.7846C71.0607 81.1869 72.0799 83.4248 73.482 85.4819C73.907 86.1065 74.2569 86.7946 74.5552 87.4897H74.5528ZM58.5202 5.68752C58.5272 6.8053 59.0862 7.4863 59.9856 7.50039C60.9109 7.51448 61.505 6.8006 61.5121 5.6429C61.5215 4.44294 61.4745 3.24062 61.5262 2.04535C61.5661 1.13187 61.2608 0.460262 60.4717 0.00469655H59.5934C58.7973 0.39451 58.4662 1.01445 58.5037 1.91619C58.5554 3.17252 58.5108 4.43119 58.5178 5.68987L58.5202 5.68752ZM78.8739 25.6244C80.8419 34.2308 78.3667 41.5175 71.2697 47.0547C69.9358 48.095 68.656 49.2245 67.4724 50.4315C66.8688 51.0468 66.5306 51.9227 66.0375 52.7376C68.4211 53.8812 69.1491 55.7222 69.0153 58.0306C68.98 58.6435 69.0223 59.2611 69.0059 59.8763C68.9589 61.7268 68.144 63.1028 66.5142 64.0281C66.2394 64.1831 66.0163 64.6386 65.9835 64.9768C65.6876 67.9051 63.4636 70.2017 60.6032 70.4717C57.71 70.7441 55.0516 68.9383 54.2437 66.1509C54.0065 65.3314 53.0178 63.9365 52.5012 63.2719C51.9423 62.5533 51.2894 61.7291 51.172 60.8814C50.9559 59.3362 50.9865 57.73 51.1344 56.1684C51.2871 54.5622 52.3814 53.5172 54.0042 52.7141C53.4194 51.8147 52.9944 50.8519 52.2969 50.1615C51.0334 48.9099 49.6526 47.7616 48.2459 46.672C37.5253 38.3755 37.9715 21.5971 49.1711 13.9769C61.2444 5.76267 76.0347 13.2184 78.8739 25.6244ZM62.9188 64.547H57.0289C57.2426 66.4257 58.5014 67.5787 60.1617 67.4941C61.7469 67.4143 62.9399 66.1579 62.9211 64.547H62.9188ZM65.9882 57.1312C65.9506 56.0439 65.4245 55.5344 64.3349 55.5203C62.9023 55.5015 61.4674 55.5156 60.0326 55.5156C58.5695 55.5156 57.1064 55.5015 55.6434 55.5203C54.5842 55.5344 54.0253 56.0885 53.9948 57.15C53.969 58.0564 53.9666 58.9652 53.9948 59.8716C54.03 60.9166 54.6054 61.4943 55.6622 61.5013C58.5601 61.5201 61.4557 61.5225 64.3537 61.5013C65.4128 61.4943 65.9529 60.9307 65.9858 59.8528C66.014 58.9464 66.0163 58.0376 65.9858 57.1312H65.9882ZM55.4931 31.427C55.3991 30.5276 54.7486 29.9828 53.9009 30.0321C53.1517 30.0744 52.5904 30.6239 52.5341 31.3683C52.4683 32.2207 53.0014 32.8852 53.8962 32.9862C54.3823 33.0402 54.8801 32.9956 55.5025 32.9956C55.5025 32.4038 55.5447 31.9107 55.4931 31.4246V31.427ZM61.451 52.4675V36.0883H58.546V52.4675H61.451ZM64.5204 33.052C65.2367 33.005 65.8261 33.0355 66.3874 32.9158C67.1553 32.7514 67.6109 31.9929 67.4818 31.2579C67.3573 30.5346 66.7396 30.0227 65.9975 30.025C65.2155 30.0274 64.6002 30.5581 64.5274 31.3542C64.4805 31.8684 64.5181 32.3921 64.5181 33.0496L64.5204 33.052ZM73.7262 21.2942C68.9918 14.0004 60.1007 11.3915 52.7924 15.3272C41.008 21.6746 40.738 37.3845 50.2538 44.4528C52.0245 45.7679 53.5486 47.4187 55.1901 48.9099C55.2606 48.9756 55.3639 49.0061 55.4696 49.0625V36.0343C53.9666 36.0343 52.4918 36.0578 51.2659 35.0644C50.0119 34.05 49.4318 32.749 49.5375 31.1381C49.6831 28.9307 51.4937 27.1508 53.7177 27.0286C54.8778 26.9652 55.9181 27.2729 56.8246 28.0008C58.4145 29.2759 58.6235 31.0512 58.5131 32.9627H61.4534C61.5097 32.1103 61.4675 31.3072 61.6271 30.5464C62.125 28.191 64.2315 26.768 66.6645 27.0615C68.8838 27.3292 70.5652 29.3581 70.4924 31.6876C70.4196 33.9819 68.5832 35.8394 66.2066 35.9991C65.6641 36.0343 65.1169 36.0037 64.5721 36.0037V48.6891C65.3001 48.0621 66.1737 47.2849 67.0755 46.5428C68.0007 45.7796 68.9378 45.0282 69.9006 44.3119C76.7157 39.2514 78.6907 28.9354 73.7262 21.2918V21.2942ZM16.6972 26.7938C16.9203 26.0705 17.0519 25.3168 17.2374 24.5817C18.1674 20.9043 19.8018 17.6121 22.6575 15.043C24.3789 13.4932 26.4197 12.4811 28.653 11.8752C29.4397 11.6615 29.9376 11.213 30.0245 10.431C30.0362 9.36492 29.1133 8.60173 28.0988 8.94693C26.5559 9.47059 24.9707 9.98017 23.5664 10.7809C17.6131 14.1718 15.0533 19.7725 13.8322 26.1809C13.6795 26.977 14.2314 27.6533 14.9782 27.7965C15.7649 27.9468 16.4554 27.5781 16.6972 26.7962V26.7938ZM79.2849 8.65809C78.0872 9.77117 76.9059 10.9124 75.8327 12.1453C75.5486 12.4717 75.5086 13.2349 75.6824 13.667C75.8539 14.0943 76.4386 14.3574 76.8097 14.665C77.2887 14.4654 77.6457 14.4137 77.8594 14.2094C79.0407 13.0775 80.2196 11.9386 81.3351 10.7434C81.8775 10.1633 81.7531 9.29212 81.2012 8.76141C80.6728 8.25419 79.8391 8.14382 79.2849 8.66044V8.65809ZM40.9329 8.83656C40.6534 8.5712 40.2425 8.4444 39.7094 8.15086C39.2867 8.52424 38.7817 8.82247 38.4929 9.26394C38.1101 9.84632 38.3637 10.4592 38.8264 10.9336C39.8268 11.9598 40.8343 12.9766 41.8605 13.9746C42.6073 14.7002 43.4856 14.7119 44.1103 14.0544C44.6927 13.4415 44.6598 12.568 43.9788 11.8658C42.9807 10.8373 41.9732 9.82049 40.9329 8.83656ZM88.54 28.5104C87.0182 28.4658 85.4941 28.4658 83.9723 28.5104C83.0846 28.5362 82.4717 29.2501 82.5116 30.065C82.5515 30.8563 83.1574 31.4457 84.017 31.4786C84.7473 31.5068 85.4824 31.4833 86.2127 31.4833C86.9149 31.4857 87.6194 31.4857 88.3216 31.4857C88.3803 31.4857 88.439 31.4857 88.4977 31.481C89.3666 31.4223 89.9608 30.8587 89.9984 30.0626C90.0359 29.2501 89.423 28.5339 88.54 28.5081V28.5104ZM31.403 28.5198C30.5506 28.548 30.0104 29.2313 30.0339 30.0462C30.055 30.8681 30.6163 31.4387 31.497 31.4763C32.2273 31.5068 32.96 31.4833 33.6927 31.481C34.4841 31.481 35.2756 31.5091 36.0623 31.4716C36.8936 31.434 37.4361 30.8704 37.476 30.0837C37.516 29.2666 36.9876 28.5574 36.1445 28.5245C34.5663 28.4634 32.9835 28.4658 31.403 28.5198ZM12.0028 31.4645C11.9816 32.2653 12.6791 32.9909 13.4846 33.0027C14.3019 33.0144 14.9782 32.331 14.9759 31.4951C14.9759 30.6403 14.33 30.0133 13.4729 30.0274C12.6744 30.0391 12.0262 30.6755 12.0028 31.4645Z" fill="white"></path>
</svg>',
            'title' => 'Openness',
            'description' => 'We remain open to learning, feedback, embracing new ideas, sharing information, and understanding different perspectives'
        ],
    ];

    foreach ($values as $value) {
        echo '<div class="value-card">';
        echo '<div class="icon-circle">' . $value['icon'] . '<div class="icon-text">' . $value['title'] . '</div></div>';
        echo '<h3 class="value-title">' . $value['title'] . '</h3>';
        echo '<p class="value-description">' . $value['description'] . '</p>';
        echo '</div>';
    }
    ?>
</div>

                <!-- Company Values Semicircle Layout -->
<section class="value-arc-wrapper">
    <input type="radio" name="value" id="v1" class="value-radio">
    <label for="v1" class="value-label" data-heading="Integrity" data-description="We do great work because we want to, not because someone is watching us">
        <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M84.2393 45.604C78.2873 43.7563 72.3489 41.8677 66.3878 40.0495C65.736 39.852 64.9213 39.8543 64.2695 40.0518C58.9534 41.6679 53.66 43.3522 48.3598 45.0138C48.1199 45.0887 47.8755 45.1432 47.6039 45.2158C47.3912 44.41 47.536 43.8607 48.2466 43.3273C50.8899 41.3411 52.7593 38.7488 53.8411 35.6141C54.0515 35.0034 54.3231 34.8128 54.9704 34.8105C58.6208 34.7923 61.4338 32.3567 61.9724 28.8179C62.5767 24.8524 59.5622 21.1251 55.5656 20.8959C55.346 20.8823 55.1288 20.8618 54.821 20.8369C54.821 19.8426 54.8255 18.887 54.821 17.9314C54.7667 9.95258 49.5683 3.34256 41.8533 1.44944C41.1472 1.27693 40.4298 1.14981 39.7169 1H36.3268C35.3966 1.20656 34.4529 1.37 33.5341 1.62423C26.3011 3.63538 21.3064 10.1614 21.225 17.6862C21.2136 18.7258 21.225 19.7655 21.225 20.7688C20.4012 20.9118 19.6657 20.9708 18.9709 21.1728C15.7459 22.1103 13.5869 25.3722 14.026 28.5818C14.5239 32.2183 17.3075 34.7628 20.9262 34.8037C21.6572 34.8128 22.0012 34.9853 22.2434 35.7139C23.0128 38.0338 24.3254 40.0381 26.0318 41.7837C26.8217 42.5918 27.9487 43.266 28.3221 44.233C28.7204 45.268 28.4126 46.5801 28.4126 47.7695C28.4126 47.7967 28.4081 47.8262 28.4126 47.8535C28.4805 48.3483 28.2565 48.5345 27.7699 48.6298C23.1418 49.5241 20.8267 50.2891 18.7106 51.3446C13.5733 53.9051 4.01167 60.0611 4.00261 68.9933C4.00035 72.3936 3.99809 75.7962 4.00261 79.1966C4.00261 80.6493 4.61818 81.2667 6.06432 81.269C9.08785 81.2736 12.1114 81.269 15.1349 81.269C16.8866 81.269 27.1045 81.2758 32.828 81.269C32.8913 81.269 32.9547 81.269 33.0181 81.269C33.9595 81.269 34.7697 81.269 35.3898 81.2667C40.7399 81.2667 46.0921 81.2622 51.4422 81.2872C51.8382 81.2872 52.3225 81.4824 52.6167 81.7502C55.9163 84.7829 59.7365 86.819 64.0885 87.8382C64.7018 87.9812 65.3965 88.0697 65.9963 87.929C75.6711 85.659 82.0146 79.7368 84.9906 70.244C85.4931 68.6414 85.6719 66.9367 86 65.282V46.9137C85.6198 46.1942 84.9974 45.8355 84.237 45.5994L84.2393 45.604ZM54.8617 24.3008C56.3531 24.2463 57.4507 24.8319 58.1659 26.0509C58.9037 27.3084 58.8584 28.5977 58.055 29.8189C57.3149 30.9448 56.2354 31.4215 54.8617 31.426V24.3008ZM21.1797 31.4034C19.8331 31.4601 18.7853 30.9698 18.0385 29.9075C17.2011 28.718 17.1106 27.4242 17.8167 26.1439C18.5183 24.8728 19.6227 24.2531 21.1797 24.3008V31.4011V31.4034ZM25.0768 14.304C26.8013 8.1775 32.355 4.14384 38.7415 4.41623C44.7705 4.67501 50.0073 9.25798 51.2 15.3051C51.3064 15.843 51.2272 16.2562 50.8334 16.6874C47.5903 20.2467 43.5665 22.108 38.7777 22.1829C34.2289 22.2533 29.68 22.2011 25.1311 22.2011C24.9953 22.2011 24.8573 22.1784 24.597 22.158C24.7034 19.4772 24.3549 16.869 25.0768 14.304ZM24.9116 32.6654C24.4771 30.4159 24.6174 28.0552 24.4861 25.606C25.0813 25.606 25.4434 25.606 25.8055 25.606C30.4924 25.5833 35.1816 25.6491 39.864 25.5129C43.9014 25.3949 47.4975 23.8944 50.7021 21.4384C50.8809 21.3022 51.0642 21.1705 51.2475 21.0389C51.2634 21.0275 51.2973 21.0457 51.3878 21.0639C51.3992 21.2886 51.424 21.5292 51.424 21.7721C51.4263 24.4914 51.4376 27.2108 51.424 29.9279C51.3833 36.8239 46.2415 42.6939 39.6875 43.3454C32.5066 44.0582 26.2627 39.6409 24.9116 32.6632V32.6654ZM44.1051 50.3413C44.1006 50.5343 43.863 50.7658 43.6774 50.8997C40.4321 53.2673 35.3921 53.2332 32.2396 50.8112C32.0268 50.6478 31.8232 50.3254 31.8141 50.0689C31.7734 48.6548 31.7937 47.2383 31.7937 45.6789C35.9534 47.2202 39.9862 47.2315 44.121 45.7175C44.121 47.3132 44.1368 48.8273 44.1006 50.3413H44.1051ZM48.138 77.8732C43.3017 77.8732 38.4654 77.8732 33.6314 77.8732C33.5182 77.8732 33.4051 77.8732 33.2874 77.8732C33.095 77.8732 32.9027 77.8664 32.7103 77.8732C25.509 77.8709 12.0752 77.8732 8.36591 77.8732H7.39503C7.39503 74.6681 7.3724 71.556 7.39956 68.444C7.45614 61.9679 14.5555 56.9264 18.421 54.9107C20.5936 53.778 23.0671 53.0039 27.8061 52.0937C28.1162 52.0347 28.5733 52.214 28.8223 52.4387C32.2622 55.5077 36.2317 56.6858 40.7466 55.7574C42.0525 55.4895 43.304 54.947 44.6279 54.5134C44.6279 57.4167 44.6211 60.3562 44.6279 63.2958C44.6415 68.3305 46.0333 72.9543 48.7739 77.1695C48.9029 77.3693 49.0116 77.5827 49.1768 77.8709H48.138V77.8732ZM82.6053 63.3049C82.5601 73.3379 75.9404 81.8932 66.2543 84.3992C65.6613 84.5536 64.9598 84.5513 64.3668 84.3992C54.6829 81.8932 48.0543 73.3243 48.0203 63.3026C48.0045 58.4608 48.0181 53.6168 48.0181 48.6797C52.4583 47.2883 56.842 45.915 61.2279 44.5417C62.3051 44.2034 63.3688 43.813 64.4619 43.5497C65.0028 43.4203 65.6432 43.384 66.166 43.5452C71.4527 45.1614 76.7235 46.8252 81.9965 48.4777C82.1821 48.5367 82.3632 48.6139 82.6076 48.7047C82.6076 49.4583 82.6076 50.221 82.6076 50.9814C82.6076 55.09 82.6234 59.1963 82.6053 63.3049ZM77.7939 50.734C73.8651 49.4946 69.9341 48.2598 65.994 47.0635C65.5663 46.9342 65.0299 46.9432 64.5999 47.0726C60.6847 48.2621 56.7831 49.4878 52.8792 50.7136C51.768 51.0632 51.4127 51.5762 51.4105 52.7883C51.4059 54.6882 51.4105 56.5859 51.4105 58.4858C51.4105 60.102 51.4037 61.7159 51.4105 63.3321C51.4444 71.388 56.5998 78.4362 64.2673 80.8332C64.8941 81.0284 65.6817 81.0398 66.3109 80.8514C73.8154 78.5973 79.1269 71.497 79.2084 63.6408C79.2468 60.0135 79.2197 56.3861 79.2174 52.7565C79.2174 51.592 78.8712 51.0722 77.7962 50.734H77.7939ZM75.825 59.3189C75.825 59.5141 75.911 64.1175 75.4335 66.4192C74.3155 71.808 71.0114 75.4671 65.865 77.4101C65.5346 77.535 65.082 77.51 64.7402 77.3943C59.13 75.5238 54.9681 69.924 54.8278 63.9722C54.7508 60.7444 54.8165 57.5143 54.7893 54.2842C54.7848 53.7825 54.9771 53.6191 55.423 53.4806C58.5506 52.5227 61.6624 51.5194 64.7923 50.566C65.1408 50.4594 65.5799 50.4821 65.9352 50.5887C68.9836 51.5149 72.0184 52.4909 75.0669 53.4125C75.6553 53.5896 75.8612 53.8484 75.8431 54.4726C75.7956 56.0865 75.8273 57.7027 75.8273 59.3166L75.825 59.3189ZM72.0003 61.1984C69.6557 63.6068 67.2772 65.9834 64.8806 68.3373C64.2559 68.9501 63.2896 68.9161 62.6536 68.3009C61.2935 66.9867 59.9515 65.6497 58.6479 64.2786C57.9713 63.5659 58.0527 62.5649 58.7339 61.9225C59.3812 61.3119 60.3498 61.2687 61.0287 61.9225C61.9385 62.8032 62.7849 63.7498 63.7377 64.7553C65.7157 62.7601 67.5397 60.9101 69.3774 59.0737C70.1468 58.3042 71.0838 58.2089 71.7989 58.7922C72.5503 59.4029 72.7019 60.4833 72.0026 61.2006L72.0003 61.1984Z" fill="white"></path>        <span class="value-text">Integrity</span>
    </label>

    <input type="radio" name="value" id="v2" class="value-radio">
    <label for="v2" class="value-label" data-heading="Respect" data-description="We treat everyone fairly and respect all views and perspectives, even when we disagree">
        <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M55.1754 20.0269C52.2406 18.3692 49.2258 18.4091 46.218 20.1468C45.3872 20.6265 44.6717 20.6195 43.8221 20.1163C42.41 19.2839 40.8402 18.8606 39.261 18.8606C38.136 18.8606 37.0087 19.0746 35.9284 19.5072C33.3631 20.5348 31.4638 22.6464 30.7178 25.2988C29.9058 28.1839 30.4212 31.0103 32.2899 33.9378C34.8388 37.9282 38.7221 41.0367 44.5117 43.722C44.7635 43.8396 45.4343 43.7996 45.9238 43.5739C49.8259 41.7798 52.9749 39.5718 55.5567 36.823C58.3033 33.9002 59.6236 30.9868 59.7083 27.6572C59.6024 24.2336 58.0773 21.6658 55.1731 20.0246L55.1754 20.0269ZM54.2858 32.969C52.3418 35.5509 49.6635 37.7071 45.8602 39.7599C45.6273 39.8845 45.3425 39.948 45.053 39.948C44.7635 39.948 44.4646 39.8822 44.2246 39.7528C40.4448 37.7283 37.783 35.5956 35.8484 33.0419C34.8011 31.6593 33.4643 29.5101 33.9397 26.8201C34.3186 24.6756 35.6884 23.1566 37.7971 22.5429C39.8541 21.9457 41.7675 22.4888 43.3279 24.1137C43.4102 24.2007 43.4926 24.29 43.575 24.3794C43.7703 24.5933 43.9539 24.7956 44.1304 24.9155C45.2272 25.6562 46.1733 24.7767 46.531 24.3629C48.0231 22.6346 49.9295 21.9833 52.0453 22.4818C54.5635 23.0767 56.2392 25.2447 56.218 27.8759C56.1969 29.5995 55.5638 31.2619 54.2811 32.9643L54.2858 32.969ZM29.3668 8.82952C28.8255 8.23697 28.7338 7.6256 29.0821 6.90137C29.3339 6.37466 29.7222 6.02195 30.8119 5.91378C30.859 5.9373 30.9084 5.96081 30.9602 5.98432C31.2779 6.13246 31.6709 6.31822 31.9439 6.55336C32.7983 7.29641 33.6149 8.14762 34.4034 8.97296L34.6222 9.20105C35.3989 10.0123 35.4318 11.0704 34.7023 11.7711C33.9821 12.4624 32.9654 12.4154 32.1699 11.6489C31.2826 10.7929 30.28 9.82417 29.3692 8.82952H29.3668ZM38.8397 61.3176C34.8082 57.2543 30.7084 53.17 27.2275 49.711C26.0225 48.5118 24.714 47.8487 23.3419 47.7359C22.3958 47.6606 21.4261 47.7076 20.3976 47.7594C19.9293 47.7829 19.4445 47.8064 18.9526 47.8205L17.9688 47.844V46.8611C17.9688 46.6754 17.9712 46.4685 17.9735 46.2498C17.9806 45.7466 17.9876 45.1752 17.9641 44.6132C17.8064 40.8039 14.568 37.6248 10.7459 37.5261C10.6776 37.5261 10.607 37.5237 10.5388 37.5237C8.55712 37.5237 6.70255 38.2644 5.2928 39.6259C3.8501 41.0179 3.04284 42.9037 3.02401 44.9353C3.00283 47.2209 3.00754 49.5488 3.01225 51.7991C3.01695 54.1058 3.02166 56.4925 3.00048 58.8415C2.97694 61.2776 3.82185 63.3069 5.58699 65.0469C7.66043 67.0903 9.7527 69.183 11.7744 71.2076C13.7443 73.178 15.7824 75.2167 17.7993 77.206C18.5336 77.9302 18.8561 78.725 18.8396 79.7761C18.8043 82.1745 18.8137 84.6176 18.8231 86.9784L18.8278 88.0131C18.8302 88.916 19.1691 89.9906 20.7601 89.9929C27.2157 90.0024 33.2054 90.0024 39.0751 89.9929C40.2824 89.9929 40.9767 89.2922 40.9791 88.0789C40.9885 80.1382 40.9885 73.0958 40.9791 66.5494C40.9767 64.5037 40.2565 62.7449 38.8444 61.3199L38.8397 61.3176ZM37.51 80.7049C37.51 82.3086 37.51 83.9146 37.51 85.53V86.4894H22.4122L22.351 85.5958C22.3463 85.5182 22.3393 85.4406 22.3322 85.363C22.3157 85.1702 22.2993 84.9703 22.2993 84.754C22.2993 84.1662 22.2922 83.5807 22.2875 82.9928C22.2734 81.502 22.2569 79.9595 22.3204 78.4334C22.3675 77.3118 22.0357 76.51 21.1743 75.6635C18.9408 73.4696 16.6908 71.2193 14.5162 69.0443C12.4475 66.9774 10.3105 64.8376 8.19232 62.7543C6.99203 61.5715 6.44366 60.2406 6.46485 58.5594C6.50015 55.7988 6.49544 52.9889 6.49073 50.2707C6.48838 48.5212 6.48603 46.7718 6.49073 45.0223C6.50015 42.9225 8.00169 41.2342 10.061 41.0108C12.1086 40.7898 13.9796 42.1442 14.4174 44.1664C14.5091 44.5897 14.5091 45.0059 14.5091 45.3703L14.5138 47.5877C14.5209 49.9133 14.528 52.3187 14.5068 54.6866C14.4856 57.031 15.2834 58.9615 16.945 60.5839C17.7311 61.3528 18.5148 62.1406 19.275 62.9024C19.8751 63.5044 20.4753 64.1087 21.0801 64.7059C21.9415 65.5571 22.9747 65.6253 23.7161 64.8847C24.4621 64.1369 24.3868 63.1258 23.5184 62.2464C22.7582 61.4751 21.9886 60.7109 21.2214 59.9443C20.6118 59.3377 20.0022 58.731 19.3974 58.1197C17.6017 56.3114 17.5028 53.8895 19.155 52.2317C19.9669 51.4182 21.0095 50.9926 22.0945 51.0419C23.163 51.0866 24.1868 51.5686 25.0529 52.4293C26.4791 53.8495 27.903 55.2745 29.3268 56.6971C31.525 58.8956 33.7985 61.1671 36.0438 63.3915C37.0464 64.3862 37.5312 65.5501 37.5265 66.9515C37.5076 71.5203 37.51 76.1878 37.5123 80.7025L37.51 80.7049ZM43.2431 2.57949C43.149 1.29562 43.5256 0.512606 44.5023 0H45.4931C46.4675 0.512606 46.8464 1.29562 46.7522 2.57949C46.6957 3.33664 46.7099 4.08674 46.7216 4.81332C46.7287 5.26715 46.7358 5.69275 46.7287 6.12306C46.7099 7.18119 45.9803 7.9501 44.9989 7.9501C44.0174 7.9501 43.2879 7.18119 43.269 6.12306C43.262 5.69745 43.269 5.25304 43.2761 4.82508C43.2879 4.09379 43.302 3.33664 43.2455 2.57479L43.2431 2.57949ZM55.2766 11.757C54.5682 11.054 54.5941 10.0193 55.3378 9.24102C56.4416 8.08883 57.3124 7.22351 58.1597 6.43109C58.5315 6.08543 58.9081 5.91378 59.3176 5.91378C59.5647 5.91378 59.8213 5.97492 60.0943 6.09954C60.6262 6.33939 60.9886 6.72267 61.1181 7.82783C61.0969 7.87485 61.0733 7.92188 61.0498 7.97126C60.8945 8.30046 60.6991 8.71195 60.4497 9.00118C59.7154 9.85003 58.8681 10.6566 58.0467 11.4372L57.7996 11.6724C57.0159 12.4178 55.978 12.453 55.2743 11.7547L55.2766 11.757ZM80.0497 37.5378C76.1617 37.2768 72.7703 39.9457 72.1513 43.7596C72.0336 44.4792 72.0101 45.2246 71.9866 46.0123C71.9748 46.3862 71.963 46.7624 71.9395 47.1339L71.8948 47.9052L71.1652 48.018C71.0758 48.0392 70.8945 48.0721 70.678 48.0227C70.4944 47.9804 70.3109 47.9357 70.1296 47.8863C69.3318 47.6724 68.5669 47.5642 67.835 47.5642C65.8957 47.5642 64.1776 48.319 62.6337 49.8427C60.0378 52.401 57.4183 55.0252 54.886 57.5624C53.6998 58.7498 52.516 59.9373 51.3275 61.1224C49.76 62.6884 48.9998 64.5484 49.0045 66.8128C49.014 71.2734 49.0116 75.7364 49.0116 80.197C49.0116 82.7647 49.0116 85.3301 49.0116 87.8978C49.0116 89.3463 49.6565 89.9906 51.0992 89.9929C57.0841 89.9976 63.0691 89.9976 69.0564 89.9929C70.5133 89.9929 71.1628 89.3534 71.1652 87.919V86.7903C71.1746 84.4295 71.1817 81.9864 71.1534 79.588C71.144 78.6662 71.4264 77.9702 72.0713 77.3306C74.639 74.7888 77.2372 72.1905 79.7531 69.6768C81.3653 68.0661 82.9775 66.4554 84.592 64.847C86.1759 63.2716 86.9525 61.5339 86.9667 59.5352C86.9972 55.2157 87.0231 50.0896 86.9667 44.8389C86.9243 41.0061 83.8883 37.7965 80.0544 37.5355L80.0497 37.5378ZM81.9866 62.5615C80.3674 64.1651 78.7294 65.8088 77.1455 67.3959C74.5095 70.0366 71.7842 72.7689 69.0612 75.4166C68.0044 76.4442 67.5996 77.4247 67.6679 78.7956C67.7455 80.3592 67.7291 81.9511 67.7102 83.4913C67.7032 84.1662 67.6961 84.8434 67.6961 85.5276V86.487H52.4783V80.3428C52.4807 75.9221 52.483 71.351 52.4713 66.8551C52.4689 65.5313 52.8925 64.4614 53.7704 63.589C57.5195 59.8503 61.264 56.1069 65.0084 52.3634C66.4888 50.882 68.0209 50.9103 69.0423 51.1924C70.5274 51.6039 71.5441 52.662 71.9018 54.1693C72.2337 55.5684 71.8242 56.8946 70.718 58.0044C70.1744 58.55 69.6283 59.0955 69.0823 59.6387C68.2304 60.4875 67.3784 61.3387 66.5311 62.1923C66.0887 62.6367 65.1802 63.7912 66.2793 64.8894C67.1783 65.7876 68.2633 65.3502 68.9788 64.6401C70.0402 63.5867 71.0969 62.5285 72.156 61.4704L73.168 60.457C74.7284 58.8956 75.4839 57.0639 75.4792 54.8512C75.4721 51.4534 75.4745 48.1356 75.4792 45.3374C75.4839 42.8308 77.1596 41.0014 79.466 40.9873H79.4919C80.5604 40.9873 81.5442 41.38 82.2667 42.0972C83.0645 42.8873 83.5046 44.023 83.5046 45.2975C83.5046 46.8376 83.5046 48.3778 83.5046 49.918C83.5023 52.8408 83.4999 55.8647 83.5211 58.8368C83.5329 60.3041 83.0292 61.5221 81.9843 62.5591L81.9866 62.5615Z" fill="white"></path>
        <span class="value-text">Respect</span>
    </label>

    <input type="radio" name="value" id="v3" class="value-radio">
    <label for="v3" class="value-label" data-heading="Growing together" data-description="We seek opportunities every day to get better as individuals and as a team">
        <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M84 13.1519V12.138C83.9954 10.4655 83.4767 9.95285 81.7738 9.95057C80.9981 9.95057 80.22 9.95057 79.4282 9.95057H78.6663V9.10979C78.6663 8.26902 78.6686 7.46925 78.6663 6.66949C78.6571 5.22262 78.0833 4.65754 76.6122 4.64843C75.5771 4.64387 74.5443 4.64387 73.5093 4.64387C72.768 4.64387 72.0267 4.64387 71.2854 4.64387C71.0949 4.64387 70.8952 4.6302 70.6863 4.61653C70.6818 4.61653 70.6772 4.61653 70.6726 4.61653C70.6726 4.58007 70.6726 4.54361 70.6726 4.50944C70.6749 4.22234 70.6795 3.97398 70.6657 3.71879C70.6152 2.67522 69.9542 1.97343 69.0224 2.00077C68.1274 2.01444 67.4962 2.6661 67.4182 3.65954C67.4021 3.86233 67.4044 4.06057 67.4067 4.25424C67.4067 4.32032 67.4067 4.38867 67.4067 4.45475V6.78341C67.4067 9.48575 67.409 12.2792 67.3906 15.0249C67.3906 15.2846 67.269 15.5854 67.1956 15.6355C65.488 16.5697 64.687 18.0052 64.749 20.0217C64.7743 20.8101 64.7674 21.6167 64.7605 22.3959C64.7605 22.6374 64.7559 22.879 64.7559 23.1182H60.8727C60.9025 22.9701 60.9346 22.822 60.9668 22.6739C61.1022 22.0427 61.2399 21.3888 61.2972 20.7326C61.373 19.869 61.3707 18.9029 61.2927 17.7842C61.093 14.9337 58.7727 12.6985 55.8947 12.5846C53.0006 12.4706 50.4737 14.5714 50.1432 17.3695C49.9252 19.2242 49.7806 21.1906 50.5357 23.1569C50.3773 23.1569 50.2213 23.1569 50.0675 23.1592C49.3721 23.1638 48.7134 23.1683 48.057 23.1478C46.6088 23.0977 45.397 23.6013 44.3505 24.6745C42.9849 26.0712 41.562 27.4679 40.2263 28.7667C39.9715 29.0151 39.4873 29.3591 39.1683 29.5209C36.7194 30.7513 34.2201 31.984 31.8011 33.1779L31.2365 33.4559C31.1837 33.4833 31.1286 33.5106 31.0736 33.5402C30.9037 33.6314 30.727 33.7225 30.6008 33.7248C28.6477 33.743 26.6762 33.7407 24.7667 33.7385H23.5297C23.5618 33.5698 23.594 33.4035 23.6261 33.2372C23.7592 32.5513 23.8877 31.9042 23.9359 31.2321C24.0048 30.3047 23.9933 29.3135 23.8992 28.1971C23.6582 25.3238 21.0832 23.0749 18.1661 23.1729C15.2078 23.2777 12.7658 25.6725 12.7222 28.5092C12.7176 28.8305 12.7039 29.1518 12.6924 29.4753C12.635 30.8835 12.5777 32.3326 13.1904 33.7453C13.1767 33.7453 13.1652 33.7453 13.1514 33.7476C12.798 33.7726 12.4629 33.7977 12.1301 33.8364C8.62096 34.2602 6.05508 37.0697 6.02754 40.5171C5.99082 45.0901 5.99082 50.0573 6.02754 55.7012C6.04131 57.9069 7.16589 59.5087 9.27964 60.3358C9.78456 60.534 10.3193 60.6593 10.8334 60.7824C10.9826 60.8188 11.1341 60.853 11.2832 60.8917V78.8557H10.6337C9.92685 78.8557 10.6888 78.8671 8.84817 78.842H8.82522C8.22621 78.8375 7.64327 79.0653 7.2095 79.4891C6.77573 79.9152 6.53704 80.4803 6.53704 81.0864V87.8286C6.53704 89.0248 7.51704 90 8.72424 90H80.8259C81.9299 90 82.8318 89.1273 82.8364 88.061V88.0336C82.8226 82.8226 82.8226 77.525 82.8226 72.4006C82.8226 69.8805 82.8226 67.3605 82.8226 64.8404C82.8226 63.5121 82.2144 62.9151 80.8489 62.906C79.2584 62.8946 77.7781 62.8946 76.3276 62.906C75.6253 62.8946 74.923 62.8968 74.1886 62.9037C73.9109 62.9037 73.6309 62.906 73.3463 62.9082V59.1122C73.3463 56.6035 73.3463 54.0926 73.3463 51.5839C73.3486 49.7565 72.589 48.4168 71.0214 47.4894C69.6605 46.6828 68.2857 45.8511 66.9546 45.0445C65.6923 44.2789 64.3864 43.4883 63.0943 42.7227C62.7294 42.5063 62.6261 42.3262 62.649 41.9571C62.6903 41.2941 62.6766 40.6242 62.6605 39.9748L62.6559 39.7971C62.6307 38.6214 61.9904 37.915 60.9759 37.9241C59.9891 37.9446 59.4153 38.6191 59.3969 39.7743C59.39 40.2801 59.39 40.7837 59.3923 41.3032C59.3923 41.4262 59.3923 41.5515 59.3923 41.6746H51.977V38.1839C51.977 35.641 51.977 33.0982 51.977 30.5553C51.977 30.0769 51.977 29.1859 51.1141 28.7097C50.1708 28.188 48.1259 28.491 47.341 29.2657C47.0495 29.5551 46.7511 29.8376 46.4528 30.1224C45.7459 30.7969 45.0138 31.4964 44.3597 32.2506C43.219 33.5698 41.7158 34.2739 40.123 35.019C39.4987 35.3106 38.8561 35.6114 38.2227 35.9577C38.2227 35.8848 38.2227 35.8142 38.225 35.7413C38.2319 35.1671 38.2387 34.6704 38.2066 34.16C38.1791 33.7111 38.2869 33.5243 38.6564 33.3898C40.6807 32.6493 42.4686 31.3642 44.2862 29.3432C44.5915 29.0037 44.9334 28.6756 45.2662 28.3588C45.6266 28.0148 45.9984 27.6593 46.338 27.2743C46.898 26.6385 47.5406 26.3811 48.4839 26.3788C53.2324 26.4153 58.0612 26.4107 62.7294 26.4061H66.0067C67.4228 26.4039 68.0034 25.8297 68.0126 24.4261C68.0172 23.5511 68.0172 22.6762 68.0172 21.8012C68.0172 21.1701 68.0172 20.5366 68.0172 19.9055C68.0172 19.3632 68.1411 18.9599 68.3683 18.7389C68.5336 18.5771 68.7585 18.5042 69.066 18.5087C69.4975 18.5179 70.069 18.6705 70.0713 19.885C70.0759 21.9038 70.0759 23.9203 70.0713 25.939C70.0713 27.7505 69.3644 28.4568 67.5536 28.4614C66.6057 28.4637 65.6579 28.4614 64.7123 28.4614C63.6267 28.4614 62.5412 28.4614 61.4556 28.4637C59.9799 28.4682 59.4015 29.0333 59.39 30.471C59.3809 31.5921 59.3832 32.6265 59.39 33.6314C59.3992 34.8185 60.0166 35.5613 60.9966 35.5658H61.0058C61.9812 35.5658 62.6261 34.8162 62.649 33.6496C62.6582 33.146 62.6559 32.6425 62.6536 32.1116C62.6536 31.9771 62.6536 31.8404 62.6536 31.7014C63.1356 31.7014 63.613 31.7014 64.0857 31.7014C65.3962 31.7014 66.6333 31.706 67.891 31.6969C70.9778 31.6764 73.3165 29.35 73.3349 26.2831C73.344 24.8408 73.3417 23.3734 73.3394 21.9562C73.3394 21.2088 73.3394 20.4614 73.3394 19.7164C73.3394 17.9414 72.5568 16.5925 71.0099 15.7084C70.8401 15.6104 70.7254 15.5102 70.6932 15.4623C70.6588 14.8449 70.6634 14.2092 70.668 13.5939C70.668 13.48 70.668 13.3661 70.6703 13.2522H75.3935C75.3935 13.5233 75.3935 13.7945 75.3935 14.061C75.3935 14.9428 75.3889 15.7745 75.3981 16.6221C75.4096 17.8571 76.0178 18.4677 77.2571 18.4837C78.7856 18.5065 80.3623 18.5065 82.0767 18.4837C83.3712 18.4677 83.9794 17.8616 83.9908 16.572C84 15.4304 83.9977 14.2707 83.9954 13.1474L84 13.1519ZM15.972 28.6983C16.0134 27.4497 17.0255 26.4449 18.2763 26.413C18.2969 26.413 18.3153 26.413 18.336 26.413C19.5248 26.413 20.5369 27.3312 20.654 28.5275C20.6884 28.8738 20.6792 29.2224 20.67 29.5938C20.6655 29.7647 20.6609 29.9379 20.6609 30.1088V30.4505H20.6907C20.6907 30.471 20.6907 30.4938 20.6907 30.5143C20.6953 30.7969 20.6976 31.0885 20.6838 31.3688C20.6195 32.7951 19.7015 33.6815 18.2855 33.6815H18.2786C16.8694 33.6792 15.9835 32.7724 15.9652 31.3187V31.2549C15.9537 30.4141 15.9445 29.546 15.972 28.6961V28.6983ZM21.9668 86.7075H9.83046V82.1687H21.9668V86.7075ZM30.5939 69.9421C30.5893 67.9871 30.5847 65.9637 30.61 63.9769C30.6214 62.988 30.2152 62.3363 29.3293 61.9262C27.4497 61.0558 25.5448 60.158 23.6995 59.2922C22.2674 58.6178 20.8353 57.9456 19.4009 57.2757C18.5173 56.8633 17.8586 56.8086 17.3858 57.1071C16.913 57.4056 16.6835 58.0185 16.6812 58.9846C16.6789 63.5622 16.6789 68.142 16.6789 72.7196V78.7509H14.6593V55.5554C15.0793 55.56 15.4993 55.5691 15.9078 55.5759C17.0645 55.601 18.2602 55.626 19.4284 55.5235C22.0815 55.2888 24.2939 55.8995 26.3939 57.442C27.6883 58.3922 29.1067 59.2011 30.4814 59.9826C30.9841 60.2674 31.4844 60.5545 31.9801 60.8462C32.4988 61.1515 32.7214 61.5183 32.7191 62.072C32.7168 64.2776 32.7145 66.4833 32.7145 68.6889C32.7145 70.236 32.7145 71.7831 32.7145 73.328C32.7145 73.3621 32.7099 73.4009 32.7054 73.4419H30.5962V72.2434C30.5962 71.4755 30.5962 70.7077 30.5939 69.9421ZM24.0369 73.4989C22.6346 73.5125 22.031 74.1049 22.0172 75.4789C22.0104 76.3037 22.0104 77.1263 22.0127 77.9739C22.0127 78.2405 22.0127 78.5094 22.0127 78.7805H19.9999V61.1447L20.273 61.2723C22.4831 62.3044 24.769 63.3708 27.0044 64.444C27.1398 64.5101 27.289 64.6718 27.3051 64.7151C27.3303 67.144 27.3303 69.5342 27.328 72.0634V73.492C27.0434 73.492 26.768 73.492 26.4949 73.492C25.648 73.492 24.8402 73.4897 24.0346 73.4966L24.0369 73.4989ZM43.3131 86.7121H25.3313V76.7959H43.3108V86.7121H43.3131ZM64.7513 68.1511H57.3727V50.5654C57.453 50.5973 57.5288 50.6292 57.6022 50.6634L58.8645 51.2535C60.634 52.0806 62.4631 52.9374 64.2464 53.8123C64.4621 53.9194 64.6848 54.227 64.6848 54.3387C64.7307 58.2646 64.7375 62.2566 64.7467 66.1164L64.7513 68.1375C64.7513 68.1375 64.7513 68.1466 64.7513 68.1511ZM76.1371 66.1415H76.3276C76.4515 66.1415 76.5801 66.1415 76.7086 66.1415C77.3902 66.1438 78.0719 66.146 78.7604 66.1438H79.4971V86.7143H68.0929V66.1415H76.1394H76.1371ZM52.0573 44.9876C52.1216 44.9785 52.1858 44.9693 52.2455 44.9693H53.1658C55.4517 44.9625 57.8133 44.958 60.1382 44.9785C60.4045 44.9807 60.7258 45.0787 60.9966 45.2405C62.5549 46.1679 64.1362 47.1203 65.6625 48.0408C66.8697 48.7677 68.0769 49.4968 69.2864 50.2191C69.8532 50.5586 70.0827 50.9049 70.0827 51.413C70.0805 53.5776 70.0782 55.7423 70.0782 57.9069C70.0782 59.5588 70.0782 61.2107 70.0759 62.8513H67.9782V62.2361C67.9782 61.4249 67.9782 60.616 67.9759 59.8049C67.9713 57.7246 67.969 55.5759 67.9896 53.4614C67.9988 52.4019 67.5742 51.7343 66.6103 51.29C64.4025 50.276 62.1671 49.2211 60.0051 48.2003C59.032 47.74 58.0566 47.2798 57.0812 46.8218L56.8976 46.7352C56.613 46.5985 56.3193 46.4572 56.0002 46.3706C55.3439 46.1952 54.9262 46.3501 54.6921 46.5096C54.4557 46.6714 54.1573 47.0109 54.0907 47.6968C54.0609 47.9952 54.0632 48.2914 54.0632 48.5785V48.7517C54.0632 53.8761 54.0632 58.9983 54.0632 64.1227V68.1625H52.0619V44.983L52.0573 44.9876ZM64.7123 71.4869V86.7075H46.7144V71.4869H64.7123ZM39.4345 38.9768C40.6486 38.3844 41.9063 37.7715 43.118 37.1221C44.3574 36.459 45.4567 35.5681 46.2784 34.8663C46.9715 34.2739 47.6049 33.6131 48.2728 32.9159C48.3921 32.7929 48.5115 32.6653 48.6354 32.5399V68.1967H45.7964C43.8295 68.1967 43.3751 68.6524 43.3751 70.637C43.3751 71.3479 43.3751 72.0588 43.3751 72.7925V73.4282H35.9873V72.7355C35.9873 71.7216 35.985 70.7054 35.9827 69.6914C35.9781 67.3081 35.9712 64.845 35.9988 62.4229C36.0217 60.4041 35.184 58.9345 33.4375 57.9319C32.0421 57.1322 30.6444 56.2777 29.2903 55.4529C28.1749 54.7716 27.0228 54.0698 25.8752 53.3976C25.3818 53.1083 25.228 52.8303 25.2693 52.2926C25.3267 51.5384 25.2992 50.7773 25.2739 50.0391C25.2418 49.1094 24.6336 48.4487 23.7615 48.394C23.3254 48.3666 22.91 48.5079 22.591 48.7882C22.2605 49.0798 22.0654 49.4968 22.0379 49.9616C22.0035 50.5586 22.0104 51.1465 22.0195 51.7685C22.0195 51.928 22.0241 52.0875 22.0241 52.2493H14.6111V49.3715C14.6111 47.5259 14.6111 45.6802 14.6111 43.8346C14.6111 43.0166 14.4344 42.447 14.0717 42.0916C13.794 41.8227 13.4199 41.6883 12.9563 41.6974C11.8891 41.7156 11.3475 42.4516 11.3475 43.8848C11.3475 47.0884 11.3475 50.292 11.3475 53.4979V57.5332C10.7003 57.5696 10.1816 57.4329 9.83964 57.1367C9.47473 56.82 9.28883 56.3119 9.28653 55.6306C9.27046 50.0436 9.26358 45.3521 9.28883 40.6265C9.3003 38.4687 10.9045 37.0127 13.2799 37.0036C16.4173 36.9922 19.6074 36.9899 22.6943 36.9899C24.0231 36.9899 25.3497 36.9899 26.6762 36.9899C26.7336 36.9899 26.791 36.9899 26.8484 36.9876C26.9585 36.9854 27.0618 36.9831 27.1559 36.9922C30.2106 37.293 32.7627 36.7211 34.9614 35.24C34.9614 35.3403 34.9614 35.4382 34.9637 35.5339C34.9706 36.156 34.9775 36.7438 34.9362 37.3135C34.9293 37.4183 34.7824 37.6348 34.6286 37.7304C32.8132 38.8401 31.1562 39.2867 29.4119 39.1363C28.0762 39.0224 26.7152 39.052 25.3979 39.0816C24.9228 39.093 24.4454 39.1021 23.9703 39.1067C22.614 39.1181 22.0287 39.7014 22.0149 41.0525C22.0035 41.9822 21.9966 43.085 22.0172 44.2174C22.0379 45.3362 22.6667 46.0311 23.6582 46.0311C24.652 46.0311 25.2556 45.3521 25.2739 44.2129C25.2831 43.7321 25.2808 43.2491 25.2785 42.7364C25.2785 42.6065 25.2785 42.4743 25.2785 42.3399C25.6756 42.3399 26.0657 42.3331 26.4513 42.3262C27.5506 42.3103 28.5903 42.2966 29.6323 42.3581C31.6473 42.4766 33.5339 42.0392 35.3975 41.0229C36.7171 40.3029 38.0965 39.6307 39.4299 38.9791L39.4345 38.9768ZM58.0589 19.9374C58.0589 20.2062 58.0635 20.4751 58.0566 20.7417C58.0222 22.143 57.0858 23.0635 55.6743 23.084C55.6583 23.084 55.6445 23.084 55.6284 23.084C54.2812 23.084 53.3747 22.2068 53.3127 20.8351C53.2737 19.9966 53.2737 19.1194 53.3127 18.2262C53.3701 16.8409 54.3799 15.8201 55.6766 15.8201C55.7019 15.8201 55.7271 15.8201 55.7524 15.8201C57.056 15.8611 58.0268 16.8978 58.0589 18.2877C58.0658 18.5771 58.0635 18.8642 58.0612 19.1536C58.0612 19.288 58.0612 19.4224 58.0612 19.5569C58.0612 19.6822 58.0612 19.8075 58.0612 19.9351L58.0589 19.9374ZM75.3476 9.8731H70.7345V7.93407H75.3476V9.8731ZM80.6699 15.2071H78.7214V13.2362H80.6699V15.2071Z" fill="white"></path>        <span class="value-text">Growing together</span>
    </label>

    <input type="radio" name="value" id="v4" class="value-radio">
    <label for="v4" class="value-label" data-heading="Proactiveness" data-description="We don’t wait to be told to do something. We lead from the front, use our initiative and follow-through">
                <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M89.9953 48.1099C89.6145 46.6007 89.0469 44.8777 87.741 43.4696C87.8648 43.2627 87.9863 43.0605 88.1077 42.8584C88.4535 42.2848 88.7782 41.7418 89.0959 41.18C89.3669 40.7004 89.451 40.1973 89.3295 39.7625C89.2244 39.3863 88.9791 39.0807 88.617 38.8762C87.8461 38.4437 86.9957 38.7164 86.4561 39.5744C86.1547 40.0516 85.8721 40.5264 85.573 41.0295C85.4749 41.1917 85.3791 41.3563 85.2787 41.5232L85.2109 41.4926C85.0334 41.4127 84.8745 41.3398 84.7133 41.2693C80.3144 39.3746 75.2987 41.3069 73.2943 45.6652C73.1752 45.9237 73.0841 46.2152 72.9976 46.4997C72.8388 47.0145 72.6752 47.5457 72.3762 47.7173C71.9791 47.9454 71.36 47.8889 70.7596 47.8349C70.484 47.809 70.2013 47.7832 69.9303 47.7832H69.921C69.4888 47.7832 69.0589 47.7832 68.6267 47.7808C67.6759 47.7761 66.6924 47.7714 65.7229 47.7973C65.223 47.8114 64.8072 47.983 64.5198 48.2909C64.2488 48.5848 64.1157 48.9773 64.1343 49.4263C64.1741 50.3173 64.7651 50.8485 65.7603 50.8885C65.9916 50.8979 66.2252 50.8955 66.4495 50.8932H72.5795C72.5888 50.9261 72.6005 50.9614 72.6098 50.9943C72.6566 51.1565 72.7009 51.2999 72.736 51.4456C73.7102 55.3431 76.9434 57.9736 80.9708 58.1476C81.09 58.1523 81.2091 58.1546 81.3306 58.1546C85.0077 58.1546 88.3507 55.7169 89.5445 52.1203C89.6659 51.7559 89.7617 51.3798 89.8575 51.0154C89.9019 50.8462 89.9463 50.6769 89.9907 50.51L90 48.1593L89.993 48.1005L89.9953 48.1099ZM81.2605 43.6694C82.0945 43.6694 82.9168 43.8645 83.641 44.2735L81.0526 48.5801C81.0036 48.5189 80.9545 48.4602 80.9078 48.4014C80.6695 48.1052 80.4452 47.8278 80.2046 47.5598C79.5575 46.8382 78.7235 46.723 78.0297 47.2589C77.7049 47.5105 77.497 47.8631 77.448 48.2486C77.3919 48.6788 77.5321 49.1372 77.8381 49.5368C78.5156 50.4183 79.2211 51.2928 79.9383 52.1391C80.3681 52.6445 80.8564 52.8937 81.347 52.856C81.8492 52.8184 82.3048 52.487 82.6645 51.8993C83.3327 50.8133 83.9961 49.699 84.6386 48.6224C84.9586 48.0841 85.281 47.5481 85.6034 47.0098C85.7249 46.8053 85.8534 46.6054 85.9819 46.4009L85.9912 46.3845C87.4162 48.1804 87.171 51.2881 85.3978 53.2157C83.3444 55.4466 79.9149 55.6652 77.5905 53.7141C75.3314 51.817 74.9179 48.439 76.649 46.0248C77.733 44.5109 79.5201 43.6694 81.2559 43.6694H81.2605ZM42.941 19.2358C43.4058 19.3534 43.4689 19.4968 43.4643 19.9693C43.4479 21.5607 43.4526 23.1757 43.4549 24.7389V25.7732C43.4572 25.9472 43.4549 26.1211 43.4549 26.2951C43.4502 26.8499 43.4479 27.4235 43.4923 27.9876C43.5624 28.8574 44.1254 29.3746 44.9991 29.3746H45.0038C45.8541 29.3746 46.4054 28.8692 46.5152 27.9923C46.5479 27.7314 46.5479 27.4705 46.5456 27.2166V25.4535C46.5526 23.6834 46.555 21.8522 46.5363 20.0516C46.5316 19.5156 46.6063 19.351 47.153 19.21C49.7671 18.5353 51.8954 16.7417 52.9933 14.2852C54.082 11.8451 54.0096 9.08067 52.7901 6.69703C52.6476 6.41729 52.4467 6.14696 52.2551 5.88603C51.9327 5.44879 51.6314 5.03741 51.6454 4.67305C51.6641 4.22406 51.9981 3.76096 52.3532 3.26966C52.5308 3.02518 52.713 2.7713 52.8578 2.51272C53.1312 2.02612 53.2129 1.54187 53.0938 1.10698C52.991 0.730866 52.7387 0.420569 52.3696 0.209003C51.594 -0.232935 50.7693 0.0373991 50.2157 0.911872C49.912 1.39142 49.627 1.86862 49.3256 2.37403C49.2298 2.53388 49.134 2.69373 49.0383 2.85593C47.2605 1.9744 45.4406 1.68526 43.6231 1.99321C39.4554 2.70078 36.4185 6.16811 36.2386 10.4206C36.0657 14.5085 38.8831 18.2133 42.9363 19.2311L42.941 19.2358ZM47.3352 5.71442C46.6367 6.88274 45.9359 8.0487 45.2 9.27343L44.8215 9.90578C44.7772 9.85172 44.7351 9.8 44.6907 9.74828C44.4548 9.46149 44.2305 9.19116 43.9969 8.92788C43.3147 8.15684 42.455 8.04165 41.7542 8.62698C41.065 9.20056 41.016 10.1173 41.6281 10.9095C42.3079 11.7864 42.9947 12.6373 43.6722 13.4413C44.116 13.9678 44.6113 14.2264 45.1089 14.1865C45.6135 14.1489 46.0784 13.8056 46.4522 13.1968C47.1133 12.1178 47.7744 11.0106 48.4122 9.94104C48.7509 9.37216 49.092 8.80094 49.4331 8.23206C49.5405 8.05575 49.6527 7.8818 49.7695 7.70549C51.2249 9.81175 50.9048 12.8442 48.9752 14.7506C46.8797 16.8193 43.5998 16.9392 41.343 15.0304C39.1518 13.178 38.7056 9.86112 40.3245 7.47982C41.4108 5.88368 43.1676 5.00685 44.9641 5.00685C45.7864 5.00685 46.618 5.19021 47.396 5.57338C47.3749 5.6298 47.3562 5.67446 47.3352 5.70972V5.71442ZM24.4084 47.8067C24.1608 47.7785 23.9132 47.7832 23.6725 47.7879C23.5884 47.7879 23.502 47.7879 23.4226 47.7902H17.4701C17.386 47.5598 17.3066 47.3318 17.2271 47.1109C17.0122 46.502 16.8113 45.9284 16.5473 45.376C16.4048 45.0775 16.1969 44.7907 15.996 44.5156C15.6876 44.0949 15.398 43.6976 15.4096 43.345C15.4283 42.8913 15.7624 42.4235 16.1175 41.9298C16.2904 41.6877 16.4702 41.4385 16.6151 41.1823C17.1244 40.282 16.9748 39.4216 16.2249 38.935C15.8699 38.7046 15.4821 38.6247 15.1059 38.707C14.6714 38.801 14.2813 39.1019 13.9799 39.5768C13.6739 40.0563 13.3772 40.5523 13.0899 41.0295C12.9987 41.1823 12.9076 41.3327 12.8165 41.4855C9.69312 40.0751 7.15141 40.5288 5.55583 41.1682C3.32015 42.0638 1.47695 43.9021 0.624258 46.0859C-1.13018 50.5805 0.932627 55.5735 5.31755 57.4541C6.45758 57.943 7.60462 58.1875 8.75166 58.1875C9.79591 58.1875 10.8402 57.9853 11.8751 57.5787C14.7952 56.4315 16.6595 54.1866 17.4187 50.9002H18.3462C18.9092 50.9002 19.4745 50.9002 20.0399 50.9002C21.4322 50.9002 22.8736 50.9049 24.2893 50.8885C25.205 50.8791 25.8078 50.3314 25.8615 49.4616C25.9152 48.5754 25.3289 47.9101 24.4061 47.8067H24.4084ZM8.77035 43.6741C9.6464 43.6741 10.4804 43.8809 11.1602 44.2759L8.57411 48.5824L7.95738 47.8325C6.94583 46.596 6.08847 46.8452 5.54882 47.2707C5.20541 47.541 5.00918 47.8678 4.96479 48.2416C4.91106 48.7047 5.10029 49.2148 5.5278 49.7531C6.13753 50.5218 6.7496 51.2811 7.34998 52.0074C7.84524 52.6069 8.35452 52.896 8.87548 52.856C9.38475 52.8184 9.84497 52.4705 10.2421 51.8194C10.8846 50.7662 11.527 49.692 12.1484 48.6529C12.5222 48.0276 12.896 47.4047 13.2697 46.7817C13.3468 46.6548 13.4309 46.5326 13.5197 46.4056C14.9424 48.2768 14.6527 51.4315 12.8282 53.3309C10.7724 55.4701 7.39437 55.644 5.13533 53.7305C2.84125 51.7865 2.42308 48.4813 4.16116 46.0413C5.31988 44.4169 7.11637 43.6764 8.77269 43.6764L8.77035 43.6741ZM67.3302 29.5227C72.1566 31.8734 76.0813 31.3656 79.0015 28.0111C81.8095 24.7836 81.7745 20.7403 78.9034 16.3092L79.2374 15.7497C79.5458 15.2396 79.8565 14.7224 80.1602 14.2006C80.7489 13.1921 80.6204 12.32 79.8121 11.8122C78.9968 11.2998 78.1441 11.5842 77.5297 12.5715C77.2447 13.0299 76.9667 13.4907 76.6911 13.9443L76.4224 14.3863C71.9253 12.7972 68.4725 13.4436 65.8724 16.3585C64.646 17.7361 63.9124 19.3369 63.6952 21.1211C63.4242 23.3473 63.9334 25.4347 65.2136 27.3294L64.3867 28.1451C63.4639 29.0549 62.5925 29.9129 61.7141 30.8015L61.6627 30.8532C61.3894 31.1282 61.0483 31.4691 61.0787 31.8217C61.1137 32.2378 61.2399 32.9994 61.6791 33.4084C61.9267 33.6411 62.214 33.754 62.5154 33.754C62.9102 33.754 63.3284 33.5565 63.7138 33.1686C64.6016 32.2777 65.4846 31.3844 66.3607 30.4982L67.3325 29.5156L67.3302 29.5227ZM72.194 21.4714C72.1426 21.4079 72.0912 21.3468 72.0422 21.2857C71.7875 20.9754 71.5492 20.6815 71.2899 20.4018C70.6895 19.7554 69.8205 19.6519 69.1734 20.1503C68.4609 20.7004 68.3557 21.603 68.9187 22.3506C69.5238 23.1545 70.206 24.0055 71.068 25.0304C71.4932 25.5358 71.9861 25.7803 72.486 25.745C72.993 25.7074 73.4485 25.383 73.8013 24.8094C74.5044 23.667 75.2053 22.494 75.8804 21.3609C76.1981 20.8296 76.5158 20.2984 76.8336 19.7695C76.9293 19.6096 77.0275 19.4521 77.1279 19.2946C78.4572 20.9307 78.282 24.0619 76.6957 25.9448C74.7965 28.2015 71.4348 28.6012 69.0449 26.8593C66.655 25.1174 66.0056 21.777 67.5638 19.257C68.6267 17.5386 70.4372 16.5913 72.3272 16.5913C73.1495 16.5913 73.9882 16.7723 74.7871 17.146L72.194 21.4737V21.4714ZM27.5248 33.7281C27.1043 33.7281 26.6698 33.5377 26.282 33.1569C25.2705 32.1672 24.2636 31.14 23.3735 30.2326C23.1703 30.0257 22.9904 29.8047 22.8572 29.6449C22.8526 29.6378 22.8479 29.6331 22.8432 29.6261C22.4461 29.7671 22.0629 29.9176 21.6915 30.061C20.7898 30.4136 19.9371 30.745 19.0423 30.9049C15.7834 31.4855 12.945 30.3995 10.8308 27.7667C8.79138 25.2279 8.32882 22.3106 9.46185 19.0972C9.84497 18.0111 10.6322 17.5222 11.52 17.8231C11.9171 17.957 12.2115 18.2062 12.375 18.5424C12.5829 18.9679 12.5759 19.5297 12.354 20.1644C11.3051 23.1851 12.5876 26.2669 15.405 27.4916C18.1686 28.6952 21.3668 27.5128 22.682 24.8024C23.5861 22.9383 23.4997 21.1117 22.4157 19.2264L22.2755 19.4545C22.0443 19.8282 21.8317 20.1714 21.6238 20.517C21.3177 21.0295 21.0117 21.5419 20.7057 22.0544C20.1824 22.9335 19.6404 23.8433 19.0984 24.7319C18.7176 25.3525 18.248 25.7004 17.7341 25.738C17.2271 25.7756 16.7155 25.5029 16.26 24.9552C15.5101 24.0525 14.8606 23.2486 14.2719 22.494C13.9262 22.052 13.7743 21.5842 13.8281 21.1446C13.8771 20.7591 14.0827 20.4159 14.4238 20.1503C15.1223 19.6096 15.9797 19.7554 16.6595 20.5335C16.8837 20.7897 17.0963 21.0506 17.3206 21.328C17.358 21.375 17.3954 21.4197 17.4351 21.4667V21.462C17.5519 21.2786 17.6547 21.1141 17.7551 20.9472C18.3602 19.9387 18.9652 18.9279 19.5843 17.8959L20.0422 17.1319C18.7713 16.5113 17.4234 16.4103 15.9259 16.8216C15.8138 16.8522 15.7017 16.8969 15.5825 16.9415C15.4797 16.9815 15.3793 17.0191 15.2741 17.052C14.3654 17.3365 13.5945 17.0026 13.2651 16.1822C12.931 15.3501 13.2954 14.5391 14.1692 14.1653C16.3675 13.2274 18.6592 13.2133 20.9766 14.1183C21.1565 14.1888 21.3341 14.2687 21.5326 14.3581C21.5724 14.3769 21.6144 14.3957 21.6588 14.4145C21.7569 14.2523 21.8527 14.0901 21.9508 13.9302C22.2498 13.4319 22.5325 12.9594 22.8362 12.4845C23.4132 11.5772 24.2939 11.3162 25.0789 11.8216C25.8381 12.3106 25.997 13.1686 25.49 14.0595C25.1793 14.6073 24.8593 15.1362 24.5206 15.698C24.3991 15.9002 24.2776 16.1023 24.1538 16.3092C26.873 19.7789 27.102 23.4836 24.8359 27.3247L27.943 30.4347C28.1112 30.6016 28.2817 30.7709 28.4429 30.9449C29.1718 31.73 29.2115 32.6515 28.541 33.2932C28.2397 33.5824 27.8869 33.7258 27.5225 33.7258L27.5248 33.7281ZM56.9998 51.9839C56.9998 51.7536 56.9951 51.5279 56.9928 51.3046C56.9834 50.5852 56.9764 49.9059 57.0208 49.2359C57.0419 48.9233 57.2077 48.5519 57.4156 48.352C60.3732 45.4865 61.8192 41.9557 61.7118 37.856C61.6744 36.4597 61.1534 35.9378 59.7845 35.9355C55.3715 35.9261 50.8861 35.9284 46.6694 35.9355C44.1674 35.9378 42.1911 36.9439 40.7964 38.9209C40.766 38.9656 40.738 39.0102 40.7123 39.0549C38.8831 37.7949 36.9371 37.7925 35.4303 37.9383C32.676 38.2016 30.4356 40.1127 29.7231 42.802C29.0083 45.4959 29.9871 48.2839 32.2158 49.9082C33.1222 50.5688 33.2367 50.8438 32.9493 51.9769C29.4194 52.4635 27.5248 54.2195 27.4664 57.0592C27.4407 58.3333 27.8332 59.4781 28.6345 60.4654C29.6834 61.7583 31.1248 62.3953 33.0311 62.4094C33.1479 64.6074 33.7646 66.5914 34.9093 68.4555C34.8252 68.519 34.7528 68.5707 34.6804 68.6201C30.8141 71.2764 28.7256 74.9106 28.471 79.424C28.3612 81.4034 28.3752 83.4179 28.3892 85.3691C28.3962 86.1777 28.4009 86.984 28.3985 87.7927C28.3962 88.7094 28.5247 89.2172 28.8471 89.5416C29.1695 89.866 29.6717 90 30.5875 90C38.6004 90 46.6157 90 54.6286 90H59.4364C59.4995 90 59.5602 90 59.6233 90C59.6864 90 59.7518 90 59.8172 90C59.9293 90 60.0461 89.9977 60.1606 89.9835C61.053 89.8848 61.5973 89.3371 61.6183 88.5167C61.6604 86.8195 61.6908 84.8119 61.5856 82.7832C61.5576 82.2684 60.9385 81.622 60.4783 81.495C59.9363 81.347 59.2425 81.7395 58.9178 81.9958C58.6304 82.2238 58.579 82.7245 58.5673 83.2322C58.5463 84.1584 58.5487 85.0635 58.5533 86.0249C58.5533 86.3093 58.5533 86.5961 58.5557 86.8829H57.0302C54.6987 86.8853 52.4981 86.8876 50.2671 86.8618C50.1806 86.8618 49.947 86.6878 49.8699 86.5209C48.9729 84.6098 48.0688 82.6257 47.111 80.4584C47.0479 80.3173 47.0689 80.0728 47.1133 80.0141C47.4917 79.5392 47.9076 79.0832 48.3094 78.6413C48.4052 78.5355 48.4986 78.432 48.5944 78.3263C49.5359 79.149 50.0732 79.5016 50.6105 79.4429C51.1618 79.3794 51.587 78.9069 52.3299 77.8679L55.771 73.0583C56.9321 74.323 57.7357 75.8251 58.2216 77.6422C58.6351 79.1866 59.5205 79.3065 60.1886 79.1349C61.1371 78.8904 61.5085 78.0653 61.2072 76.8711C60.511 74.1114 59.0883 71.7583 56.9811 69.8754C56.5443 69.4851 56.084 69.1302 55.6402 68.7893C55.4977 68.6788 55.3575 68.5707 55.222 68.4649C55.3388 68.1969 55.4556 67.936 55.5748 67.6774C55.8995 66.9605 56.2079 66.2834 56.4298 65.5618C56.6587 64.8189 56.7966 64.0385 56.9321 63.2839C56.9811 63.0018 57.0325 62.7174 57.0886 62.4353C60.7119 61.7512 62.4009 60.2562 62.5575 57.5975C62.6322 56.3234 62.3215 55.1833 61.63 54.2101C60.5717 52.7174 59.0556 51.9863 56.9975 51.9863L56.9998 51.9839ZM30.8959 58.2909C30.438 57.5857 30.4286 56.8617 30.8632 56.1377C31.2696 55.463 31.8607 55.1386 32.7017 55.1386C32.7975 55.1386 32.8956 55.1433 32.996 55.1504V59.243C32.0546 59.3276 31.3678 59.015 30.8959 58.2886V58.2909ZM53.4045 71.0437L50.3372 75.3244L48.6621 73.6436C48.9074 73.4931 49.1551 73.338 49.4074 73.1805C50.5497 72.4706 51.7318 71.7348 52.9466 71.0202C53.0097 70.9826 53.2013 71.0108 53.3718 71.0366C53.3811 71.0366 53.3928 71.039 53.4045 71.0413V71.0437ZM46.4428 76.1236L44.9641 77.5317L43.6044 76.2106L45.0482 74.7343L46.4405 76.1236H46.4428ZM36.6871 71.0131C36.8296 70.999 37.0282 70.9826 37.0703 71.0037C38.4089 72.1556 39.897 72.9924 41.4972 73.4908L39.6727 75.275L36.6241 71.0178C36.6451 71.0178 36.6638 71.0131 36.6848 71.0131H36.6871ZM39.4251 79.4334C39.9741 79.4946 40.5231 79.1302 41.4972 78.2886C41.5883 78.3944 41.6794 78.5002 41.7706 78.6083C42.1584 79.0597 42.5578 79.5251 42.9223 80.0094C42.9783 80.0846 42.9947 80.3314 42.9363 80.4631C41.9341 82.7245 41.0207 84.732 40.1399 86.5961C40.0862 86.7113 39.8339 86.8665 39.7007 86.8688C37.5071 86.89 35.2855 86.8876 33.1362 86.8876H31.557L31.5476 86.8782C31.5663 86.0273 31.5757 85.1622 31.585 84.323C31.606 82.2261 31.6294 80.0564 31.8163 77.9407C31.9565 76.3328 32.7391 74.7743 34.2692 73.0536L35.2761 74.4593C36.1008 75.6112 36.9114 76.7395 37.7174 77.8702C38.3598 78.7705 38.827 79.3653 39.4274 79.4358L39.4251 79.4334ZM44.9991 70.9026C44.9664 70.9026 44.9337 70.9026 44.9033 70.9026C40.0418 70.8533 36.1942 66.9534 36.1428 62.0263C36.1171 59.5063 36.1241 57.0357 36.1288 54.4169C36.1311 53.3379 36.1335 52.2519 36.1358 51.1518C38.521 51.2787 40.4039 50.5688 41.885 48.9914C45.9312 53.07 50.6198 51.9863 53.8998 50.7098C53.8998 50.7239 53.9021 50.7357 53.9021 50.7474C53.9021 51.8781 53.9068 53.0112 53.9138 54.1443C53.9278 56.7794 53.9395 59.5016 53.8811 62.1767C53.7713 67.0874 49.8793 70.9026 44.9991 70.9026ZM39.8082 46.5702C39.1588 47.6022 38.1753 48.0441 36.5236 48.0441H36.5049C35.1219 48.164 33.9141 47.6045 33.1899 46.5161C32.4657 45.423 32.398 44.0008 33.0124 42.8043C33.7039 41.4597 35.1032 41.0953 35.8765 40.9989C36.0938 40.9707 36.3134 40.959 36.5306 40.959C37.6917 40.959 38.8107 41.3327 39.341 41.9369C39.5162 42.1343 39.5793 42.534 39.6003 42.6633C39.7568 43.6482 39.904 44.612 39.9881 45.5923C40.0185 45.9613 39.9484 46.3469 39.8082 46.5702ZM46.5947 86.843H43.4549L45.0225 83.3921L46.5947 86.843ZM46.9988 48.5448C44.3099 47.99 42.4667 45.3901 42.7144 42.4987C42.8709 40.6628 44.1464 39.3276 45.9663 39.0972C46.3447 39.0478 46.7372 39.0431 47.0712 39.0408C49.0383 39.0408 51.0029 39.0384 52.97 39.0384C53.6661 39.0384 54.3623 39.0384 55.0585 39.0384H58.593C58.4342 41.6971 57.4834 43.8645 55.6892 45.6558C53.1101 48.2298 50.1876 49.2007 47.0012 48.5448H46.9988ZM59.2074 58.2087C58.7823 58.9633 58.1095 59.2806 57.0372 59.2195V55.1786C57.8572 55.0375 58.5136 55.2844 59.0392 55.9285C59.5509 56.5585 59.6186 57.4752 59.2051 58.2087H59.2074ZM50.0498 61.2341C50.6782 61.9369 50.5754 62.8302 49.7835 63.5636C48.4986 64.7531 46.8937 65.3878 45.0154 65.4489H44.9991C43.1068 65.3855 41.4972 64.732 40.2147 63.5049C39.4835 62.8067 39.397 61.9181 39.9904 61.2411C40.5651 60.5853 41.4739 60.5618 42.2541 61.1847C44.1674 62.715 45.8751 62.7174 47.7908 61.1917C48.5804 60.5618 49.4681 60.5806 50.0521 61.2341H50.0498ZM40.787 57.1226C40.7029 56.3351 40.7029 55.4936 40.787 54.6191C40.8665 53.7705 41.4762 53.2392 42.3476 53.2698C43.1863 53.298 43.7399 53.8316 43.8264 54.699C43.8544 54.9741 43.8474 55.2491 43.8404 55.5147C43.838 55.6323 43.8334 55.7498 43.8334 55.865C43.8334 55.9802 43.8357 56.0954 43.8404 56.2129C43.8474 56.4809 43.8544 56.7559 43.8264 57.0333C43.7329 57.9054 43.1956 58.4226 42.3546 58.4555C42.3266 58.4555 42.3009 58.4555 42.2728 58.4555C41.4412 58.4555 40.8758 57.9501 40.787 57.1226ZM46.2396 57.1391C46.1508 56.3633 46.1485 55.5218 46.2302 54.6379C46.3073 53.8128 46.896 53.2862 47.7651 53.2627C48.578 53.2486 49.2088 53.8786 49.2695 54.7813C49.2859 55.0375 49.2812 55.2938 49.2766 55.5429C49.2766 55.6464 49.2719 55.7498 49.2719 55.8509C49.2719 55.9731 49.2719 56.093 49.2766 56.2152C49.2812 56.4738 49.2859 56.7442 49.2649 57.0098C49.1948 57.8913 48.6808 58.4038 47.8188 58.4508C47.7791 58.4508 47.7417 58.4531 47.7043 58.4531C46.889 58.4531 46.3307 57.9571 46.2372 57.1367L46.2396 57.1391Z" fill="white"></path>        <span class="value-text">Proactiveness</span>
    </label>

    <input type="radio" name="value" id="v5" class="value-radio">
    <label for="v5" class="value-label" data-heading="Openness" data-description="We remain open to learning, feedback, embracing new ideas, sharing information, and understanding different perspectives">
              <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M74.5528 87.4897C75.1658 88.9174 74.5411 89.8661 73.0005 89.8802C69.4849 89.9108 65.9694 89.9319 62.4538 89.9413C53.7224 89.9648 44.9933 89.9836 36.2619 90C34.8059 90.0023 34.4912 89.7487 34.0849 88.375C33.3334 85.8365 32.3377 83.4084 30.8136 81.2221C30.6304 80.9591 30.4331 80.7008 30.2288 80.4519C28.6413 78.524 28.4839 78.531 26.1214 79.2684C22.9135 80.2687 19.6469 81.0906 16.4554 82.1403C13.2263 83.2017 9.4101 81.8233 8.64217 77.1291C8.43551 75.861 8.51066 74.499 8.71262 73.2216C9.01557 71.303 8.99678 71.0471 7.36698 70.089C5.52348 69.0041 5.27689 68.3489 5.84756 66.2589C5.90157 66.057 5.77945 65.7071 5.61741 65.5615C3.54846 63.7275 3.47096 63.3212 4.81895 60.8861C6.14346 58.4932 6.06361 58.1809 3.6377 56.9245C2.07365 56.1167 0.671647 55.1633 0 53.435V52.2045C0.657557 51.1079 1.29633 49.9995 1.97737 48.9193C3.94064 45.8031 6.04013 42.7668 7.8531 39.5661C9.1236 37.3258 9.4923 37.6922 7.17676 35.6092C5.46711 34.0711 4.91993 32.1761 5.84521 30.1425C7.22138 27.1179 8.03628 23.9266 9.00383 20.7681C11.7022 11.9574 17.1575 5.65699 26.2882 3.23592C30.769 2.0477 35.393 1.38783 39.963 0.5448C41.4496 0.270052 42.9713 0.176121 44.4767 0H49.3989C50.1786 0.112717 50.9559 0.225434 51.7356 0.338152C52.0245 0.380421 52.318 0.413296 52.6022 0.4767C53.5674 0.688045 54.0958 1.37374 53.9455 2.20738C53.7952 3.04806 53.0625 3.50128 52.102 3.42378C50.1504 3.2641 48.1895 2.98466 46.2403 3.03397C41.5482 3.15608 36.9007 3.80421 32.3283 4.84684C29.4867 5.49496 26.6357 6.23702 23.9257 7.29139C18.3059 9.48233 14.9265 13.9229 12.665 19.3193C12.1906 20.4464 11.7891 21.6253 11.5237 22.8158C10.9295 25.4858 10.0489 28.0407 8.91459 30.5252C8.09969 32.3076 8.22181 32.5306 9.69426 33.8269C10.2156 34.2848 10.7182 34.8014 11.0939 35.3791C11.9323 36.673 12.0075 38.0186 11.31 39.4557C9.31147 43.584 6.90669 47.4469 4.10738 51.0773C2.66076 52.9512 2.5175 52.7282 4.73676 54.1066C5.18296 54.3837 5.67143 54.595 6.12702 54.8557C8.53884 56.2365 9.26685 58.4368 8.14196 60.9871C7.86015 61.6234 7.51493 62.234 7.1932 62.8657C7.92825 63.5161 8.76664 64.0586 9.33026 64.8053C10.0371 65.74 9.10011 66.529 8.71262 67.3368C8.86997 67.4495 8.95686 67.527 9.05549 67.581C11.5589 69.0041 12.3362 70.7582 11.6646 73.6043C11.3593 74.8935 11.3616 76.1452 11.7914 77.4062C12.355 79.057 13.5738 79.7192 15.2858 79.32C21.5091 77.8664 27.4859 75.6309 33.5072 73.5433C35.0243 73.0173 36.5508 72.4725 37.9786 71.7515C38.9767 71.249 39.9325 70.5351 40.6863 69.7156C41.1936 69.1637 41.4049 68.2784 41.5881 67.5059C41.8159 66.5431 42.3443 65.9983 43.2461 66.0711C44.0751 66.1368 44.5988 66.7709 44.5776 67.7243C44.5424 69.4174 43.8684 70.8428 42.6966 72.0427C40.9094 73.872 38.6573 74.9123 36.3042 75.7647C34.766 76.3213 33.2113 76.8379 31.5768 77.4015C31.7717 77.6575 31.9173 77.8641 32.0794 78.0567C34.1154 80.4871 35.6325 83.2064 36.5601 86.2404C36.7292 86.7946 36.9406 87.0224 37.57 87.0177C48.3797 86.9566 59.1895 86.9261 70.0016 86.8885C70.2036 86.8885 70.4032 86.8603 70.6239 86.8463C70.6169 86.7335 70.6404 86.6349 70.6004 86.5715C67.6109 81.7458 66.5541 76.3354 65.8473 70.8216C65.7322 69.9222 66.2723 69.2107 67.0849 69.0886C67.942 68.9594 68.6043 69.4737 68.8016 70.4506C69.3605 73.2286 69.8349 76.0277 70.4901 78.7846C71.0607 81.1869 72.0799 83.4248 73.482 85.4819C73.907 86.1065 74.2569 86.7946 74.5552 87.4897H74.5528ZM58.5202 5.68752C58.5272 6.8053 59.0862 7.4863 59.9856 7.50039C60.9109 7.51448 61.505 6.8006 61.5121 5.6429C61.5215 4.44294 61.4745 3.24062 61.5262 2.04535C61.5661 1.13187 61.2608 0.460262 60.4717 0.00469655H59.5934C58.7973 0.39451 58.4662 1.01445 58.5037 1.91619C58.5554 3.17252 58.5108 4.43119 58.5178 5.68987L58.5202 5.68752ZM78.8739 25.6244C80.8419 34.2308 78.3667 41.5175 71.2697 47.0547C69.9358 48.095 68.656 49.2245 67.4724 50.4315C66.8688 51.0468 66.5306 51.9227 66.0375 52.7376C68.4211 53.8812 69.1491 55.7222 69.0153 58.0306C68.98 58.6435 69.0223 59.2611 69.0059 59.8763C68.9589 61.7268 68.144 63.1028 66.5142 64.0281C66.2394 64.1831 66.0163 64.6386 65.9835 64.9768C65.6876 67.9051 63.4636 70.2017 60.6032 70.4717C57.71 70.7441 55.0516 68.9383 54.2437 66.1509C54.0065 65.3314 53.0178 63.9365 52.5012 63.2719C51.9423 62.5533 51.2894 61.7291 51.172 60.8814C50.9559 59.3362 50.9865 57.73 51.1344 56.1684C51.2871 54.5622 52.3814 53.5172 54.0042 52.7141C53.4194 51.8147 52.9944 50.8519 52.2969 50.1615C51.0334 48.9099 49.6526 47.7616 48.2459 46.672C37.5253 38.3755 37.9715 21.5971 49.1711 13.9769C61.2444 5.76267 76.0347 13.2184 78.8739 25.6244ZM62.9188 64.547H57.0289C57.2426 66.4257 58.5014 67.5787 60.1617 67.4941C61.7469 67.4143 62.9399 66.1579 62.9211 64.547H62.9188ZM65.9882 57.1312C65.9506 56.0439 65.4245 55.5344 64.3349 55.5203C62.9023 55.5015 61.4674 55.5156 60.0326 55.5156C58.5695 55.5156 57.1064 55.5015 55.6434 55.5203C54.5842 55.5344 54.0253 56.0885 53.9948 57.15C53.969 58.0564 53.9666 58.9652 53.9948 59.8716C54.03 60.9166 54.6054 61.4943 55.6622 61.5013C58.5601 61.5201 61.4557 61.5225 64.3537 61.5013C65.4128 61.4943 65.9529 60.9307 65.9858 59.8528C66.014 58.9464 66.0163 58.0376 65.9858 57.1312H65.9882ZM55.4931 31.427C55.3991 30.5276 54.7486 29.9828 53.9009 30.0321C53.1517 30.0744 52.5904 30.6239 52.5341 31.3683C52.4683 32.2207 53.0014 32.8852 53.8962 32.9862C54.3823 33.0402 54.8801 32.9956 55.5025 32.9956C55.5025 32.4038 55.5447 31.9107 55.4931 31.4246V31.427ZM61.451 52.4675V36.0883H58.546V52.4675H61.451ZM64.5204 33.052C65.2367 33.005 65.8261 33.0355 66.3874 32.9158C67.1553 32.7514 67.6109 31.9929 67.4818 31.2579C67.3573 30.5346 66.7396 30.0227 65.9975 30.025C65.2155 30.0274 64.6002 30.5581 64.5274 31.3542C64.4805 31.8684 64.5181 32.3921 64.5181 33.0496L64.5204 33.052ZM73.7262 21.2942C68.9918 14.0004 60.1007 11.3915 52.7924 15.3272C41.008 21.6746 40.738 37.3845 50.2538 44.4528C52.0245 45.7679 53.5486 47.4187 55.1901 48.9099C55.2606 48.9756 55.3639 49.0061 55.4696 49.0625V36.0343C53.9666 36.0343 52.4918 36.0578 51.2659 35.0644C50.0119 34.05 49.4318 32.749 49.5375 31.1381C49.6831 28.9307 51.4937 27.1508 53.7177 27.0286C54.8778 26.9652 55.9181 27.2729 56.8246 28.0008C58.4145 29.2759 58.6235 31.0512 58.5131 32.9627H61.4534C61.5097 32.1103 61.4675 31.3072 61.6271 30.5464C62.125 28.191 64.2315 26.768 66.6645 27.0615C68.8838 27.3292 70.5652 29.3581 70.4924 31.6876C70.4196 33.9819 68.5832 35.8394 66.2066 35.9991C65.6641 36.0343 65.1169 36.0037 64.5721 36.0037V48.6891C65.3001 48.0621 66.1737 47.2849 67.0755 46.5428C68.0007 45.7796 68.9378 45.0282 69.9006 44.3119C76.7157 39.2514 78.6907 28.9354 73.7262 21.2918V21.2942ZM16.6972 26.7938C16.9203 26.0705 17.0519 25.3168 17.2374 24.5817C18.1674 20.9043 19.8018 17.6121 22.6575 15.043C24.3789 13.4932 26.4197 12.4811 28.653 11.8752C29.4397 11.6615 29.9376 11.213 30.0245 10.431C30.0362 9.36492 29.1133 8.60173 28.0988 8.94693C26.5559 9.47059 24.9707 9.98017 23.5664 10.7809C17.6131 14.1718 15.0533 19.7725 13.8322 26.1809C13.6795 26.977 14.2314 27.6533 14.9782 27.7965C15.7649 27.9468 16.4554 27.5781 16.6972 26.7962V26.7938ZM79.2849 8.65809C78.0872 9.77117 76.9059 10.9124 75.8327 12.1453C75.5486 12.4717 75.5086 13.2349 75.6824 13.667C75.8539 14.0943 76.4386 14.3574 76.8097 14.665C77.2887 14.4654 77.6457 14.4137 77.8594 14.2094C79.0407 13.0775 80.2196 11.9386 81.3351 10.7434C81.8775 10.1633 81.7531 9.29212 81.2012 8.76141C80.6728 8.25419 79.8391 8.14382 79.2849 8.66044V8.65809ZM40.9329 8.83656C40.6534 8.5712 40.2425 8.4444 39.7094 8.15086C39.2867 8.52424 38.7817 8.82247 38.4929 9.26394C38.1101 9.84632 38.3637 10.4592 38.8264 10.9336C39.8268 11.9598 40.8343 12.9766 41.8605 13.9746C42.6073 14.7002 43.4856 14.7119 44.1103 14.0544C44.6927 13.4415 44.6598 12.568 43.9788 11.8658C42.9807 10.8373 41.9732 9.82049 40.9329 8.83656ZM88.54 28.5104C87.0182 28.4658 85.4941 28.4658 83.9723 28.5104C83.0846 28.5362 82.4717 29.2501 82.5116 30.065C82.5515 30.8563 83.1574 31.4457 84.017 31.4786C84.7473 31.5068 85.4824 31.4833 86.2127 31.4833C86.9149 31.4857 87.6194 31.4857 88.3216 31.4857C88.3803 31.4857 88.439 31.4857 88.4977 31.481C89.3666 31.4223 89.9608 30.8587 89.9984 30.0626C90.0359 29.2501 89.423 28.5339 88.54 28.5081V28.5104ZM31.403 28.5198C30.5506 28.548 30.0104 29.2313 30.0339 30.0462C30.055 30.8681 30.6163 31.4387 31.497 31.4763C32.2273 31.5068 32.96 31.4833 33.6927 31.481C34.4841 31.481 35.2756 31.5091 36.0623 31.4716C36.8936 31.434 37.4361 30.8704 37.476 30.0837C37.516 29.2666 36.9876 28.5574 36.1445 28.5245C34.5663 28.4634 32.9835 28.4658 31.403 28.5198ZM12.0028 31.4645C11.9816 32.2653 12.6791 32.9909 13.4846 33.0027C14.3019 33.0144 14.9782 32.331 14.9759 31.4951C14.9759 30.6403 14.33 30.0133 13.4729 30.0274C12.6744 30.0391 12.0262 30.6755 12.0028 31.4645Z" fill="white"></path>
        <span class="value-text">Openness</span>
    </label>

    <div class="semicircle"></div>

    <!-- Content inside semicircle -->
    <div class="semicircle-content">
    <h3 class="semicircle-heading"></h3>
    <p class="semicircle-paragraph"></p>
</div>
</section>
                <!-- Building a positive culture section -->
                <section class="positive-culture">
                    <h2 class="positive-culture__heading">Building a positive culture</h2>

                    <div class="culture-grid">
                        <!-- Row 1 -->
                        <div class="culture-box">
                            <div class="culture-icon">
                              <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M89.9772 33.4525C90.0081 36.0152 90.0009 38.6205 89.9938 41.1406C89.9915 42.2643 89.9867 43.3903 89.9867 44.514C89.9867 46.1403 89.9867 47.7666 89.9867 49.3928C89.9867 53.0342 89.982 56.7988 89.9915 60.4994C90.0009 64.1668 88.8823 67.6897 86.7541 70.6862L83.5688 75.1738C80.3504 79.7089 77.0206 84.3981 73.7311 89.002C73.5463 89.2604 73.2761 89.4785 72.9917 89.7084C72.885 89.7961 72.776 89.8815 72.6741 89.9715L71.911 90C70.681 89.0019 70.6454 88.4923 71.6218 87.1149C72.8139 85.4365 74.0037 83.7557 75.1934 82.0725C78.0682 78.0092 81.0425 73.806 84.0026 69.6976C86.1 66.7888 87.1973 63.3608 87.1759 59.7835C87.1404 53.7146 87.1475 47.5437 87.1522 41.5768C87.1546 39.0591 87.157 36.5391 87.157 34.0214V33.8105C87.1617 33.5497 87.1641 33.2818 87.1333 33.0092C86.984 31.6437 85.9317 30.6361 84.5737 30.5603C84.5192 30.5579 84.4647 30.5555 84.4102 30.5555C83.1138 30.5555 82.0402 31.4256 81.77 32.7176C81.68 33.1538 81.6705 33.5995 81.6705 34.0191C81.6705 35.6809 81.6658 37.3427 81.6634 39.0046C81.6563 44.1039 81.6468 49.3763 81.6776 54.5633C81.699 58.124 80.5211 61.1087 78.0776 63.6856C77.1107 64.705 76.1485 65.7291 75.1863 66.7556C73.551 68.4981 71.8588 70.2998 70.1691 72.0446C69.7827 72.4428 69.1997 72.7463 68.7518 72.7795C68.3063 72.815 67.9128 72.6088 67.664 72.22C67.3891 71.7909 67.3678 71.2504 67.6142 70.8071C67.7943 70.4799 68.0764 70.1789 68.3276 69.9133C69.4675 68.6948 70.6122 67.4763 71.7546 66.2601C73.2192 64.7026 74.6815 63.1451 76.1414 61.5828C77.3192 60.3192 77.4899 58.693 76.5846 57.3346C75.6674 55.962 73.916 55.5092 72.6741 56.3223C71.9726 56.7846 71.2474 57.2445 70.5482 57.6878C67.7825 59.4468 64.9196 61.2675 62.5354 63.6714C59.2719 66.9595 57.1887 71.1224 56.1649 76.3947C55.3805 80.4367 54.5273 84.4787 53.6338 88.4069C53.5651 88.708 53.3565 89.0091 53.1622 89.2604C52.8967 89.6041 52.482 89.808 52.0483 89.808C51.6454 89.808 51.2804 89.6302 51.0434 89.3196C50.7685 88.9593 50.7424 88.5278 50.9415 87.608C51.2733 86.0694 51.598 84.5024 51.9132 82.9876C52.6218 79.5691 53.3541 76.032 54.2263 72.5969C55.544 67.4099 58.4425 63.0408 62.8459 59.6152C65.2443 57.7471 67.8489 56.0449 70.3681 54.3997L70.7497 54.1508C73.1931 52.5529 75.511 52.7639 78.2672 54.8335L78.836 55.2603V47.8187C78.836 43.0869 78.8313 38.355 78.8384 33.6208C78.8431 30.8685 80.2817 28.7609 82.6872 27.9857C84.3936 27.4357 86.1853 27.706 87.6025 28.7254C89.0861 29.7922 89.9512 31.5156 89.9749 33.4501L89.9772 33.4525ZM37.2289 8.0579H37.2312C37.7526 8.05316 38.2503 7.73786 38.4992 7.25187C38.7006 6.85834 38.684 6.44584 38.4541 6.12106C37.7787 5.16568 36.9137 4.29328 35.9562 3.59867C35.7642 3.4588 35.5462 3.39005 35.321 3.39005C35.1386 3.39005 34.9513 3.4351 34.7712 3.52755C34.2901 3.7741 33.982 4.27668 33.982 4.80771C33.982 5.22021 34.5224 6.04283 35.2926 6.80856C36.16 7.66911 36.9018 8.0579 37.2265 8.0579H37.2289ZM45.0166 5.45491C45.7513 5.44068 46.3177 4.87646 46.3959 4.08229C46.4244 3.80255 46.4149 3.5157 46.4078 3.21462C46.4078 3.19329 46.4078 3.16958 46.4078 3.14825H46.3983V2.79265C46.3983 2.63381 46.403 2.47498 46.4078 2.31377C46.4172 1.97002 46.4267 1.64524 46.3912 1.32757C46.3106 0.566589 45.7157 0 45.0024 0C44.9953 0 44.9881 0 44.981 0C44.2606 0.0118533 43.6752 0.559477 43.6207 1.27542C43.5519 2.20946 43.5496 3.17432 43.6159 4.13918C43.6681 4.89069 44.289 5.4715 45.0166 5.45491ZM51.0221 55.1038C50.3016 54.9473 49.8987 54.4139 49.9414 53.679C49.9817 53.001 50.4486 52.5032 51.1311 52.4131C51.38 52.3799 51.6359 52.3775 51.9061 52.3751C51.9867 52.3751 52.0696 52.3751 52.1502 52.3728C52.3848 52.368 52.6171 52.3704 52.8896 52.3704H53.6788V51.204C53.6788 50.5379 53.6788 49.8931 53.6788 49.2506C53.6788 49.1629 53.6788 49.0775 53.6788 48.9898C53.6764 48.7243 53.6741 48.4754 53.7049 48.236C53.8092 47.4655 54.4325 46.9131 55.1482 46.9416C55.9042 46.9771 56.4564 47.5532 56.4896 48.3426C56.5252 49.151 56.518 49.9523 56.5086 50.8034C56.5038 51.178 56.5015 51.5549 56.5015 51.9413V52.2969H60.9025L60.8764 51.9176C60.829 51.2443 60.8006 50.5616 60.7721 49.9002C60.7081 48.4019 60.6394 46.8515 60.3669 45.358C59.945 43.0513 58.3548 41.2283 56.0085 40.3582C54.6624 39.858 53.2356 39.5143 51.8563 39.18C51.2449 39.0307 50.6121 38.8789 49.9911 38.713L49.6973 38.6348L46.7633 45.3177C46.7253 45.4054 46.685 45.4907 46.6471 45.5784C46.576 45.7373 46.5049 45.8937 46.4386 46.0526C46.1186 46.8254 45.6778 47.1763 44.9834 47.1597C44.3411 47.1526 43.9098 46.8159 43.5875 46.0715C42.7367 44.1205 41.8811 42.1718 41.0256 40.2231L40.8905 39.9173C40.8052 39.7229 40.7151 39.5285 40.6013 39.2796L40.2695 38.5589L39.9733 38.6466C39.3097 38.8457 38.6224 39.0283 37.9612 39.2061C36.4397 39.6138 34.866 40.0358 33.399 40.6735C31.0551 41.6929 29.6355 43.7483 29.4009 46.4603C29.2942 47.7073 29.3179 48.9329 29.3464 50.2273C29.3582 50.7892 29.3701 51.37 29.3701 51.9461V52.3017H33.4938V51.2183C33.4938 50.6303 33.4938 50.0377 33.4938 49.445C33.4938 49.336 33.4938 49.2269 33.4938 49.1179C33.4891 48.8144 33.4867 48.5276 33.5199 48.2502C33.6123 47.5105 34.1858 46.9605 34.885 46.944C34.8945 46.944 34.9063 46.944 34.9158 46.944C35.6339 46.944 36.2359 47.5129 36.2951 48.2549C36.3425 48.8547 36.3354 49.4782 36.3307 50.0827C36.3283 50.3506 36.3259 50.6185 36.3259 50.884C36.3259 51.1116 36.3259 51.3392 36.3259 51.5999V52.387H36.6814C36.8971 52.387 37.1056 52.3846 37.3071 52.3799C37.7218 52.3728 38.1152 52.368 38.4944 52.3917C39.3239 52.4439 39.8595 52.9488 39.8904 53.7122C39.9212 54.4945 39.4685 55.0066 38.6129 55.1536C38.357 55.1962 38.0868 55.2034 37.8806 55.2034C34.7523 55.2081 31.6239 55.2105 28.4955 55.2034C26.9219 55.201 26.4408 54.7245 26.436 53.1693C26.4313 51.7232 26.4337 50.2747 26.436 48.7836V46.3963H23.1442C21.5302 46.3987 19.9139 46.4011 18.2999 46.3916C16.9254 46.3845 16.7026 45.742 16.6552 45.2584C16.449 43.158 16.3115 40.9319 17.1007 38.8623C18.1009 36.2333 20.1059 34.5406 23.0636 33.827C23.2792 33.7749 23.4949 33.7275 23.7106 33.6801C24.1253 33.59 24.5543 33.4952 24.9738 33.3695C25.2297 33.2937 25.443 33.1585 25.6516 33.0281C25.7322 32.9784 25.8127 32.9262 25.8957 32.8812L26.2417 32.6891L26.0142 32.3667C25.962 32.2909 25.9099 32.215 25.8578 32.1391C25.7369 31.9566 25.6089 31.7693 25.4596 31.5939C25.289 31.3947 25.1017 31.2075 24.9193 31.0273C24.5804 30.6883 24.258 30.3682 24.0637 30.0008L24.0495 29.9723C23.5328 28.9861 22.9996 27.9691 22.8787 26.9474C22.5896 24.4842 22.3692 21.8694 23.1276 19.3636C23.8149 17.0878 25.5212 15.3311 27.8082 14.5417C30.1356 13.7404 32.6098 14.0818 34.5958 15.4805C34.7286 15.5729 34.8636 15.6606 35.0011 15.7483L35.5415 16.0992L35.7097 15.7128C37.364 11.9173 40.9331 9.55853 45.0284 9.55142H45.045C49.0977 9.55142 52.61 11.813 54.2121 15.4591L54.3448 15.7602L54.6552 15.6511C55.1174 15.4899 55.5772 15.2979 56.0227 15.1154C56.9968 14.7124 57.9163 14.3307 58.8667 14.2193C60.9594 13.9751 62.9478 14.5061 64.474 15.7199C66.0145 16.9455 66.9909 18.7923 67.2232 20.9211C67.4033 22.5688 67.4128 24.3728 67.254 26.6036C67.1165 28.5239 66.2136 30.207 64.5712 31.601C64.3674 31.774 64.2465 32.0087 64.1399 32.2174C64.0996 32.2956 64.0593 32.3762 64.0142 32.4497L63.8104 32.7816L64.1588 32.9546C64.2323 32.9902 64.3034 33.0305 64.3769 33.0708C64.557 33.1704 64.7418 33.2747 64.9575 33.3387C65.403 33.4714 65.8581 33.5829 66.3013 33.6919C67.1876 33.91 68.0219 34.1163 68.7992 34.4719C71.7593 35.8255 73.3377 38.2744 73.4894 41.7498C73.5297 42.6625 73.5297 43.6392 73.4894 44.7416C73.4562 45.6638 72.7026 46.3916 71.7735 46.3987C70.1809 46.4105 68.5622 46.4082 66.9957 46.4058C66.3155 46.4058 65.6377 46.4058 64.9575 46.4058H63.5829V48.7267C63.5829 50.187 63.5829 51.6118 63.5829 53.0389C63.5829 54.7837 63.1516 55.2105 61.3954 55.2105C58.2671 55.2128 55.1387 55.2152 52.0104 55.2057C51.6478 55.2057 51.3207 55.1725 51.0339 55.1109L51.0221 55.1038ZM55.3473 20.0914L55.6269 20.2431C57.8666 21.464 60.2436 21.7509 62.5543 21.7509C63.0663 21.7509 63.5758 21.7366 64.0806 21.7153L64.4574 21.6987L64.4195 21.3241C64.192 19.0507 62.6278 17.313 60.528 16.9977C58.2908 16.6634 56.3071 17.761 55.4705 19.7927L55.3496 20.0867L55.3473 20.0914ZM57.2053 29.7448C57.9708 30.2379 58.8691 30.4844 59.7602 30.4844C60.7105 30.4844 61.6538 30.207 62.4264 29.6523C63.9882 28.531 64.647 26.9711 64.4361 24.8873L64.4029 24.5577L64.0711 24.5672C61.3788 24.6549 58.4875 24.5672 55.5653 23.3511L55.1127 23.1638L55.0747 23.6521C54.8875 26.0157 54.9088 28.2631 57.2053 29.7448ZM38.0204 19.8401L38.075 20.0914L38.3285 20.1199C38.9139 20.1839 39.4732 20.217 40.0065 20.217C42.8955 20.217 45.0901 19.2759 46.8296 17.3154C47.3629 16.7132 47.8914 16.6563 48.7137 17.1067C49.3276 17.4434 49.9343 17.8013 50.5742 18.1806C50.8657 18.3537 51.1643 18.5291 51.4724 18.7093L52.1241 19.0886L52.0033 18.3442C51.6928 16.4311 50.6524 14.774 49.0787 13.6811C47.4742 12.5669 45.5143 12.1639 43.5638 12.5503C41.7626 12.9059 40.042 14.1031 38.959 15.7531C38.075 17.102 37.7408 18.5528 38.0181 19.8401H38.0204ZM25.6042 21.6513L25.9478 21.6845C26.5048 21.7366 27.0688 21.7627 27.6258 21.7627C30.3678 21.7627 33.0056 21.1558 34.4015 20.1459L34.6314 19.98L34.52 19.7192C33.6621 17.7231 31.5457 16.6136 29.3724 17.019C27.3082 17.4054 25.7653 19.1692 25.6255 21.3099L25.6018 21.656L25.6042 21.6513ZM27.9576 29.8633C28.7349 30.3019 29.5241 30.52 30.3038 30.52C31.2234 30.52 32.1311 30.2165 33.0009 29.6096C35.1149 28.1374 35.1386 25.9446 34.9703 23.6379L34.9347 23.1425L34.4773 23.3368C31.6594 24.5388 28.6946 24.6431 25.9146 24.5743L25.5686 24.5648L25.552 24.911C25.4312 27.2579 26.2204 28.8771 27.9599 29.8609L27.9576 29.8633ZM35.8827 36.5746L35.1172 36.2333C34.6788 36.0365 34.1835 35.9369 33.7071 35.8421C32.4178 35.5813 31.4864 35.3941 31.5694 33.7322L31.5883 33.36H28.9861L28.9553 33.6824C28.8107 35.1973 28.614 35.4296 27.2039 35.7639L26.4834 35.9346C25.6919 36.1219 24.9003 36.3091 24.1087 36.494C20.5396 37.3285 19.0583 39.474 19.447 43.2481L19.4802 43.5681H19.8001C20.3049 43.5681 20.8074 43.5681 21.3074 43.5681C21.7933 43.5681 22.2862 43.5681 22.7792 43.5681C24.0353 43.5681 25.3079 43.5634 26.5593 43.5397C26.9835 43.5302 27.3698 43.0655 27.5167 42.7739C28.5429 40.7138 30.0337 39.1658 31.9533 38.1725C32.6264 37.824 33.335 37.5585 34.0839 37.2787C34.4252 37.1507 34.7665 37.0227 35.1078 36.8876L35.8851 36.5746H35.8827ZM39.8975 34.46L36.5558 31.2098L37.9114 29.0857L36.0344 30.7333C35.9088 30.8424 35.7808 30.9514 35.6552 31.0605C35.359 31.3118 35.0532 31.5725 34.7688 31.857C34.4086 32.2197 34.3944 32.5208 34.4489 32.7128C34.501 32.9001 34.6669 33.1514 35.1552 33.2747L39.8998 34.4624L39.8975 34.46ZM47.46 35.5055L47.4553 35.1546H42.4641L42.4902 35.5339C42.5968 37.0867 43.2652 38.3882 43.9098 39.6447C44.1847 40.1804 44.4691 40.7352 44.7014 41.2947L45.0592 42.1599L45.365 41.2757C45.5925 40.6214 45.9195 39.986 46.2371 39.3697C46.8486 38.1867 47.479 36.9611 47.46 35.5055ZM51.9322 26.8928C52.136 25.9422 52.1265 24.9489 52.117 23.9911C52.1123 23.5668 52.1099 23.1282 52.1241 22.7039L52.1312 22.5L51.3373 21.9998C50.7282 21.6157 50.1191 21.2317 49.5077 20.8476C49.4437 20.8073 49.375 20.7718 49.3062 20.7339C49.2233 20.6912 49.1451 20.6485 49.0906 20.6035C48.3511 19.9871 47.7681 20.3119 47.3415 20.6627C45.8129 21.9263 44.1018 22.6659 42.2556 22.8627C41.3692 22.9575 40.4828 22.9931 39.5443 23.0334C39.1154 23.0524 38.6793 23.069 38.2361 23.0927L37.8996 23.1116V23.4483C37.8996 23.683 37.8972 23.9129 37.8925 24.1358C37.8854 24.6241 37.8806 25.084 37.9043 25.5534C38.0963 29.2303 40.8123 32.0348 44.5118 32.3715C44.7132 32.3904 44.9147 32.3999 45.1161 32.3999C48.3227 32.3999 51.2496 30.079 51.9274 26.8928H51.9322ZM52.1455 33.9551C52.8778 33.7725 53.8708 33.5236 54.8662 33.2747C55.3662 33.1514 55.5321 32.8977 55.5843 32.7081C55.6364 32.5208 55.6222 32.2221 55.2667 31.8689C54.9728 31.5773 54.6529 31.3094 54.3448 31.051C54.2121 30.9396 54.0793 30.8282 53.9466 30.7144L51.4226 28.5262L53.4276 31.198L53.4513 31.2312L50.2542 34.4316L52.1407 33.9598L52.1455 33.9551ZM62.5828 42.9565C62.7558 43.3073 63.2653 43.5373 63.6422 43.5515C64.2181 43.5705 64.8011 43.5776 65.377 43.5776C65.6969 43.5776 66.0145 43.5776 66.3273 43.5752C66.6449 43.5752 66.9625 43.5729 67.2801 43.5729H70.5506L70.5838 43.2528C70.9654 39.4811 69.4889 37.3356 65.9387 36.5012C65.4031 36.3755 64.8674 36.2451 64.3318 36.1147C63.8958 36.0081 63.4597 35.9038 63.0212 35.7994C61.3599 35.4059 61.2675 35.304 61.0281 33.6469L60.9831 33.3411H58.4282L58.4425 33.7109C58.5064 35.3941 57.5774 35.5861 56.2953 35.8492C55.8094 35.9488 55.307 36.0531 54.8543 36.257L54.2215 36.5438L54.8235 36.8899C54.8662 36.916 54.9041 36.9374 54.9349 36.9587C55.0131 37.0085 55.0961 37.063 55.2051 37.0891C58.651 37.9425 61.066 39.8604 62.5852 42.9565H62.5828ZM38.0892 82.9994C37.3806 79.5785 36.6482 76.0415 35.7737 72.604C34.456 67.417 31.5575 63.0479 27.1541 59.6223C24.7534 57.7542 22.1511 56.0521 19.6319 54.4068L19.2503 54.1579C16.8069 52.5601 14.489 52.771 11.7328 54.8406L11.164 55.2674V47.8258C11.164 43.094 11.1687 38.3621 11.1616 33.6279C11.1569 30.8756 9.71829 28.768 7.31277 27.9928C6.73213 27.8056 6.142 27.7131 5.55899 27.7131C4.43089 27.7131 3.33359 28.0592 2.39745 28.7325C0.909115 29.7969 0.0440768 31.518 0.0227471 33.4525C-0.00806245 36.0152 -0.000952547 38.6205 0.00615736 41.1406C0.00852732 42.2643 0.0132673 43.3903 0.0132673 44.514C0.0132673 46.1047 0.0132673 47.6978 0.0132673 49.2885C0.0132673 52.9631 0.0180072 56.7633 0.00852732 60.4994C-0.000952546 64.1668 1.11767 67.6897 3.24827 70.6862L6.4501 75.1952C9.66378 79.7232 12.9888 84.4052 16.2712 88.9996C16.3684 89.1347 16.4964 89.2698 16.6741 89.4263C17.0225 89.7321 17.506 89.8649 17.9634 89.7819C18.3947 89.7037 18.7408 89.4548 18.9422 89.0826C19.1697 88.6558 19.1531 88.1959 18.3805 87.1102C17.1884 85.4317 16.0011 83.7509 14.8113 82.0725C11.9342 78.0068 8.9599 73.8036 5.99981 69.6952C3.90238 66.7864 2.80509 63.3584 2.82642 59.7811C2.86197 53.7169 2.85486 47.5508 2.85012 41.5886C2.84775 39.0662 2.84538 36.5415 2.84538 34.0191V33.8081C2.84064 33.5473 2.83827 33.2794 2.86908 33.0068C3.01839 31.6413 4.07065 30.6338 5.42864 30.5579C6.79375 30.482 7.95029 31.3687 8.22995 32.7152C8.32 33.1514 8.32948 33.5971 8.32948 34.0167C8.32948 35.688 8.33422 37.3593 8.33659 39.0283C8.3437 44.1181 8.35318 49.3834 8.32237 54.5609C8.30104 58.1216 9.47892 61.1063 11.9224 63.6832C12.8893 64.7026 13.8515 65.7267 14.8137 66.7509C16.449 68.4933 18.1412 70.295 19.8309 72.0398C20.2172 72.4381 20.8003 72.7415 21.2482 72.7747C21.6937 72.8103 22.0871 72.604 22.336 72.2153C22.6109 71.7862 22.6322 71.2433 22.3881 70.8023C22.208 70.4776 21.926 70.1741 21.6748 69.9086C20.5443 68.6996 19.4091 67.4929 18.2762 66.2886C16.8021 64.7216 15.328 63.1522 13.861 61.5781C12.6831 60.3145 12.5125 58.6882 13.4178 57.3298C14.3326 55.9572 16.0864 55.5021 17.3283 56.3176C18.0298 56.7799 18.755 57.2398 19.4541 57.6831C22.2222 59.4421 25.0828 61.2628 27.467 63.6666C30.7304 66.9548 32.8136 71.1176 33.8374 76.39C34.6219 80.4272 35.4727 84.4692 36.3686 88.4022C36.4373 88.7032 36.6459 89.0043 36.8402 89.2556C37.1056 89.5994 37.5204 89.8032 37.9541 89.8032C38.357 89.8032 38.722 89.6254 38.959 89.3149C39.2339 88.9545 39.2599 88.5231 39.0609 87.6033C38.7291 86.0671 38.4044 84.5024 38.0915 82.9899L38.0892 82.9994ZM45.0118 52.3989H45.0024C44.2463 52.3989 43.6017 53.0223 43.597 53.7667C43.5922 54.2764 43.9169 54.7814 44.4028 55.0256C44.8791 55.265 45.4147 55.2057 45.8674 54.8596C46.0096 54.7506 46.1305 54.6131 46.2158 54.4613C46.4623 54.0228 46.4646 53.532 46.2229 53.1124C45.9693 52.6739 45.5048 52.4012 45.0118 52.3965V52.3989ZM52.8162 8.06027C53.0247 8.08397 53.5058 7.89906 54.1884 7.29691C55.1577 6.4411 55.9066 5.31029 55.9824 4.60146C55.9492 4.54931 55.9161 4.49241 55.8805 4.43315C55.7312 4.17949 55.5274 3.83337 55.2738 3.71246C55.0724 3.61527 54.7903 3.56785 54.5296 3.56785C54.2832 3.56785 54.0533 3.61053 53.9324 3.70061C53.0484 4.34543 52.2402 5.1562 51.5932 6.04046C51.406 6.29649 51.4131 7.01955 51.6027 7.35144C51.79 7.67622 52.3564 8.00811 52.8162 8.06264V8.06027Z" fill="white"></path>                    </svg>
                            </div>
                            <h3 class="culture-title">Sense of Belonging</h3>
                            <p class="culture-description">We envision a culture where everyone feels like they belong. We all contribute by bringing our whole selves to work. When you join Krystelis, we will become torchbearers of this culture and help make it a reality every day.</p>
                        </div>

                        <div class="culture-box">
                            <div class="culture-icon">
                              <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M41.6493 23.4126C42.4762 22.4612 43.3336 21.477 44.2451 20.5726C44.6163 20.2062 45.2317 19.9149 45.8918 19.7998C47.2849 19.5532 48.7061 19.3347 50.078 19.1233C51.0529 18.973 52.0607 18.8179 53.0473 18.6535C53.4232 18.5924 53.7497 18.4538 53.9235 18.2847C55.5468 16.7015 57.1254 15.1229 58.798 13.4504L60.1111 12.1397L59.6484 11.6769C59.1738 11.2024 58.7064 10.735 58.2459 10.2652C57.2663 9.26685 57.2663 8.45644 58.2459 7.4722C60.2991 5.40977 62.5026 3.2064 64.9833 0.73524C65.1594 0.561414 65.3521 0.406379 65.5377 0.256042C65.6222 0.19027 65.7044 0.122149 65.7867 0.0540272L65.8524 0H66.8931L66.9565 0.0493292C67.6495 0.591951 67.9714 1.05471 68.0019 1.55035C68.0324 2.04834 67.767 2.54868 67.1421 3.17586C65.8008 4.5195 64.4571 5.85843 63.1298 7.18327L61.5488 8.75946L80.5862 27.8146L81.9675 26.4334C83.3135 25.0874 84.676 23.725 86.0409 22.3626C86.0926 22.3109 86.1442 22.2592 86.1936 22.2052C86.3698 22.022 86.553 21.8341 86.7691 21.6814C87.4621 21.1834 88.2091 21.228 88.8176 21.8012C89.4495 22.3978 89.5294 23.1495 89.0384 23.9153C88.928 24.0868 88.7894 24.2253 88.6531 24.3592L87.5725 25.4421C85.7144 27.3049 83.8538 29.1677 81.991 31.0257C80.9033 32.111 80.0905 32.1063 78.9958 31.0116C78.3193 30.3351 77.6474 29.6563 76.9662 28.968L76.1957 28.1905L75 29.3838C73.3814 30.9975 71.8521 32.5221 70.3134 34.0935C70.1725 34.2392 70.0668 34.5516 70.0503 34.8711C70.008 35.6979 69.9846 36.5412 69.9611 37.3587C69.9399 38.061 69.9211 38.7869 69.8882 39.4986C69.8389 40.5933 69.2375 41.2416 68.2767 41.2416C68.2603 41.2416 68.2415 41.2416 68.2251 41.2416C67.2408 41.2181 66.6183 40.4993 66.6394 39.4117L66.6488 38.9184C66.6793 37.2436 66.7122 35.5123 66.832 33.8117C66.8696 33.2808 67.1609 32.6606 67.5743 32.2331C68.9603 30.7932 70.4004 29.3603 71.791 27.972C72.3173 27.4482 72.8411 26.9244 73.3673 26.4005C73.5012 26.2643 73.6328 26.1257 73.7455 26.0035L62.3193 14.5756C62.2113 14.6696 62.0868 14.7823 61.967 14.8998C61.3961 15.4682 60.8276 16.039 60.2591 16.6099C58.8567 18.0169 57.4049 19.4733 55.9508 20.878C55.5068 21.3055 54.8914 21.6344 54.3417 21.733C51.9855 22.1606 49.6481 22.5082 47.2637 22.8535C46.6177 22.9475 46.1808 23.1871 45.8002 23.6569C45.1049 24.5166 44.3391 25.3341 43.5967 26.1257L43.5756 26.1468C42.8309 26.9432 41.8842 27.0301 41.1654 26.3747C40.4747 25.7452 40.4794 24.7468 41.1795 23.9482L41.647 23.4102L41.6493 23.4126ZM90.5442 68.1518C90.227 68.4807 89.8629 68.6498 89.4659 68.6545C89.4612 68.6545 89.4565 68.6545 89.4518 68.6545C88.9538 68.6545 88.444 68.3961 87.9319 67.8864C86.3627 66.3243 84.5139 64.478 82.6816 62.6246C82.5148 62.4578 82.3856 62.2605 82.2729 62.0843L63.1791 81.1959L68.413 86.4342C68.4788 86.5 68.5445 86.5657 68.6103 86.6292C68.7724 86.7889 68.9392 86.9533 69.0966 87.1248C69.8248 87.9352 69.8413 88.8842 69.1365 89.5466C68.8147 89.8473 68.4435 89.9976 68.0606 89.9976C67.626 89.9976 67.182 89.805 66.785 89.4245C66.1038 88.7668 65.4272 88.0855 64.7718 87.4255C64.4923 87.1436 64.2127 86.8641 63.9332 86.5822L62.7234 85.3748C61.7861 84.4399 60.8488 83.5026 59.9138 82.563C58.9037 81.5459 58.9084 80.7144 59.9256 79.6925C60.6068 79.009 61.2857 78.3442 62.0046 77.6418C62.2677 77.3835 62.5331 77.1251 62.8009 76.862C62.7727 76.8291 62.7445 76.7962 62.7163 76.7657C62.5777 76.6083 62.4579 76.4744 62.3334 76.3475C61.9411 75.9553 61.5512 75.563 61.1612 75.1683C59.8692 73.867 58.5349 72.5234 57.1888 71.2361C56.8881 70.9472 56.3948 70.7334 55.9908 70.7146C52.507 70.5549 49.4508 70.4562 46.6483 70.4139C44.4777 70.3811 42.6923 69.62 41.1865 68.0861C38.9267 65.7817 36.601 63.4632 34.3506 61.2223C33.2723 60.1488 32.1964 59.0753 31.1205 57.9994C30.162 57.0387 29.7603 55.9652 29.9882 54.9739C30.2161 53.985 31.043 53.198 32.3162 52.7588C33.183 52.4581 34.0428 52.1739 34.9519 51.8709C35.2503 51.7722 35.551 51.6712 35.8563 51.5702C35.7483 51.4551 35.6332 51.3353 35.5181 51.2202C34.5573 50.2571 33.5918 49.2987 32.6286 48.3379C30.552 46.2685 28.4025 44.1285 26.3118 42.0003C25.0245 40.6919 24.5429 38.8855 25.0221 37.1684C25.4779 35.5335 26.7417 34.305 28.4002 33.8821C29.1848 33.6825 29.4221 33.4452 29.6053 32.6794C29.9342 31.3029 30.8809 30.1636 32.2034 29.5576C33.5847 28.9234 35.1469 28.9539 36.4859 29.6445C36.6644 29.7361 36.8359 29.8395 37.0168 29.9475C37.038 29.9593 37.0591 29.9734 37.0802 29.9851C37.8883 28.5099 39.225 27.5375 40.7825 27.3049C42.3587 27.07 43.9186 27.6197 45.2059 28.8599C45.8965 27.4529 46.9584 26.572 48.3655 26.2408C50.2471 25.7968 51.9503 26.3254 53.4279 27.8076C56.5874 30.9788 59.7517 34.1405 62.916 37.3046L66.3458 40.7318C66.7169 41.103 67.0928 41.4741 67.4663 41.8429C68.4247 42.7896 69.4161 43.7691 70.3534 44.7698C70.7645 45.2067 71.0816 45.8268 71.1826 46.3883C71.3917 47.551 71.5749 48.7396 71.7535 49.8883C71.9343 51.0604 72.1199 52.2725 72.336 53.4564C72.43 53.9709 72.7096 54.5229 73.0666 54.8987C74.4855 56.3857 75.9185 57.8232 77.436 59.3478C77.9176 59.8293 78.4015 60.3179 78.8948 60.8135C79.0546 60.6444 79.212 60.4776 79.3646 60.3132C79.8086 59.841 80.2268 59.3947 80.659 58.9602C81.2087 58.4081 81.6621 58.1592 82.1296 58.1545H82.146C82.6088 58.1545 83.0575 58.3941 83.5931 58.9296C85.853 61.1823 88.0987 63.428 90.2693 65.6079C91.1385 66.4793 91.2419 67.4331 90.5442 68.1542V68.1518ZM33.5941 55.8571C33.6223 55.8877 33.6482 55.9158 33.6764 55.944L36.7467 59.0142C39.0841 61.3491 41.4191 63.684 43.7494 66.026C44.4659 66.7448 45.3351 67.1089 46.4087 67.1371C47.8205 67.1747 49.2347 67.2193 50.6465 67.2663C52.6949 67.332 54.8115 67.4002 56.8952 67.4424C57.7315 67.4612 58.3728 67.7431 58.9718 68.3562C60.3625 69.7797 61.7955 71.2056 63.1838 72.5868C63.6536 73.0542 64.1211 73.5193 64.591 73.9868C64.7413 74.1348 64.894 74.2781 65.0373 74.412L65.0608 74.4331L76.494 63.0005L76.4517 62.9558C76.3178 62.8125 76.1863 62.6716 76.05 62.5354C75.505 61.9904 74.9577 61.4454 74.4127 60.9028C72.9985 59.4957 71.5373 58.0417 70.1302 56.5806C69.698 56.132 69.362 55.4836 69.254 54.894C69.0261 53.642 68.8288 52.3665 68.6385 51.1309C68.4717 50.0457 68.2979 48.9228 68.1053 47.8235C68.016 47.3114 67.7787 46.8111 67.4851 46.5175C61.8072 40.7906 56.0847 35.0801 51.2361 30.2482C50.9589 29.971 50.5384 29.5529 50.1743 29.5059C49.6293 29.4355 48.9105 29.4848 48.6145 29.762C48.2715 30.0838 48.1118 30.8026 48.1071 31.3734C48.1071 31.5543 48.2151 31.8878 48.7554 32.4492C49.4414 33.1657 50.1555 33.8774 50.8485 34.5657C51.4569 35.1717 52.0842 35.7966 52.6926 36.4238C53.0497 36.7902 53.2329 37.2177 53.2258 37.657C53.2188 38.0728 53.0403 38.4604 52.7114 38.7775C52.0583 39.407 51.1844 39.4 50.4304 38.7563C50.2941 38.6389 50.1696 38.512 50.0498 38.3899C50.0052 38.3452 49.9605 38.2983 49.9159 38.2536L47.7665 36.1043C46.1737 34.5117 44.5834 32.9214 42.9907 31.3311C42.4574 30.8002 41.903 30.5183 41.3886 30.5183H41.3815C40.9986 30.5183 40.6509 30.6781 40.3502 30.9858C39.6432 31.7093 39.7864 32.6747 40.7425 33.6355C41.6164 34.5117 42.4903 35.3855 43.3642 36.2593C43.9937 36.8889 44.6257 37.5184 45.2552 38.1503C45.5207 38.4157 45.7861 38.6788 46.0539 38.9442C46.7493 39.6349 47.4705 40.3466 48.1541 41.0748C48.8071 41.7701 48.7977 42.7097 48.1306 43.3604C47.4799 43.9923 46.5872 43.9899 45.8519 43.3486C45.7039 43.2194 45.57 43.0809 45.4385 42.9446C45.3868 42.8906 45.3328 42.8366 45.2811 42.7825L42.7487 40.2315C40.6768 38.1432 38.6072 36.0573 36.5329 33.9714L36.2862 33.7224C35.948 33.3771 35.5979 33.0224 35.2268 32.7147C34.6301 32.2214 34.0029 32.3459 33.5824 32.5385C33.0914 32.7617 32.6004 33.2315 32.6921 33.8774C32.7672 34.413 33.0609 35.0097 33.4414 35.3972C35.8493 37.8567 38.3276 40.3302 40.7261 42.7215C41.506 43.5013 42.2859 44.2789 43.0658 45.0587L43.1974 45.1903C43.3407 45.3312 43.491 45.4792 43.6273 45.6389C44.2451 46.3789 44.2216 47.295 43.5686 47.9198C42.9366 48.5235 42.0698 48.5353 41.3604 47.948C41.2218 47.8329 41.0973 47.7061 40.9775 47.5816C40.9328 47.5346 40.8882 47.4899 40.8436 47.4453L37.9988 44.6007C35.6708 42.2728 33.3428 39.9449 31.0101 37.6218C30.5802 37.1942 30.0845 36.957 29.61 36.957H29.6006C29.213 36.9593 28.8559 37.1191 28.5646 37.4174C27.874 38.1244 27.9586 39.0218 28.7902 39.8745C29.2576 40.3537 29.7298 40.8235 30.2043 41.2956L35.262 46.353C38.8726 49.9635 42.4833 53.5762 46.0939 57.1867C46.653 57.7457 46.9349 58.286 46.9302 58.7887C46.9278 59.1763 46.7587 59.5357 46.4298 59.8575C45.9999 60.2756 45.0837 60.7783 43.7588 59.4605C43.4934 59.1974 43.2256 58.9343 42.9601 58.6712C42.3 58.0229 41.6188 57.3558 40.9775 56.6675C38.9502 54.4923 36.8641 54.2551 33.5988 55.8571H33.5941ZM9.86789 31.8408H9.87258C10.3377 31.8408 10.7911 31.5965 11.3032 31.0774C11.7519 30.6217 12.1724 30.1496 12.6187 29.6516C12.7667 29.4871 12.9147 29.318 13.0698 29.1489C13.6007 29.6844 14.1198 30.2106 14.6296 30.7274C16.0626 32.1814 17.418 33.5533 18.811 34.9016C19.3584 35.4325 19.645 36.0174 19.7366 36.7996C19.8494 37.7674 20.0161 38.7117 20.2299 39.8298C20.3991 40.7177 21.0028 41.2463 21.8038 41.2463C21.9048 41.2463 22.0082 41.2369 22.1163 41.2204C23.0254 41.0748 23.5774 40.2526 23.4294 39.2661C23.3848 38.9748 23.3425 38.6859 23.3002 38.3946C23.1334 37.26 22.9643 36.0878 22.7247 34.9462C22.6143 34.4177 22.3042 33.8492 21.8931 33.4264C20.4789 31.9677 19.0178 30.5113 17.6012 29.1019C17.0586 28.5616 16.5136 28.019 15.9733 27.4764C15.8229 27.326 15.6773 27.171 15.5551 27.0395L26.9907 15.6021C27.1176 15.7172 27.2609 15.8488 27.3971 15.985C27.9304 16.5159 28.4613 17.0491 28.9922 17.5823C30.3711 18.9659 31.797 20.3941 33.2206 21.78C33.5401 22.0901 34.0781 22.4424 34.6231 22.4941C36.3309 22.6586 38.0716 22.7384 39.7536 22.8183L39.864 22.823C40.4442 22.8488 40.954 22.6844 41.3063 22.3532C41.6141 22.0643 41.7832 21.6743 41.7926 21.228C41.8114 20.2837 41.1184 19.6307 40.0261 19.5696C39.6009 19.5461 39.1733 19.525 38.7458 19.5062C37.8508 19.4663 36.9252 19.424 36.0255 19.3324C35.6167 19.2901 35.1587 19.0857 34.8862 18.8203C33.472 17.4578 32.0648 16.0414 30.7024 14.6719C30.3218 14.289 29.9412 13.9061 29.5607 13.5233C29.4667 13.4269 29.3751 13.3165 29.2694 13.1897C29.2529 13.1686 29.2341 13.1474 29.2177 13.1263C29.4902 12.8585 29.7603 12.5954 30.0281 12.3347C30.7822 11.5994 31.494 10.9018 32.2058 10.1783C33.0162 9.35611 33.0632 8.4259 32.3326 7.69066C30.4768 5.8232 28.5764 3.94164 26.7347 2.12351C26.0417 1.43759 25.3487 0.754032 24.658 0.0681213L24.5899 0H23.3543L23.2861 0.0610743C22.7129 0.587253 22.417 1.03826 22.3559 1.48223C22.2925 1.94263 22.4757 2.38894 22.9314 2.88693C23.0747 3.04432 23.2274 3.19465 23.3754 3.33794L28.8583 8.80174L9.78332 27.8616L8.26578 26.3301C6.89623 24.9488 5.51729 23.5559 4.13365 22.1676C3.52288 21.5545 2.99902 21.2914 2.49161 21.3431C1.98185 21.3948 1.51907 21.7565 1.03749 22.4847L0.997559 22.5435V23.5794L1.05864 23.6451C1.12911 23.7227 1.20193 23.8025 1.27241 23.8824C1.42745 24.0562 1.58954 24.2394 1.75868 24.4086C3.98096 26.6354 6.20324 28.8623 8.43257 31.0845C8.94703 31.5989 9.40276 31.8385 9.86554 31.8385L9.86789 31.8408ZM40.2962 68.0461L40.167 68.0414C38.7881 67.9898 37.2282 67.9287 36.0396 69.1055C34.7687 70.3646 33.5049 71.6307 32.241 72.8968C31.5292 73.6086 30.8175 74.3227 30.1033 75.0344C29.9553 75.1824 29.8003 75.3234 29.6546 75.4549C29.6476 75.462 29.6382 75.469 29.6311 75.4761L18.1885 64.0434C18.703 63.529 19.2151 63.0146 19.7295 62.4978C21.2095 61.0132 22.7388 59.4793 24.2634 57.99C24.9094 57.3605 25.2054 56.6699 25.2242 55.7444C25.297 52.3031 25.3957 48.8947 25.5202 45.6154C25.546 44.9624 25.7175 44.2906 25.8772 43.7127C26.1591 42.698 25.8232 41.8594 25.0034 41.5258C24.5852 41.3567 24.1459 41.352 23.7654 41.5164C23.3613 41.6902 23.0489 42.0332 22.8633 42.5077C22.6284 43.109 22.3559 43.8983 22.3206 44.6782C22.2196 46.8581 22.1492 49.0732 22.081 51.2178C22.0411 52.4745 22.0012 53.7289 21.9565 54.9856C21.9471 55.2182 21.933 55.6504 21.785 55.8007C20.2299 57.4075 18.6865 58.9602 17.0492 60.6045L15.8323 61.8283L15.0313 61.0155C14.3571 60.332 13.704 59.6719 13.0486 59.0142C11.8999 57.8632 11.127 57.8585 9.99239 58.9884C8.68392 60.2921 7.38015 61.5981 6.07404 62.9042L4.1501 64.828C3.89169 65.084 3.63329 65.3424 3.37958 65.6032C2.18387 66.827 2.69598 67.722 3.10708 68.1471C3.42656 68.476 3.78833 68.6451 4.18063 68.6498C4.681 68.6639 5.19076 68.3961 5.71462 67.8817C6.28781 67.3179 6.85395 66.7495 7.42244 66.1787L7.69494 65.9038C8.67688 64.9196 9.65881 63.9354 10.622 62.9676L11.416 62.1713L30.5285 81.2852C30.3594 81.3956 30.1714 81.5224 30.007 81.6845C28.3955 83.2795 26.6571 85.0177 24.6886 86.998C23.8687 87.8225 23.7795 88.7715 24.4537 89.4762C24.7684 89.8074 25.1466 89.9835 25.546 89.9882C25.553 89.9882 25.5577 89.9882 25.5624 89.9882C26.037 89.9882 26.5326 89.7439 26.9954 89.2835C29.5442 86.749 31.7689 84.5244 33.7938 82.4855C34.2777 81.9993 34.5009 81.5623 34.4986 81.1113C34.4962 80.6674 34.2613 80.2234 33.7797 79.7536C33.3075 79.2908 32.8189 78.8633 32.3021 78.41C32.133 78.262 31.9638 78.114 31.7923 77.9613C32.2786 77.4774 32.7508 77.01 33.2159 76.5519C34.4046 75.3798 35.5275 74.271 36.6151 73.1247C37.7286 71.9525 38.6377 71.1186 40.0871 71.1351C40.2516 71.1327 40.6298 71.0787 40.7684 70.8509C40.8248 70.7569 40.8882 70.6582 40.9516 70.5596C41.21 70.1626 41.4778 69.7492 41.4426 69.3216C41.4003 68.8307 40.7778 68.0555 40.2986 68.0391L40.2962 68.0461Z" fill="white"></path>                            </div>
                            <h3 class="culture-title">Team Environment</h3>
                            <p class="culture-description">We support and encourage each other to challenge perceived limitations and achieve greater heights. When we inevitably make mistakes, we don't blame, but own them, correct them, and learn from them before moving on. Through our growing success as individuals, our teams and our business thrive.</p>
                        </div>

                        <div class="culture-box">
                            <div class="culture-icon">
                              <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M72.0651 51.619C71.4601 50.5882 71.219 49.5691 71.2839 48.3129C71.8356 37.6007 68.0063 28.7295 59.9003 21.9457C59.388 21.516 59.0101 20.7129 59.0009 20.032C58.9684 17.2306 58.9707 14.3847 58.9754 11.635C58.9754 10.8156 58.9754 9.99605 58.9754 9.17655V8.50734C58.9754 8.00249 59.2929 7.55634 59.7658 7.39667C60.2364 7.237 60.7533 7.40137 61.0523 7.8029C61.1195 7.89447 61.1867 7.9884 61.2516 8.07998C61.4116 8.3054 61.5623 8.51908 61.7152 8.66231C62.4895 9.37849 63.4607 9.39023 64.1329 8.69519C64.8121 7.99309 64.8051 6.96226 64.119 6.24608C62.3226 4.37462 60.4334 2.45855 58.5025 0.551863C57.7654 -0.176059 56.8173 -0.185451 56.0872 0.533078C54.1331 2.45855 52.2207 4.39576 50.4081 6.28835C49.7567 6.96931 49.7567 7.99544 50.4104 8.67405C51.0872 9.37849 52.084 9.38554 52.8304 8.68814C52.9973 8.53316 53.1572 8.31714 53.3287 8.09172L53.447 7.93674C53.7483 7.53991 54.2652 7.38493 54.7334 7.5446C55.2017 7.70662 55.5146 8.15042 55.5146 8.65057V18.8344L54.2281 18.339C54.1308 18.3014 54.038 18.2662 53.95 18.2356C53.7576 18.1652 53.5768 18.0971 53.389 18.0149C48.2454 15.7044 42.7703 14.9177 37.112 15.6738C22.9027 17.5735 12.2029 29.1897 11.0949 43.9195C10.4435 52.556 13.1486 60.2578 19.1314 66.8091C19.7039 67.4361 19.9589 68.1029 19.9566 68.9717C19.9357 75.3469 19.9403 81.809 19.9473 87.8836C19.9473 89.3535 20.5616 89.9852 21.9987 89.9946C23.2157 90.004 33.3407 89.9993 40.7328 89.9946C43.7902 89.9946 46.4744 89.9946 48.0669 89.9946C49.3696 89.9946 49.5643 88.9896 49.5643 88.3908C49.5643 88.0409 49.562 87.3623 49.5574 86.6344C49.5527 85.7797 49.5504 84.8569 49.5504 84.3004V83.3376H53.7274C56.0663 83.3376 58.3588 83.34 60.6513 83.3376C64.4598 83.3306 66.9331 80.8369 66.9562 76.9812C66.9701 74.4124 66.9586 69.2324 66.9586 69.1807L66.9539 68.3683C66.9539 68.063 67.0698 67.7742 67.2831 67.5558C67.4963 67.3398 67.7791 67.22 68.0805 67.22C68.9706 67.22 73.5301 67.2153 75.8272 67.2036C76.2282 67.2036 76.6084 67.1989 76.919 67.1214C77.7882 66.9007 78.4512 66.3442 78.785 65.5505C79.1327 64.7216 79.0562 63.7566 78.574 62.9018C76.4809 59.1988 74.2904 55.3995 72.0674 51.612L72.0651 51.619ZM70.1736 63.7519C69.7031 63.7519 69.2302 63.7519 68.7596 63.7519C67.698 63.7519 66.6363 63.7472 65.5747 63.7565C64.214 63.7659 63.5511 64.4422 63.5441 65.8206C63.5372 68.0771 63.5372 70.3313 63.5395 72.5879C63.5395 73.9803 63.5395 75.3728 63.5395 76.7652C63.5395 78.9466 62.6239 79.8788 60.4867 79.8812C58.7134 79.8812 56.9425 79.8812 55.1715 79.8812C52.8698 79.8812 50.5657 79.8812 48.2639 79.8812C46.7132 79.8812 46.136 80.4753 46.1337 82.0696C46.1337 82.8375 46.1337 83.6053 46.1337 84.3732V86.416H23.422V70.9324L24.8313 71.7238C25.1187 71.8858 25.3992 72.0431 25.6727 72.2004C26.2708 72.5386 26.8341 72.8603 27.4066 73.1679C28.0093 73.4919 28.6096 73.5905 29.0941 73.4402C29.4858 73.3181 29.7988 73.0411 30.019 72.6184C30.6078 71.4913 29.8822 70.5873 29.0083 70.1153C21.3311 65.9755 16.5862 59.5534 14.908 51.025C13.6261 44.5206 14.8176 37.7651 18.2621 32.0004C21.702 26.2428 27.0728 22.0091 33.3847 20.0789C40.6006 17.8717 47.6404 18.6231 54.3069 22.3144C55.1878 22.8028 55.5795 23.472 55.5749 24.484C55.561 28.2857 55.5633 32.1507 55.5633 35.8913C55.5633 39.2961 55.5633 42.7032 55.5563 46.108C55.5355 53.514 50.677 59.335 43.4657 60.5936C39.7986 61.2323 35.8951 60.2156 32.7519 57.8017C29.618 55.3925 27.6013 51.8703 27.2212 48.1344C26.7947 43.9289 27.9676 39.9465 30.5266 36.9174C33.0509 33.9306 36.6879 32.1671 40.7675 31.9534C42.411 31.8666 44.0289 32.261 45.5959 32.6438L45.9807 32.7377C46.7503 32.9232 47.4897 32.7518 47.9579 32.2775C48.352 31.8783 48.4911 31.3194 48.3451 30.7042C48.2013 30.0914 47.8212 29.6898 47.149 29.4409C42.564 27.7385 37.367 28.1847 32.884 30.6667C28.3964 33.1533 25.2138 37.3541 24.1521 42.196C23.0325 47.3079 23.9876 52.4057 26.8456 56.5525C29.7223 60.7251 34.1589 63.4114 39.3373 64.1135C43.7183 64.7075 48.3636 63.3855 52.084 60.4833C55.7881 57.5951 58.2267 53.406 58.776 48.9938C58.8989 48.0006 58.9638 46.9275 58.9684 45.8027C58.9823 41.6935 58.98 37.5162 58.98 33.475V28.5087C58.98 28.0133 59.2744 27.5836 59.7334 27.4122C60.1923 27.2407 60.693 27.3722 61.0106 27.7479C61.1311 27.8912 61.2516 28.0297 61.3722 28.1706C61.7523 28.6144 62.1441 29.0723 62.508 29.5607C66.787 35.3136 68.5603 41.8673 67.7791 49.0385C67.6447 50.2642 67.8255 51.18 68.3795 52.1004C69.71 54.3053 71.0058 56.576 72.2575 58.7715C72.7419 59.6215 73.2264 60.4715 73.7155 61.3192C73.8245 61.5094 73.9288 61.7043 74.0563 61.9414C74.1188 62.0588 74.1884 62.188 74.2649 62.3312L75.0344 63.7542H70.1759L70.1736 63.7519ZM41.347 39.2115H41.3262C39.4694 39.2115 37.7101 39.9606 36.3703 41.3201C35.0282 42.6844 34.2934 44.4713 34.3026 46.3545C34.3235 50.243 37.4737 53.4154 41.3238 53.4248H41.3424C45.1787 53.4248 48.3149 50.2689 48.3381 46.3804C48.3497 44.4619 47.6265 42.6609 46.3006 41.306C44.9816 39.9582 43.2223 39.2139 41.3447 39.2092L41.347 39.2115ZM43.8852 48.8952C43.213 49.5809 42.3136 49.9589 41.3517 49.9613H41.3401C39.3559 49.9613 37.7333 48.3411 37.7217 46.3475C37.7147 45.4082 38.1018 44.469 38.7833 43.7739C39.4625 43.0789 40.3851 42.6797 41.3146 42.6727H41.3378C42.2789 42.6727 43.169 43.046 43.8458 43.7246C44.5343 44.415 44.9167 45.3331 44.9214 46.3099C44.926 47.2891 44.5574 48.2072 43.8829 48.8952H43.8852Z" fill="white"></path>                    </svg>
                            </div>
                            <h3 class="culture-title">Personal Development</h3>
                            <p class="culture-description">At Krystelis we believe in supporting your development whether you are just starting your professional career or are an experienced manager. We want to do our part to help you achieve your career goals. We offer each of our team members a personalised development program covering technical, interpersonal, and leadership skills.</p>
                        </div>

                        <!-- Row 2 -->
                        <div class="culture-box">
                            <div class="culture-icon">
                              <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M39.9246 9.27229L40.8323 9.93607L40.4079 11.2448C40.151 12.0381 39.8987 12.8172 39.6441 13.5963C39.4649 14.1424 39.3164 14.9333 40.0779 15.477C40.8417 16.0231 41.5419 15.6371 41.9993 15.2981C42.716 14.7685 43.4256 14.2577 44.1753 13.7187L45.1112 13.0432L46.2499 13.8647C46.9572 14.3754 47.6573 14.8839 48.3575 15.3923C48.9823 15.8442 49.5834 15.8748 50.1257 15.4912C50.7103 15.0769 50.873 14.479 50.6113 13.714C50.4534 13.255 50.3072 12.7842 50.1634 12.3276C49.9795 11.7391 49.7744 11.0824 49.5457 10.4633C49.3972 10.0585 49.4632 9.85839 49.8286 9.60653C50.3567 9.24404 50.8919 8.85095 51.3657 8.50258C51.7547 8.21776 52.1414 7.93294 52.5351 7.65519C53.2258 7.16794 53.471 6.61479 53.2848 5.96277C53.0844 5.25661 52.5539 4.91295 51.6604 4.90824C51.3917 4.90824 51.1253 4.90354 50.8565 4.90118C50.1257 4.89412 49.2958 4.8847 48.5013 4.91766C47.8907 4.9459 47.6503 4.77878 47.5065 4.22327C47.3414 3.57596 47.1222 2.93807 46.9076 2.31901C46.7827 1.95651 46.6577 1.59402 46.5446 1.22682C46.3772 0.692494 46.0377 -0.0207236 45.0664 0.0004611C44.1588 0.0169381 43.8334 0.697201 43.6684 1.20799L43.4303 1.93768C43.1686 2.73328 42.8998 3.55713 42.6547 4.38098C42.5321 4.79055 42.3458 4.92237 41.9191 4.9153C41.2166 4.89412 40.5046 4.89647 39.8162 4.90118H39.7407C39.4461 4.90118 39.1537 4.90589 38.859 4.90589H38.6516C38.0245 4.90589 37.2677 5.01417 36.973 5.84978C36.6406 6.79603 37.2535 7.36096 37.7416 7.70462L37.6072 7.89764L37.7416 7.70462C38.4771 8.22011 39.2103 8.75444 39.9176 9.27464L39.9246 9.27229ZM43.4916 7.79171C44.1352 7.66696 44.5973 7.33506 44.9061 6.77249L45.1159 6.39117L45.321 6.7772C45.6251 7.35154 46.0943 7.68108 46.7568 7.787L47.2094 7.85997L46.8864 8.18481C46.4055 8.6697 46.2192 9.21579 46.3159 9.85369L46.3843 10.2939L45.9811 10.1032C45.3729 9.81367 44.7953 9.81838 44.2177 10.115L43.8146 10.3221L43.8759 9.87487C43.9702 9.18755 43.8051 8.63675 43.3666 8.18951L43.0602 7.87645L43.4916 7.79407V7.79171ZM56.6325 30.238C53.636 27.171 49.6612 25.4503 45.4413 25.3962C45.3658 25.3962 45.2927 25.3962 45.2173 25.3962C40.9054 25.3962 36.8598 27.0321 33.7998 30.0144C30.7444 32.9944 29.0375 36.9795 28.9928 41.2376C28.9008 50.1163 35.9993 57.4203 44.8212 57.5192H45.0169C53.9802 57.5192 61.0882 50.4953 61.1966 41.5295C61.2485 37.3137 59.6242 33.3051 56.6301 30.2357L56.6325 30.238ZM45.2479 54.6946C45.2244 54.6946 45.1984 54.6946 45.1749 54.6946C41.5985 54.6946 38.2437 53.3129 35.7164 50.7989C33.2127 48.3086 31.8312 44.9967 31.8312 41.4753C31.8312 37.9187 33.2174 34.5856 35.7353 32.0882C38.2508 29.5931 41.6103 28.2208 45.1914 28.2208C52.4431 28.2255 58.3488 34.1713 58.3653 41.4753C58.3794 48.737 52.495 54.6663 45.2479 54.6922V54.6946ZM85.1916 48.7676C83.6121 48.4969 82.1975 49.1913 81.5021 50.5777C80.974 51.6322 80.4648 52.7197 79.972 53.7695C79.6514 54.4545 79.2577 55.2948 78.8711 56.0904C78.0648 57.7499 76.9827 59.0351 75.5611 60.0213L75.2405 60.2426L75.1933 59.8566C74.9835 58.1736 74.2055 57.0767 72.8146 56.507C72.3148 56.3022 71.8197 56.1987 71.327 56.1987C70.3062 56.1987 69.2924 56.6389 68.2598 57.5263C67.8802 57.8511 67.4842 58.1689 67.1023 58.4749C66.7015 58.795 66.2889 59.1269 65.8952 59.4658C62.1727 62.6812 58.8061 64.7738 55.2957 66.0496C52.653 67.0123 50.5807 69.0295 49.1402 72.0448C47.9897 74.4528 47.4334 77.1244 47.3886 80.4504C47.3815 80.9847 47.2542 81.2319 46.8652 81.4696C46.0354 81.9757 45.578 82.8207 45.5403 83.9129C45.5002 85.104 45.5002 86.0785 45.5403 86.9777C45.6157 88.7383 46.8959 89.9741 48.6499 89.9812H49.3595C51.3775 89.9906 53.4239 90 55.4796 90C58.3865 90 60.7864 89.9859 63.0355 89.9529C63.7593 89.9435 64.6056 89.6163 65.1903 89.122C66.1074 88.3476 66.2536 86.5187 66.2724 85.7678C66.3078 84.4049 66.0414 82.5218 65.1738 81.7074C65.1479 81.6815 65.1196 81.6532 65.0819 81.6368C64.608 81.4155 64.608 81.1048 64.7848 80.5304C65.3483 78.6921 66.487 77.3151 68.1726 76.4395C69.3773 75.811 70.6669 75.1472 71.9824 74.547C75.9972 72.7133 78.9512 70.2959 81.0117 67.1559C83.6121 63.1943 85.8329 58.6561 87.8014 53.2846C87.9759 52.8068 88.0395 52.2254 87.9759 51.6887C87.799 50.2199 86.6556 49.0194 85.194 48.7699L85.1916 48.7676ZM63.3349 86.4504C63.3562 86.7305 63.3208 86.9094 63.2218 87.0153C63.118 87.1283 62.9129 87.1777 62.6065 87.1707C60.9656 87.1495 59.3083 87.1518 57.7028 87.1565C57.0828 87.1565 56.4627 87.1589 55.8427 87.1589C55.4513 87.1542 55.06 87.1565 54.671 87.1565C54.4352 87.1565 54.1971 87.1565 53.9614 87.1565C52.3865 87.1518 50.7646 87.1471 49.1709 87.1754C49.1473 87.1754 49.1237 87.1754 49.1025 87.1754C48.8125 87.1754 48.6263 87.1259 48.5155 87.0177C48.3952 86.9 48.3434 86.6952 48.3434 86.3351V86.2668C48.3434 84.9604 48.3434 84.3037 48.6522 84.0024C48.9634 83.7011 49.6424 83.7011 50.9956 83.7011C51.9928 83.7011 52.9901 83.7011 53.9873 83.7034H54.1547C56.8564 83.7082 59.6501 83.7105 62.3966 83.687C62.854 83.687 63.0874 83.7435 63.2194 83.8894C63.3396 84.0212 63.3797 84.2425 63.342 84.5838C63.2831 85.1275 63.2831 85.7207 63.3396 86.4527L63.3349 86.4504ZM84.9842 52.7762C82.9637 58.0512 80.9339 62.1422 78.5952 65.6518C76.9379 68.1398 74.7808 70.0064 72.0012 71.3645C70.3297 72.1813 68.927 72.8734 67.5196 73.5889C64.1907 75.2837 62.7503 77.0679 61.8143 80.6576L61.7672 80.8341H50.2365L50.2294 80.6058C50.1658 78.638 50.4345 76.7125 51.0239 74.8812C52.106 71.527 54.2561 69.2767 57.4104 68.1963C61.291 66.8687 64.4052 64.4019 67.2649 61.9915C67.8355 61.5113 68.4272 61.0311 68.9977 60.5698L69.0307 60.5439C69.4952 60.1673 69.962 59.793 70.4217 59.4093C71.2633 58.7126 71.7655 59.0492 72.0719 59.3623C72.5222 59.8236 72.501 60.3415 72.0036 60.9417C70.9663 62.201 69.8559 63.5498 68.7785 64.8891C68.1042 65.7271 68.0901 66.5344 68.7408 67.0994C69.0637 67.3795 69.4244 67.5066 69.7875 67.4619C70.2166 67.4124 70.6244 67.13 70.9663 66.6498C71.9824 65.2257 73.246 64.2677 75.066 63.5403C77.8078 62.4435 79.9414 60.3556 81.4078 57.3332C81.9807 56.1516 82.5488 54.9441 83.1005 53.7766L83.1311 53.713C83.3504 53.2493 83.5696 52.7832 83.7889 52.3195L83.8455 52.1971C84.0765 51.6934 84.2462 51.3945 84.7602 51.5969C85.3071 51.8111 85.2105 52.1854 84.9842 52.7738V52.7762ZM66.9844 22.5127C67.1447 22.628 67.305 22.741 67.4653 22.854L67.5361 22.9034C68.2457 23.4048 68.9789 23.9227 69.6602 24.4852C69.8559 24.6453 70.002 25.0855 69.9407 25.3256C69.7333 26.1282 69.4574 26.9285 69.191 27.7053L69.1604 27.7947C69.092 27.9948 69.0237 28.1949 68.9553 28.3973C68.7455 29.0188 68.6064 29.8026 69.3985 30.3557C70.2331 30.9418 70.9946 30.3816 71.3175 30.1415C72.0554 29.5978 72.8122 29.0517 73.5454 28.5244L74.4106 27.9007L75.3772 28.5998C76.1528 29.16 76.9167 29.7108 77.6782 30.2639C78.3053 30.7182 78.9041 30.7418 79.4463 30.3369C80.0074 29.9156 80.1701 29.3459 79.9296 28.6398C79.7905 28.2302 79.6585 27.8089 79.5335 27.3993C79.3331 26.7496 79.1304 26.1 78.8946 25.4739C78.6966 24.9466 78.7768 24.683 79.2271 24.3864C79.8259 23.9886 80.4153 23.5555 80.9834 23.1365C81.2757 22.9199 81.5681 22.7034 81.8651 22.4915C82.3036 22.1761 82.8553 21.6583 82.5748 20.7897C82.3602 20.1188 81.8581 19.7917 81.0376 19.7917C80.6439 19.7917 80.2691 19.7917 79.8966 19.7893H79.8542C79.2082 19.7846 78.475 19.7775 77.7701 19.8034C77.2019 19.827 76.9709 19.6645 76.8342 19.1514C76.6644 18.5112 76.4452 17.8733 76.2354 17.2566C76.1057 16.8799 75.9666 16.468 75.8416 16.0702C75.5917 15.2675 75.1344 14.8721 74.446 14.858C73.4794 14.8391 73.1423 15.5571 72.9725 16.0985L72.8216 16.5833C72.5482 17.4637 72.2629 18.3746 71.9164 19.2432C71.8221 19.4762 71.4472 19.7516 71.1973 19.7681C70.3203 19.8246 69.4197 19.8128 68.5498 19.8034C68.3188 19.8011 68.0948 19.7987 67.8685 19.7964C67.3003 19.7869 66.5223 19.8858 66.2465 20.8297C65.9966 21.6912 66.5506 22.2067 66.9891 22.5198L66.9844 22.5127ZM72.5741 22.6045C73.51 22.3879 73.7505 22.2161 74.1796 21.4582L74.4224 21.0321L74.6039 21.4864C74.7548 21.8607 74.9623 22.1361 75.2381 22.3315C75.4857 22.5056 75.7898 22.6045 76.1411 22.628L76.6408 22.661L76.2966 23.0235C76.0491 23.2848 75.7992 23.5696 75.6978 23.9015C75.6082 24.1957 75.646 24.5088 75.6978 24.803L75.7874 25.3044L75.3466 25.0478C75.0471 24.8736 74.7407 24.7842 74.4366 24.7795C74.1065 24.7724 73.7599 24.876 73.411 25.0878L72.9867 25.3444L73.055 24.8548C73.1753 23.9839 73.0786 23.6873 72.4492 22.9905L72.1804 22.694L72.5694 22.6045H72.5741ZM7.87633 22.4962L7.7396 22.6869L7.87633 22.4962C8.16395 22.7034 8.44921 22.9152 8.73448 23.1247C9.2885 23.5366 9.85666 23.9603 10.4437 24.3322C10.96 24.6594 11.0496 24.9607 10.8162 25.5657C10.6016 26.1165 10.4225 26.6884 10.2504 27.2416C10.1254 27.6441 9.99575 28.0607 9.85194 28.4632C9.55961 29.2942 9.70106 29.9085 10.2881 30.3393C10.538 30.5229 10.8044 30.617 11.0779 30.617C11.5894 30.617 12.0232 30.2969 12.2095 30.1604C12.945 29.619 13.6735 29.0941 14.4421 28.5409L15.3332 27.9007L16.2244 28.5456C16.941 29.0635 17.6837 29.6002 18.4169 30.1439C18.9214 30.5182 19.5933 30.8783 20.3359 30.3581C21.1233 29.8049 20.9842 29.0188 20.7768 28.3973C20.6542 28.0278 20.5316 27.6582 20.4113 27.2863C20.2015 26.639 19.9846 25.9729 19.7559 25.3256C19.6145 24.923 19.6852 24.7065 20.0412 24.464C20.7178 24.005 21.3826 23.5178 22.0239 23.0447C22.2903 22.8493 22.5567 22.6539 22.8254 22.4586C23.4596 21.9996 23.6765 21.4629 23.4903 20.8203C23.2899 20.1306 22.7901 19.794 21.9673 19.794C21.6844 19.794 21.4109 19.7964 21.1351 19.8011H21.1092C20.2652 19.8105 19.3905 19.8175 18.5371 19.7634C18.2943 19.7493 17.9123 19.4668 17.8204 19.2361C17.5351 18.5112 17.297 17.7532 17.066 17.0188C16.9575 16.6775 16.8515 16.3362 16.7383 15.9972C16.413 15.0157 15.7694 14.8321 15.2884 14.8509C14.6189 14.8697 14.1592 15.2487 13.9258 15.9808C13.8291 16.2844 13.723 16.5857 13.6193 16.8894C13.4024 17.5131 13.1808 18.1581 13.0228 18.8077C12.8272 19.6245 12.4806 19.8882 11.6908 19.8128C11.0236 19.7493 10.347 19.7634 9.69163 19.7799H9.66334C9.38515 19.7869 9.10697 19.7917 8.82878 19.7917H8.6944C7.88576 19.7917 7.36946 20.1283 7.15964 20.7944C6.88381 21.663 7.43783 22.1808 7.87633 22.4939V22.4962ZM13.6782 22.6634C14.3643 22.5292 14.8381 22.1949 15.1234 21.6465L15.3261 21.2605L15.5383 21.6418C15.8566 22.2114 16.3163 22.5457 16.9458 22.6657L17.3748 22.7481L17.0707 23.0612C16.8538 23.2848 16.71 23.5225 16.6275 23.7932C16.5261 24.1227 16.5238 24.4758 16.6228 24.8736L16.7477 25.3844L16.2833 25.1396C15.536 24.7442 15.1918 24.7465 14.3501 25.1514L13.8975 25.3679L14.0201 24.8807C14.2016 24.1557 14.0507 23.5602 13.5556 23.0635L13.2468 22.7504L13.6782 22.6657V22.6634ZM41.681 37.7092C41.1505 37.6786 40.6107 37.6856 40.0873 37.6927H40.0307C39.6299 37.6998 39.2292 37.6974 38.8307 37.6998H38.7341C38.239 37.7021 37.6779 37.7068 37.1333 37.6927C36.6312 37.6809 35.9263 37.7586 35.6504 38.573C35.3864 39.3475 35.8767 39.8229 36.3318 40.1525C36.7255 40.4349 37.1168 40.7197 37.5058 41.0046L37.5765 41.0564C38.3687 41.633 39.1891 42.2286 40.0189 42.8053C40.382 43.0571 40.4456 43.2596 40.2995 43.6762C40.0189 44.4741 39.7572 45.2933 39.505 46.0865C39.3164 46.675 39.1042 47.3411 38.8873 47.9837C38.7223 48.4686 38.5832 49.1701 39.2551 49.6644C39.9411 50.1681 40.5659 49.8245 40.9737 49.5208C41.4099 49.1983 41.8531 48.8829 42.294 48.5675C43.0036 48.0614 43.7368 47.5389 44.4393 46.9904C44.7104 46.7786 44.925 46.675 45.1324 46.675C45.3399 46.675 45.5308 46.7715 45.7713 46.9669C46.4739 47.5342 47.2212 48.0685 47.945 48.5863C48.3599 48.8829 48.7607 49.1701 49.1567 49.4643C49.3736 49.6244 49.7768 49.9233 50.2412 49.9233C50.4817 49.9233 50.7174 49.8409 50.9437 49.6809C51.6251 49.196 51.5143 48.5204 51.3139 47.9225C51.1394 47.4 50.9697 46.8727 50.7999 46.3478L50.7693 46.256C50.5123 45.4604 50.22 44.5589 49.9229 43.6974C49.7791 43.2831 49.8498 43.0595 50.2058 42.81C50.8471 42.3604 51.4883 41.892 52.1107 41.44L52.1579 41.4071C52.7237 40.9952 53.306 40.5691 53.8907 40.1572C54.5343 39.7029 54.7512 39.1827 54.5555 38.5636C54.3669 37.9657 53.9189 37.6974 53.108 37.6974C53.0938 37.6974 53.0797 37.6974 53.0655 37.6974C52.4809 37.7045 51.8938 37.7021 51.3092 37.6974C50.4416 37.695 49.5552 37.6903 48.6852 37.7139C48.1288 37.7209 47.9096 37.575 47.7634 37.0689C47.5065 36.1768 47.2094 35.28 46.9194 34.4161C46.7403 33.8771 46.5658 33.3569 46.4008 32.8343C46.1579 32.067 45.7289 31.6998 45.0923 31.7045C44.2578 31.7116 43.956 32.3683 43.8028 32.8602C43.6165 33.4534 43.4232 34.0442 43.2323 34.635L43.2205 34.6703C42.9541 35.4895 42.6782 36.3369 42.4236 37.1772C42.294 37.6032 42.1053 37.7374 41.6763 37.7115L41.681 37.7092ZM44.5855 38.6813C44.6845 38.38 44.7835 38.0811 44.8943 37.7492L45.1136 37.0901L45.3422 37.7468C45.5143 38.2458 45.6558 38.6601 45.7784 39.0815C46.0094 39.8794 46.5187 40.249 47.3367 40.2278C47.7917 40.216 48.2491 40.2184 48.756 40.2207L49.4444 40.2254L48.8974 40.6444C48.5155 40.9363 48.1996 41.1764 47.8624 41.3977C47.0703 41.9179 46.8463 42.584 47.1788 43.429C47.332 43.8174 47.4593 44.2223 47.5984 44.6719L47.8059 45.3474L47.233 44.9308C46.9171 44.7025 46.6412 44.5 46.3631 44.3C45.7548 43.8621 45.4177 43.7021 45.1159 43.7021C44.8142 43.7021 44.4817 43.8621 43.8876 44.2952C43.6047 44.5 43.3195 44.7025 42.9941 44.9331L42.4307 45.3333L42.6334 44.6719C42.7749 44.2082 42.8951 43.8245 43.0366 43.4479C43.3666 42.5628 43.1521 41.8943 42.3788 41.4047C42.0087 41.1717 41.6786 40.9198 41.3698 40.635L40.9242 40.2254H42.4826C43.9324 40.2254 44.1305 40.0795 44.5878 38.6837L44.5855 38.6813ZM42.0323 81.3567C41.8177 81.2037 41.6692 80.8317 41.6362 80.5611C41.5725 80.0173 41.5348 79.4618 41.4995 78.9251C41.4429 78.0966 41.3863 77.2398 41.2354 76.4136C40.1934 70.722 37.237 67.1182 32.1966 65.3952C30.0725 64.6702 28.0002 63.4627 25.8572 61.7043C25.4659 61.3819 25.0745 61.0594 24.6832 60.7369C23.2144 59.5247 21.6962 58.2724 20.1567 57.0979C18.9615 56.1846 17.4904 55.9468 16.222 56.46C14.9395 56.9802 14.0389 58.1877 13.815 59.6942C13.8055 59.7506 13.7985 59.8048 13.7914 59.8636L13.7419 60.2473L13.4236 60.026C12.0044 59.0351 10.9293 57.7546 10.1372 56.1139C9.74114 55.2948 9.34979 54.4592 8.97023 53.6495C8.55059 52.7503 8.07437 51.7358 7.6005 50.7636C6.95454 49.4431 5.77577 48.6993 4.48856 48.6993C4.03355 48.6993 3.56676 48.7911 3.10233 48.9841C1.32946 49.7232 0.565624 51.5522 1.24695 53.4329C2.75577 57.5992 4.597 61.549 6.7235 65.1763C9.12818 69.2767 11.8393 71.9695 15.2554 73.6525C17.4314 74.7235 19.1948 75.6062 20.8027 76.4277C22.5708 77.3316 23.726 78.7627 24.2376 80.6834C24.2989 80.9094 24.2117 81.3472 24.0372 81.4979C23.1885 82.2393 22.7948 83.1526 22.8302 84.2896C22.8443 84.7627 22.8419 85.2452 22.8372 85.7113C22.8349 86.1067 22.8325 86.4951 22.8372 86.8811C22.8702 88.7007 24.141 89.9765 25.9303 89.9835C30.5181 90.0024 35.2237 90.0024 40.316 89.9835C42.1313 89.9765 43.3926 88.743 43.4609 86.9141C43.4987 85.8878 43.4821 84.8827 43.4609 84.0353C43.435 82.9149 42.9517 82.0134 42.0275 81.3543L42.0323 81.3567ZM21.4204 73.556C20.3689 73.064 19.3151 72.5344 18.2943 72.0213C17.8133 71.7788 17.3348 71.5387 16.8538 71.2986C14.1686 69.9687 11.9855 68.0503 10.3635 65.5976C7.97535 61.9892 5.9408 57.8652 3.95576 52.6208C3.70821 51.9688 3.88503 51.7428 4.22687 51.6063C4.62765 51.4463 4.87519 51.5922 5.13452 52.1501C5.95259 53.9013 6.76358 55.6173 7.55099 57.2532C9.18241 60.6498 11.8794 62.999 15.5619 64.2371C15.8943 64.3477 16.2574 64.6043 16.6086 64.9715C16.9198 65.2987 17.2098 65.6612 17.4904 66.0119L17.323 66.1814L17.5069 66.0354C17.6955 66.2708 17.8888 66.5133 18.0915 66.7463C18.7304 67.4854 19.5626 67.6125 20.2062 67.0735C20.8428 66.5392 20.8593 65.7012 20.2487 64.9362C19.8408 64.4254 19.4259 63.9217 19.011 63.4179C18.3721 62.6412 17.7167 61.8479 17.1037 61.0453C16.8302 60.6875 16.6676 60.2144 16.7076 59.8942C16.7548 59.5341 16.9481 59.2658 17.2546 59.1433C17.6554 58.9833 18.1693 59.0986 18.6007 59.4423C20.2699 60.7816 22.0451 62.2104 23.7779 63.6533C26.3547 65.7977 28.6863 67.177 31.1169 67.9962C35.4241 69.4438 37.8689 72.5909 38.595 77.614C38.6987 78.332 38.7364 79.0546 38.7789 79.8219C38.793 80.0691 38.8048 80.3186 38.8213 80.5728L38.8378 80.8223H27.2199L27.1798 80.6364C26.4726 77.3904 24.5347 75.0083 21.4227 73.5536L21.4204 73.556ZM40.6508 84.6709C40.5824 85.3041 40.613 85.9537 40.646 86.4457C40.6649 86.7258 40.6272 86.907 40.5234 87.0153C40.415 87.1307 40.2217 87.1754 39.9105 87.173C38.2555 87.1518 36.5321 87.1542 35.0139 87.1589C33.7762 87.1613 32.5408 87.1589 31.3055 87.1589H31.2018C29.6222 87.1542 27.9908 87.1518 26.3853 87.173C26.3594 87.173 26.3358 87.173 26.3099 87.173C26.0458 87.173 25.8667 87.1212 25.77 87.0153C25.6733 86.9118 25.638 86.7329 25.6639 86.4551C25.6922 86.1491 25.6757 85.8101 25.6639 85.483C25.6356 84.8262 25.6073 84.2048 25.895 83.887C26.0435 83.7223 26.324 83.6517 26.8285 83.6517C27.0879 83.6517 27.3637 83.6705 27.6042 83.687C27.8352 83.7034 28.1252 83.7223 28.3727 83.7223C29.8085 83.7176 31.2418 83.7176 32.6776 83.7223H32.9935C35.1836 83.7223 37.4469 83.7246 39.6724 83.7011C40.1132 83.7034 40.3796 83.7646 40.5164 83.9176C40.646 84.0589 40.6885 84.306 40.6508 84.6709ZM60.355 56.7236C64.45 52.642 66.7086 47.2117 66.7156 41.4306C66.7298 29.5648 57.071 19.8929 45.1843 19.8693H45.1395C39.3683 19.8693 33.9412 22.1102 29.858 26.18C25.7629 30.2616 23.5091 35.6919 23.5091 41.473C23.5115 53.3482 33.1679 63.0225 45.0358 63.039H45.0664C50.84 63.039 56.2694 60.7958 60.3574 56.7236H60.355ZM26.3547 41.3859C26.4183 36.2592 28.4482 31.4997 32.0646 27.9878C35.5773 24.5747 40.2288 22.7057 45.1866 22.7057C45.2762 22.7057 45.3635 22.7057 45.453 22.7057C55.7342 22.8587 63.9974 31.4644 63.8724 41.8873C63.8159 46.6797 61.7342 51.418 58.1625 54.8852C54.6191 58.3242 50.0832 60.2073 45.3564 60.2073C45.2527 60.2073 45.1489 60.2073 45.0428 60.2049C34.6084 60.0378 26.225 51.5969 26.3547 41.3835V41.3859Z" fill="white"></path>                    </svg>
                            </div>
                            <h3 class="culture-title">Rewards and Recognition</h3>
                            <p class="culture-description">We have an engaging rewards & recognition program. Every win, however small, is worth celebrating. We frequently pause to celebrate our achievements.</p>
                        </div>

                        <div class="culture-box">
                            <div class="culture-icon">
                              <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M89.1313 43.515C88.7471 43.2516 88.3093 43.1257 87.7947 43.1257H87.7878C79.3657 43.142 70.8016 43.142 62.5192 43.142C59.1732 43.142 55.8272 43.142 52.4812 43.142C51.7757 43.142 51.2797 43.3005 50.9607 43.6246C50.6441 43.9486 50.4974 44.4474 50.5113 45.1514C50.6208 50.1957 54.5116 54.6036 59.3712 55.1864C61.2363 55.4101 63.1549 55.4008 65.013 55.3938C65.8396 55.3892 66.6942 55.3868 67.5324 55.4031C67.7793 55.4078 68.0261 55.4055 68.2682 55.4031H68.4731V65.6596H52.4184C50.6999 61.4894 48.297 59.6013 44.8485 59.7015C41.109 59.8181 38.6036 61.8111 37.4044 65.6246H21.3263V55.4008H22.2717C22.9889 55.4008 23.7014 55.4031 24.4139 55.4078C25.96 55.4148 27.5573 55.4241 29.129 55.3752C34.6126 55.2004 39.174 50.6129 39.2951 45.1467C39.3114 44.4451 39.1624 43.9416 38.8387 43.6129C38.5291 43.2959 38.0634 43.142 37.416 43.1397C26.0415 43.135 14.0988 43.1327 1.92091 43.1397C1.25962 43.1397 0.784617 43.2959 0.472602 43.6129C0.153602 43.9369 0.00690887 44.4311 0.0185512 45.1211C0.107033 50.0954 4.07008 54.6083 8.85275 55.1794C10.7202 55.4031 12.6412 55.3962 14.4993 55.3892C15.3236 55.3868 16.1781 55.3822 17.0164 55.4008C17.2608 55.4055 17.5053 55.4055 17.7638 55.4008H18.0386V66.4941C18.0386 68.5034 18.4647 68.937 20.4345 68.937H37.1692C37.1692 68.937 37.1785 68.9556 37.1832 68.9673L37.1692 68.9813L37.1832 69.0442C37.5045 70.5151 38.5267 71.6293 39.5466 72.6456C40.2475 73.3449 41.1206 73.8484 42.0427 74.3822C42.4269 74.6037 42.8227 74.8344 43.2093 75.0792V86.7529H34.1469C33.4856 86.7529 32.8243 86.7529 32.1654 86.7529C30.4749 86.7529 28.7285 86.7482 27.0101 86.7622C25.9926 86.7692 25.2917 87.4009 25.2638 88.3287C25.2358 89.2797 25.9414 89.9487 27.0171 89.993C27.1522 89.9977 27.2872 89.9977 27.4199 89.9953H31.9162C42.0218 89.9953 52.1273 89.9953 62.2352 89.9953C62.305 89.9953 62.3772 89.9953 62.4494 89.9977C62.5216 89.9977 62.5938 90 62.6683 90C62.8429 90 63.0199 89.993 63.1945 89.9604C63.8139 89.8462 64.3122 89.4103 64.4984 88.8228C64.6591 88.3077 64.5334 87.7762 64.1608 87.3986C63.744 86.979 63.0106 86.7832 62.5053 86.7785C58.633 86.7459 54.6956 86.7482 50.8862 86.7506C49.7499 86.7506 48.623 86.7506 47.4797 86.7506H46.5623V74.8368C50.2319 73.3449 51.0259 72.5454 52.4277 68.9347H58.1813C61.9464 68.9347 64.9502 68.9347 69.6723 68.9347C71.2184 68.9347 71.7679 68.3892 71.7702 66.8507C71.7726 64.3542 71.7726 61.8601 71.7702 59.3635V55.3915C72.5619 55.3915 73.3443 55.3985 74.1173 55.4055C75.9312 55.4195 77.6426 55.4335 79.3843 55.3775C83.3101 55.2493 86.2859 53.5104 88.2302 50.2073C88.7355 49.3472 89.0498 48.3635 89.3548 47.4124C89.5132 46.9183 89.6761 46.4078 89.8624 45.9229C90.2024 45.0441 89.895 44.0302 89.1313 43.508V43.515ZM86.3604 46.4264C85.8342 48.5803 84.7142 50.1211 83.026 51.0092C82.0993 51.4964 80.6976 52.0838 79.2795 52.1118C73.328 52.2237 67.4742 52.1957 60.9848 52.1514C57.5806 52.1281 54.5396 49.6246 53.9994 46.4288H86.3604V46.4264ZM49.2912 67.3403C49.2819 69.7389 47.326 71.7016 44.93 71.7132C43.7634 71.7132 42.6481 71.261 41.8099 70.4242C40.9716 69.585 40.5106 68.4754 40.5152 67.3006C40.5246 64.902 42.4805 62.9393 44.8765 62.9277H44.8974C46.0663 62.9277 47.1653 63.3845 47.9989 64.219C48.8348 65.0559 49.2936 66.1654 49.2912 67.3403ZM35.8071 46.4218C35.3088 49.487 32.2771 52.0861 29.1151 52.1281C22.6699 52.2143 16.2945 52.2143 10.1637 52.1258C7.02956 52.0815 4.18651 49.6386 3.49029 46.4218H35.8071ZM2.58219 22.121C2.93612 22.135 3.29005 22.1326 3.66493 22.128C3.80231 22.128 3.94435 22.1257 4.09104 22.1257V27.2003C4.09104 30.5872 4.08871 33.9765 4.09337 37.3635C4.0957 39.4101 5.04338 40.3635 7.07148 40.3635C11.0671 40.3635 15.0628 40.3658 19.0561 40.3658C23.0494 40.3658 26.8402 40.3658 30.731 40.3635C32.7661 40.3635 33.7138 39.4124 33.7184 37.3728C33.7254 34.0394 33.7231 30.7061 33.7231 27.3728V22.128H34.1422C34.4938 22.1303 34.8105 22.1326 35.1248 22.128C36.331 22.1047 37.1622 21.5662 37.5977 20.5266C38.0261 19.5033 37.8189 18.5359 36.9969 17.727C31.8766 12.6944 26.3651 7.28644 20.7535 1.82022C19.6312 0.726981 18.1829 0.726981 17.0629 1.81789C11.4467 7.2911 5.73494 12.8972 0.821872 17.7224C-7.65551e-05 18.5289 -0.209638 19.4963 0.211814 20.5173C0.630938 21.5336 1.4296 22.072 2.58452 22.121H2.58219ZM30.4842 37.0558H7.32761V31.8809C7.32761 28.6711 7.32761 25.4613 7.32761 22.2515C7.32761 19.9065 6.61044 19.079 4.29129 18.8552L18.9094 4.52886L33.5182 18.8459C33.4414 18.8506 33.3668 18.8576 33.297 18.8622C33.0129 18.8809 32.7684 18.8972 32.5309 18.9485C31.2153 19.2305 30.4889 20.2725 30.4865 21.8856C30.4795 25.2935 30.4819 28.6991 30.4819 32.107V37.0581L30.4842 37.0558ZM58.8449 40.3402C62.2911 40.3705 65.835 40.3845 69.5279 40.3845C73.2209 40.3845 77.2957 40.3681 81.4706 40.3378C84.2462 40.3169 86.5164 39.121 88.0369 36.8786C88.2465 36.5686 88.4421 36.2189 88.6353 35.8133C89.3944 34.2026 89.7972 32.3565 89.7972 30.4707V15.9998L89.7926 15.9648C89.7274 15.7503 89.6668 15.5312 89.6086 15.3144C89.4806 14.8436 89.3502 14.3541 89.1476 13.9018C87.6364 10.5289 85.0239 8.734 81.3868 8.56617C80.3367 8.51722 79.2726 8.5312 78.2434 8.54286C77.808 8.54752 77.3725 8.55451 76.9394 8.55451C76.3014 4.94611 74.0545 2.70135 70.9134 2.54051C69.486 2.46591 67.6372 2.53584 66.0213 3.73165C64.4449 4.89716 63.5764 6.47525 63.3645 8.55684C62.9011 8.55684 62.4424 8.55451 61.9884 8.54985C60.9033 8.54286 59.8788 8.53586 58.8333 8.56151C54.2928 8.67106 50.5626 12.431 50.516 16.9438C50.4648 21.9974 50.4648 27.0581 50.516 31.9835C50.5649 36.5546 54.3021 40.3052 58.8449 40.3448V40.3402ZM72.3687 28.9998C72.3407 29.2073 72.0124 29.5592 71.8331 29.5709C70.7434 29.6408 69.67 29.6198 68.4778 29.5895C68.2985 29.5849 68.0237 29.445 67.9958 29.4054C67.9469 28.6198 67.9469 27.8203 68.0005 27.0278C68.054 26.9695 68.3218 26.8576 68.5011 26.8436C68.8946 26.8156 69.3021 26.8203 69.6932 26.8273C69.8539 26.8273 70.0146 26.832 70.1752 26.832C70.2684 26.832 70.3638 26.8273 70.4547 26.832C71.5583 26.832 72.1032 26.8599 72.3267 27.114C72.5526 27.3704 72.5107 27.9135 72.3687 29.0021V28.9998ZM83.4824 15.5196C82.8211 17.5988 82.1342 19.748 81.317 21.8016C80.3274 24.2865 78.3551 25.9019 75.6099 26.48C74.753 24.2935 73.7308 23.5872 71.4536 23.5872C71.0158 23.5872 70.5781 23.5872 70.1403 23.5872C69.7026 23.5872 69.2648 23.5872 68.8271 23.5872C66.5591 23.5872 65.5439 24.2842 64.694 26.4357C63.4949 26.3145 62.3795 25.7947 61.1943 24.8086C59.9696 23.7853 59.0917 22.4846 58.5888 20.9438C58.2744 19.9765 57.9624 19.0068 57.6527 18.0371C57.1614 16.5009 56.6538 14.9135 56.1323 13.3564C56.0368 13.072 56.0252 12.8739 56.095 12.7387C56.1532 12.6268 56.2743 12.5429 56.4652 12.4869C56.6911 12.4193 56.917 12.3401 57.1428 12.2631C57.7645 12.0487 58.4072 11.8273 59.0289 11.8226C65.5183 11.776 72.1195 11.7853 78.5018 11.7923H81.275C82.4137 11.7969 83.4196 12.1419 84.3463 12.8482C84.0506 13.7387 83.7618 14.6454 83.4824 15.5242V15.5196ZM63.0851 29.431C64.2237 29.5266 64.8174 29.9742 65.2016 31.0301C65.5998 32.1187 66.6872 32.7924 68.1099 32.832C69.4278 32.8669 70.7876 32.8693 72.1521 32.832C73.6749 32.7924 74.8927 31.8739 75.4911 30.3122C75.5866 30.0651 75.8707 29.7411 76.0965 29.6944C80.2459 28.846 83.1448 26.2958 84.7119 22.1117C84.9517 21.4683 85.1799 20.8063 85.3988 20.1676C85.7527 19.1373 86.1159 18.0767 86.5583 17.0301V21.6222C86.5607 24.9159 86.563 28.2096 86.556 31.501C86.5467 34.8553 84.2927 37.1117 80.9467 37.1164C73.9474 37.128 66.6825 37.128 59.3525 37.1164C56.0019 37.1117 53.7456 34.86 53.7363 31.5126C53.727 28.2119 53.7293 24.9112 53.7316 21.6128C53.7316 20.2142 53.7316 18.8156 53.7316 17.4147V16.5848L53.7642 16.5802C53.9272 17.0883 54.0902 17.5965 54.2509 18.107C54.6188 19.2678 55.0006 20.4683 55.3965 21.6455C56.6981 25.5196 59.1849 28.107 62.7893 29.3331C62.815 29.3425 62.8429 29.3541 62.8685 29.3658C62.9314 29.3937 63.0036 29.424 63.0827 29.431H63.0851ZM69.7072 5.77129C72.0427 5.69669 73.4328 6.69204 73.5678 8.51255H66.7408C66.7803 7.01838 68.061 5.8249 69.7072 5.77129Z" fill="white"></path>                    </svg>
                            </div>
                            <h3 class="culture-title">Work-life Balance</h3>
                            <p class="culture-description">We cannot perform to our best at work if we don't have the time to support other aspects of our life. At Krystelis, we offer a flexible working approach to encourage our employees to maintain a healthy work-life balance.</p>
                        </div>

                        <div class="culture-box">
                            <div class="culture-icon">
                              <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M7.40873 34.1364C7.57084 32.958 7.59199 31.8501 7.88568 30.8199C9.45753 25.3017 16.2712 23.2459 20.5215 27.0988C23.4115 29.7191 26.144 32.5228 28.8013 35.383C31.212 37.9775 31.353 41.2987 29.4616 44.9257C30.7468 46.1959 32.0414 47.4754 33.3571 48.7738C33.9539 49.3619 33.9257 50.3333 33.296 50.8884L33.2279 50.9472C32.6546 51.4506 31.7923 51.4224 31.2543 50.8814C30.0231 49.6418 28.7168 48.3269 27.394 46.9956C25.317 48.2964 23.193 48.6868 20.8434 48.3034C22.1944 49.6653 23.5337 51.0413 24.9152 52.3726C25.0938 52.5443 25.4979 52.5279 25.7963 52.5255C27.8756 52.5043 29.4216 53.3652 30.2816 55.2846C31.0781 57.0628 30.6199 58.9093 29.0574 60.4923C28.1364 61.4261 27.2131 62.3599 26.2756 63.2772C24.7437 64.7755 22.8852 65.2036 21.1348 64.4156C19.2575 63.5689 18.3623 62.0564 18.4046 60.0148C18.4164 59.4009 18.2307 58.9704 17.7914 58.547C16.4192 57.2251 15.1058 55.8421 13.7267 54.5272C13.4494 54.2614 12.9772 54.0497 12.6036 54.0615C9.93214 54.1462 7.74706 53.17 5.93321 51.2342C4.82187 50.0487 3.6377 48.9361 2.49582 47.7789C0.244957 45.4926 -0.567987 42.7829 0.404726 39.7039C1.38684 36.5944 3.60011 34.8044 6.81194 34.2517C7.0281 34.214 7.24191 34.1717 7.40873 34.1387V34.1364ZM12.3451 51.0319C12.9889 51.2554 13.5269 50.8508 14.0438 50.2816C14.7135 49.543 15.4442 48.8562 16.1467 48.1482C17.2392 47.0474 17.2251 46.0007 16.1279 44.8787C13.729 42.4254 11.3489 39.9533 8.93123 37.5188C8.70568 37.293 8.26866 37.1683 7.93502 37.1754C5.88387 37.2201 4.32612 38.1492 3.47323 40.0285C2.62035 41.9056 2.8788 43.7332 4.26738 45.255C5.69591 46.8215 7.23251 48.2916 8.75736 49.7688C9.69718 50.6791 10.8673 51.0648 12.3428 51.0295L12.3451 51.0319ZM16.0903 52.6267C17.1476 53.6804 18.1673 54.7765 19.2739 55.7762C20.6367 57.0064 21.7739 58.2601 21.4426 60.3276C21.3815 60.7134 21.8937 61.4096 22.3049 61.5907C22.7184 61.7718 23.4961 61.6754 23.8344 61.379C25.0538 60.3158 26.2098 59.1609 27.2694 57.9378C27.5584 57.6038 27.6172 56.83 27.4386 56.3972C27.2788 56.0091 26.6774 55.7503 26.2215 55.5551C25.9302 55.4304 25.5402 55.4939 25.2018 55.5339C24.1774 55.6562 23.4279 55.2517 22.7348 54.5084C21.358 53.0289 19.9107 51.6176 18.4751 50.1616C17.6527 51.0107 16.922 51.7658 16.0903 52.6243V52.6267ZM19.2622 34.8914L19.5112 34.755C19.5112 33.3578 19.5183 31.9607 19.5018 30.5635C19.5018 30.3965 19.4267 30.1613 19.3045 30.0719C18.508 29.4909 17.7585 28.7382 16.861 28.4207C14.9202 27.7338 13.144 28.2631 11.7718 29.8108C10.5759 31.1586 10.2352 32.7369 10.7733 34.4469H14.549L14.6947 34.2281C14.0697 33.7201 13.4424 33.2096 12.8526 32.7298C13.635 31.9136 14.2929 31.2291 14.986 30.507C16.4333 31.9936 17.8478 33.4425 19.2622 34.8938V34.8914ZM22.514 33.2849C22.514 34.7221 22.5398 35.9311 22.5022 37.1354C22.4834 37.7375 22.6408 38.1844 23.0919 38.6102C24.2691 39.7251 25.3945 40.8965 26.5458 42.042C26.7502 42.2466 26.9734 42.4324 27.2506 42.6818C28.1035 40.6307 27.8897 38.8431 26.5176 37.3118C25.3123 35.9664 23.9754 34.7409 22.5163 33.2826L22.514 33.2849ZM13.2967 37.5188L13.1369 37.7564C13.9663 38.5467 14.7957 39.337 15.6298 40.1226C15.7849 40.2685 15.9681 40.4849 16.142 40.4896C17.6128 40.5178 19.0836 40.506 20.8152 40.506C19.7556 39.4734 18.9192 38.6314 18.0498 37.8269C17.8666 37.6576 17.5658 37.5376 17.3168 37.5329C15.9775 37.5047 14.6359 37.5188 13.2943 37.5188H13.2967ZM19.1283 43.5238C20.7095 45.4009 23.0755 45.8925 25.1173 44.7258C24.4876 44.2906 23.966 43.6485 23.3739 43.5756C22.03 43.4109 20.6508 43.5238 19.1259 43.5238H19.1283ZM22.5328 22.1592V21.0043C22.5328 20.5057 22.9369 20.0988 23.4373 20.0988H29.4569C26.0218 16.9986 24.4006 13.4539 25.1572 9.07657C25.6107 6.45863 26.9006 4.28054 28.9376 2.57052C32.9812 -0.828338 39.9077 -1.43049 45.0203 4.39814C47.976 1.09572 51.5802 -0.614293 56.0772 0.201904C58.6218 0.662926 60.7599 1.91192 62.421 3.90184C64.4369 6.31985 65.3063 9.11891 64.9726 12.2637C64.6413 15.3709 63.1024 17.8383 60.5931 20.04H65.0408C65.5483 20.04 65.9594 20.4516 65.9594 20.9596V22.1569C65.9594 22.6555 65.553 23.0625 65.0549 23.0648C62.8674 23.0695 60.6824 23.086 58.4949 23.0554C57.8911 23.046 57.4541 23.213 57.0218 23.6529C53.5491 27.1623 50.053 30.6482 46.5616 34.1411C45.3022 35.4019 44.7172 35.3972 43.4437 34.1223C39.9523 30.6293 36.475 27.127 32.9553 23.6623C32.6452 23.3565 32.0367 23.1001 31.602 23.093C28.9024 23.0484 26.2027 23.0625 23.4397 23.0672C22.9392 23.0672 22.5351 22.6626 22.5351 22.1616L22.5328 22.1592ZM51.1291 20.0635C51.3781 20.0635 51.6295 20.0635 51.8786 20.0635C53.3753 20.0494 55.0505 20.4469 56.3216 19.9035C57.6303 19.3437 58.5678 17.8854 59.6345 16.7869C62.8228 13.5104 62.8557 8.5638 59.7284 5.40251C56.5612 2.19888 51.6084 2.19418 48.2885 5.39545C47.5907 6.06817 46.9281 6.77617 46.2279 7.44418C45.3704 8.26273 44.6349 8.25567 43.7774 7.43242C42.9668 6.65386 42.2032 5.82355 41.3691 5.06851C38.1925 2.19653 33.3736 2.32825 30.345 5.34841C27.2882 8.39445 27.1567 13.2775 30.0772 16.4506C31.1133 17.5772 32.2176 18.6404 33.3125 19.7107C34.0009 20.3834 35.0089 20.1999 35.4318 19.3531C36.6206 16.9798 37.8142 14.6112 38.9866 12.2285C39.3109 11.5699 39.7079 11.0524 40.5162 11.0571C41.3244 11.0618 41.7145 11.5863 42.0363 12.2449C43.2088 14.6253 44.4188 16.9892 45.5771 19.3766C45.8332 19.9059 46.1434 20.0893 46.7237 20.0729C48.1898 20.0305 49.6606 20.0588 51.1291 20.0588V20.0635ZM36.8603 23.0578C39.5035 25.7039 42.2807 28.4818 44.9145 31.1186C47.5907 28.4748 50.3209 25.7768 53.0651 23.0648C50.8307 23.0648 48.554 22.9637 46.289 23.0977C44.6349 23.1965 43.6928 22.6226 43.0654 21.089C42.3629 19.3766 41.4302 17.7584 40.5232 15.9566C39.238 18.4452 38.0374 20.7738 36.8603 23.0554V23.0578ZM49.4092 67.2124C49.6113 66.8407 49.7711 66.4244 50.0577 66.1374C51.747 64.458 52.5764 62.454 52.5083 60.0689C52.4777 59.0057 52.52 57.9425 52.4965 56.8794C52.4096 53.043 49.4656 49.8888 45.6734 49.55C41.839 49.2066 38.3264 51.8316 37.6826 55.6539C37.4571 56.9946 37.5722 58.3941 37.5064 59.7631C37.3819 62.3175 38.2113 64.4744 40.0745 66.2503C40.3588 66.5208 40.4481 66.996 40.7042 67.537H39.4166C34.4191 67.537 29.4216 67.537 24.4241 67.537C21.5553 67.537 19.5089 69.4304 19.5183 72.0578C19.5277 74.6828 21.5718 76.5363 24.4641 76.541C27.4621 76.5433 30.4601 76.541 33.4605 76.541H34.553V88.5181C34.553 89.3367 35.2156 90 36.0333 90C36.8509 90 37.5111 89.339 37.5135 88.5205C37.5135 85.5944 37.5135 79.086 37.5135 75.6236C37.5135 74.0148 37.0506 73.542 35.4717 73.5396C31.8487 73.5373 28.2257 73.5396 24.6027 73.5396C23.24 73.5396 22.5422 73.0386 22.5257 72.0601C22.5116 71.0746 23.2188 70.5406 24.5581 70.5383C26.6515 70.5359 28.7426 70.5383 30.8361 70.5383C34.4591 70.5383 38.0821 70.5312 41.7051 70.543C42.5462 70.5454 43.3615 70.2725 43.4108 69.4281C43.526 67.4993 44.0969 65.4365 42.0857 63.9382C40.8945 63.049 40.4645 61.6919 40.5068 60.1959C40.5373 59.1327 40.4974 58.0695 40.5185 57.0064C40.5679 54.5013 42.5415 52.5373 44.9968 52.5302C47.4591 52.5232 49.4468 54.4707 49.5056 56.9758C49.5314 58.133 49.5079 59.288 49.5126 60.4452C49.5173 61.7271 49.1484 62.9197 48.1593 63.7171C46.7754 64.8343 46.2561 66.1892 46.5075 67.9062C46.5428 68.1509 46.5099 68.4049 46.5122 68.6566C46.5263 70.0067 47.0385 70.5312 48.3801 70.5359C50.5981 70.543 52.8161 70.5359 55.034 70.5359C58.5631 70.5359 62.0921 70.5218 65.6211 70.5548C66.0887 70.5595 66.7324 70.67 66.9721 70.9805C67.2799 71.3804 67.4467 72.0672 67.3457 72.5659C67.1929 73.3303 66.4881 73.5443 65.7315 73.542C62.0145 73.5326 58.2976 73.5373 54.5806 73.5396C52.9758 73.5396 52.5106 74.003 52.5106 75.5907C52.5106 79.0672 52.5271 85.5827 52.5412 88.5017C52.5459 89.3155 53.2061 89.9741 54.0214 89.9741H54.0684C54.886 89.9741 55.5486 89.3108 55.5486 88.4923V76.5386C55.9527 76.5386 56.291 76.5386 56.627 76.5386C59.6556 76.5386 62.6865 76.5457 65.7151 76.5386C68.4946 76.5292 70.5269 74.5934 70.5034 71.9919C70.4799 69.4351 68.457 67.5417 65.7174 67.537C61.7514 67.5299 57.783 67.537 53.817 67.537C52.4143 67.537 51.0116 67.537 49.6113 67.537L49.4163 67.21L49.4092 67.2124ZM62.999 43.5003H74.9981C74.9981 42.6206 74.9981 41.7597 74.9981 40.8988C75.0075 37.8975 76.8566 36.0275 79.8288 36.0181C81.6708 36.0134 83.5152 36.004 85.3573 36.0181C88.0499 36.0393 89.9788 37.9398 89.9859 40.626C90.0047 46.5652 90.0047 52.5067 89.9859 58.4459C89.9765 61.0803 88.0499 62.9973 85.4254 63.0255C83.4589 63.049 81.4899 63.0514 79.5234 63.0255C77.0399 62.9926 75.0428 60.9839 75.0005 58.4976C74.984 57.538 74.9981 56.5759 74.9981 55.5739H62.999C62.999 56.4677 63.0037 57.3615 62.999 58.253C62.9802 61.1297 61.1123 63.0114 58.2506 63.0326C57.8394 63.0349 57.4282 63.0349 57.01 63.0349C56.2135 63.0349 55.5674 62.3881 55.5674 61.5907V61.4778C55.5674 60.6781 56.2159 60.0336 57.0124 60.0336C57.4259 60.0336 57.8394 60.0336 58.2529 60.0265C59.6345 60.003 60.1772 59.2503 59.9399 57.6109C59.1575 52.2103 59.3267 46.7345 59.961 41.3128C60.142 39.7627 59.6039 39.0312 58.2764 39.0195C56.4344 39.003 54.59 39.003 52.7479 39.0195C51.5426 39.0289 51.0116 39.5863 51.0046 40.8306C50.9975 42.2231 50.9999 43.6179 50.9999 45.0292C50.9999 45.8266 50.3537 46.4758 49.5572 46.4758C48.7725 46.4758 48.1334 45.8478 48.117 45.0621C48.0747 43.1969 47.9924 41.3316 48.1922 39.4993C48.4224 37.4176 50.3467 36.0487 52.4683 36.0205C54.496 35.9922 56.526 35.9875 58.556 36.0205C60.9831 36.0628 62.9262 38.0245 62.9896 40.4543C63.0154 41.4445 62.9943 42.4348 62.9943 43.4956L62.999 43.5003ZM86.9949 49.5265C86.9949 46.6193 87.002 43.712 86.9902 40.8024C86.9855 39.5769 86.4357 39.0312 85.2116 39.0218C83.4001 39.0101 81.5886 39.0101 79.7771 39.0218C78.5506 39.0312 78.0009 39.5722 78.0009 40.8C77.9938 46.6146 77.9938 52.4314 78.0009 58.246C78.0009 59.4714 78.553 60.0171 79.7771 60.0265C81.5581 60.0406 83.339 60.0359 85.12 60.0265C86.4639 60.0195 86.9902 59.4949 86.9949 58.1519C87.002 55.2752 86.9949 52.3985 86.9949 49.5218V49.5265ZM63.0507 46.5652V52.4738H74.9324V46.5652H63.0507ZM2.19743 52.5396L2.16689 52.5443C1.35394 52.6855 0.813547 53.4781 0.987413 54.2873C2.72373 62.3693 6.33028 69.421 11.8964 75.4919C16.8469 80.8925 22.7536 84.8206 29.6284 87.335C30.4014 87.6173 31.2566 87.1939 31.508 86.4083L31.5174 86.38C31.7524 85.6509 31.3811 84.8652 30.6692 84.5783C16.4122 78.872 7.47921 68.6072 3.85856 53.6334C3.67764 52.8878 2.95398 52.4056 2.19743 52.5373V52.5396ZM81.1046 66.5161C75.9215 75.0497 68.6779 81.0407 59.3807 84.5383C58.6406 84.8159 58.2553 85.632 58.5137 86.38L58.5396 86.4529C58.8074 87.2268 59.6626 87.6314 60.4286 87.3421C70.386 83.5951 78.1089 77.2114 83.6609 68.0685C84.0862 67.3676 83.8418 66.4503 83.1252 66.0551L83.0312 66.0033C82.3546 65.627 81.504 65.8575 81.1023 66.5185L81.1046 66.5161ZM78.9078 20.2187C81.5651 23.8481 83.5599 27.8115 84.972 32.09C85.2187 32.8357 86.0081 33.2543 86.7623 33.0332L86.8352 33.0121C87.6269 32.7792 88.071 31.9348 87.8102 31.1539C84.6665 21.7288 79.1216 14.1619 71.1026 8.34741C70.4212 7.85345 69.4626 8.03457 68.9997 8.73786L68.9786 8.76844C68.5416 9.4341 68.7131 10.3232 69.3592 10.7866C73.0692 13.4469 76.2363 16.5752 78.9054 20.2211L78.9078 20.2187ZM5.40221 26.9953L5.4727 27.0282C6.18696 27.3551 7.0281 27.0564 7.38288 26.3531C10.5477 20.0494 14.9743 14.8746 20.6837 10.7654C21.3204 10.3067 21.4849 9.43175 21.0525 8.77785L21.0103 8.71199C20.5568 8.02281 19.6193 7.8464 18.9473 8.32859C12.8103 12.7365 8.09244 18.2641 4.70675 25.0195C4.33787 25.7557 4.65506 26.6542 5.40456 26.9976L5.40221 26.9953Z" fill="white"></path>                    </svg>
                            </div>
                            <h3 class="culture-title">Wellness</h3>
                            <p class="culture-description">A healthy human being is healthy physically, mentally, emotionally, and spiritually. Our leadership team takes the time to focus on their wellness and encourages everyone to pursue theirs. Our wellness program promotes all aspects of health.</p>
                        </div>
                    </div>
                </section>

                <!-- Diversity and Inclusion Section -->
                <section class="diversity-inclusion">
                    <div class="diversity-content">
                        <div class="diversity-icon">
                            <svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M10.7125 51.2872C11.184 51.4993 11.6414 51.603 12.0657 51.603C12.4901 51.603 12.8885 51.4946 13.2233 51.2777C13.8881 50.8488 14.2559 50.0427 14.2606 49.0127C14.27 46.2339 14.2676 43.4267 14.2606 41.0462C14.2606 39.9573 13.8904 39.1206 13.2233 38.6916C12.5537 38.2603 11.6343 38.2721 10.63 38.7223C9.54555 39.2102 8.4611 39.7051 7.379 40.1977C5.57551 41.0203 3.71308 41.8688 1.8695 42.6819C0.914713 43.1038 0.341839 43.6365 0.00943003 44.4143L0 45.5598L0.0330051 45.5951C0.186243 45.753 0.334766 45.9322 0.476217 46.1066C0.796838 46.4978 1.12925 46.9032 1.56539 47.1106C4.28595 48.4188 7.08903 49.6703 9.79781 50.8818L10.7102 51.2895L10.7125 51.2872ZM3.79087 44.9682L11.3538 41.5412V48.4305L10.175 47.8979C8.16641 46.9904 6.20025 46.1019 4.23409 45.2109C4.128 45.1615 4.02191 45.1025 3.8781 45.02L3.79087 44.9705V44.9682ZM44.3754 14.2336C44.5876 14.2525 44.7997 14.2619 45.0119 14.2619C48.2063 14.2619 51.0377 12.1147 51.8864 8.95875C52.7964 5.56946 51.1202 2.06704 47.9022 0.631662C47.5439 0.471389 47.169 0.348828 46.8437 0.247479C46.325 0.0824931 45.7852 0 45.2429 0H44.7408C44.2882 0 43.8096 0.0683514 43.2674 0.209768C39.8608 1.09598 37.5999 4.26136 37.8922 7.73314C38.1775 11.1342 40.9664 13.9296 44.3777 14.2312L44.3754 14.2336ZM40.7377 7.12976C40.7377 5.99136 41.1833 4.91895 41.9943 4.11287C42.8006 3.30915 43.8685 2.8684 45.0025 2.8684H45.019C46.1553 2.87312 47.2233 3.32329 48.0248 4.13408C48.8311 4.94959 49.2696 6.02671 49.2601 7.16983C49.2507 8.30117 48.7981 9.36651 47.98 10.1655C47.1596 10.9692 46.0775 11.4053 44.9365 11.3911C42.6214 11.3628 40.7377 9.45135 40.7377 7.13212V7.12976ZM74.5421 52.1663C73.6839 51.3178 73.2902 50.3774 73.3044 49.206C73.3327 46.7547 73.3562 43.8604 73.2973 40.9661C73.2714 39.6556 73.7193 38.595 74.7024 37.6334C77.0245 35.366 79.5093 32.8888 82.5246 29.8366C84.2762 28.0642 84.3069 25.4267 82.5977 23.7038C80.9003 21.995 78.2599 22.0162 76.4564 23.7533C75.6949 24.4863 74.9405 25.2476 74.2073 25.9853L73.7217 26.4756C72.8918 27.3099 72.7834 28.1278 73.4199 28.776C73.6839 29.0447 74.0069 29.1884 74.3511 29.1908H74.3605C74.8108 29.1908 75.2823 28.9574 75.7303 28.5167C76.0674 28.182 76.4021 27.8473 76.7369 27.5103C77.3192 26.9234 77.9204 26.3177 78.5286 25.7426C79.1581 25.1486 79.9738 25.1439 80.5608 25.7331C81.1313 26.3059 81.1242 27.1049 80.5419 27.7719C80.4335 27.8968 80.3109 28.017 80.193 28.1325L80.0563 28.2669C79.3278 28.9928 78.6017 29.7211 77.8732 30.4494C76.1334 32.1935 74.3323 33.9942 72.5453 35.7525C71.1025 37.1714 70.4117 38.8118 70.4376 40.7634C70.4706 43.382 70.4706 46.1608 70.4376 49.2602C70.4188 51.174 71.0789 52.7673 72.458 54.1296C73.8914 55.5462 75.3389 56.9933 76.7393 58.3957C77.3192 58.9755 77.8968 59.5553 78.4768 60.1328C78.6795 60.3355 78.8822 60.5358 79.085 60.7362C79.5589 61.2028 80.0516 61.6884 80.5136 62.181C81.1195 62.8268 81.1478 63.6258 80.582 64.215C79.9997 64.8231 79.1746 64.816 78.4791 64.1985C78.3565 64.0901 78.2386 63.9675 78.1231 63.852L77.984 63.713C77.14 62.8692 76.2961 62.023 75.4544 61.1793C73.3633 59.0792 71.2015 56.9085 69.0326 54.8155C68.363 54.1697 67.6086 53.5946 66.8802 53.0384C66.4204 52.6895 65.9466 52.3266 65.501 51.9542C65.3219 51.8033 65.1663 51.4404 65.2111 51.2848C66.4535 47.1036 66.4535 42.8681 65.2087 38.7011C65.1545 38.5219 65.2865 38.1495 65.3997 38.0883C67.2008 37.0889 68.6318 35.63 70.0157 34.2205C70.3316 33.8976 70.6475 33.5771 70.9657 33.2612C71.3476 32.8841 71.548 32.4693 71.5457 32.0615C71.5457 31.708 71.3901 31.3686 71.1001 31.0858C70.4801 30.4777 69.6667 30.5437 68.9288 31.2625C68.6153 31.5666 68.3065 31.8824 68.0071 32.1865C67.5238 32.6814 67.0216 33.1905 66.5006 33.6572C65.9419 34.1569 65.3549 34.6118 64.7325 35.0926C64.4897 35.2788 64.2468 35.4697 63.9993 35.6653C61.762 31.3851 58.5817 28.2103 54.2864 25.9618C54.5386 25.6601 54.7791 25.3631 55.0148 25.0732C55.5759 24.3826 56.104 23.7274 56.691 23.1311C59.8595 19.9044 63.1388 16.64 66.0338 13.7599C66.9815 12.8171 67.4743 11.8154 67.5403 10.6958C67.644 8.94226 66.6491 7.27118 65.0625 6.54053C63.3958 5.77216 61.5475 6.09978 60.2367 7.39374C57.436 10.1608 54.6093 13.0056 52.1811 15.4545C51.3442 16.2983 50.4648 16.6943 49.4157 16.6943H49.3945C46.3015 16.6778 43.2013 16.6848 40.5397 16.6919H40.5303C39.5307 16.6919 38.6891 16.336 37.9606 15.6006C37.8498 15.4899 37.7414 15.3767 37.6306 15.2636C37.2958 14.9195 36.9469 14.5636 36.5744 14.2477C35.9567 13.7245 35.266 13.7504 34.679 14.3161C34.1084 14.8676 34.0471 15.5841 34.521 16.1404C36.0699 17.9623 37.8852 19.6357 40.7283 19.5697C43.4489 19.5014 46.219 19.5297 48.8971 19.5579H49.2248C51.1744 19.5768 52.8176 18.8886 54.2368 17.4461C55.9272 15.7255 57.6623 13.9932 59.3409 12.3174C60.0316 11.6268 60.7224 10.9362 61.4131 10.2456C61.4956 10.1632 61.5758 10.0807 61.6559 9.99817C61.8563 9.79311 62.0614 9.57863 62.2807 9.39479C62.9078 8.86448 63.7022 8.88569 64.2539 9.44428C64.8079 10.0029 64.8221 10.7807 64.2916 11.4265C64.1879 11.5538 64.0653 11.6716 63.9474 11.7871C63.9026 11.8319 63.8555 11.8767 63.8107 11.9214C63.0374 12.6969 62.2618 13.4676 61.4862 14.2407C59.7039 16.0155 57.8603 17.8492 56.0804 19.6876C55.003 20.8001 53.9822 21.9526 52.9025 23.1735C52.4216 23.7156 51.9336 24.2695 51.4267 24.8328C47.1667 23.5129 42.8406 23.5105 38.5688 24.8281C38.4769 24.8587 38.1681 24.6796 37.9936 24.4675C37.6541 24.055 37.3264 23.6166 37.0082 23.1947C36.4612 22.4688 35.8978 21.7169 35.2565 21.057C33.1819 18.9169 31.0366 16.7791 28.962 14.7121C27.9483 13.7009 26.9322 12.6898 25.9232 11.674C25.1711 10.9174 25.098 10.0877 25.7298 9.449C26.3546 8.81734 27.1844 8.88569 27.9506 9.6352C28.2712 9.94867 28.5848 10.2669 28.9007 10.585C29.3604 11.0494 29.8366 11.5302 30.3199 11.9874C30.9612 12.5932 31.7816 12.6073 32.3639 12.0275C32.9627 11.4312 32.9509 10.6322 32.3332 9.98638C31.4916 9.10724 30.5392 8.11025 29.5302 7.20047C27.9247 5.75331 25.487 5.77923 23.858 7.2594C22.9669 8.07018 22.4576 9.14967 22.427 10.2999C22.394 11.4948 22.8796 12.6615 23.7896 13.5831C25.2018 15.0137 26.6469 16.4586 28.0449 17.8562C29.8154 19.6263 31.6472 21.4576 33.4295 23.2772C33.9976 23.857 34.4927 24.491 35.0137 25.1604C35.2235 25.4291 35.4357 25.7001 35.6502 25.9688C30.5981 29.0399 30.5368 29.1036 25.9609 35.564C25.7086 35.3566 25.454 35.1515 25.1971 34.9488C24.5888 34.4633 23.9594 33.9636 23.403 33.4144C20.3194 30.3858 17.2169 27.2722 14.2158 24.2624L13.8433 23.89C12.6645 22.7068 11.3208 22.2472 9.85203 22.523C8.17584 22.8365 7.02066 23.8523 6.50908 25.455C5.99514 27.0648 6.36056 28.5568 7.56289 29.7706C10.0642 32.2972 12.7023 34.9323 15.6256 37.8219C16.479 38.6657 16.8703 39.6085 16.8562 40.787C16.8255 43.3843 16.8043 46.1702 16.8633 48.9396C16.8916 50.3043 16.4436 51.3909 15.4488 52.3572C12.5514 55.1808 9.74594 58.0068 7.62654 60.154C6.70475 61.0873 6.23325 62.28 6.30162 63.5126C6.36763 64.71 6.93815 65.8012 7.90473 66.5861C9.66578 68.0144 12.0799 67.84 13.7749 66.1595C15.1116 64.8349 16.4436 63.5032 17.7756 62.1715C19.6145 60.3331 21.517 58.4311 23.4053 56.5761C23.9735 56.0175 24.6147 55.5084 25.1806 55.0583C25.3998 54.8839 25.612 54.7165 25.81 54.5515C26.2509 55.1148 26.6729 55.6829 27.0807 56.2344C28.0614 57.5566 28.9903 58.8082 30.0936 59.9065C31.2134 61.019 32.4794 61.9547 33.8185 62.9423C34.3725 63.35 34.943 63.7719 35.5088 64.2126C35.3579 64.3988 35.2 64.5945 35.0396 64.7972C34.5658 65.3888 34.0778 65.9992 33.5521 66.6002C33.2479 66.9467 32.9108 67.2767 32.5831 67.5949C32.3356 67.8376 32.0763 68.0875 31.8358 68.3467C31.2252 68.9996 31.1875 69.801 31.7439 70.3879C32.0409 70.7037 32.4157 70.8852 32.8047 70.871C33.1725 70.8663 33.512 70.706 33.7878 70.4043C35.2212 68.8205 36.6168 67.1895 37.93 65.5561C38.2388 65.1719 38.3967 65.0824 38.8541 65.2214C42.1216 66.2231 45.4315 66.4188 48.692 65.8036C49.437 65.6622 50.1678 65.4878 50.8137 65.3322C51.0518 65.2756 51.2758 65.2214 51.4833 65.1743C51.9218 65.674 52.3438 66.1666 52.761 66.6497C53.7017 67.7457 54.5881 68.7781 55.5429 69.7656C57.2969 71.5805 59.1169 73.3906 60.8756 75.1418C61.6347 75.896 62.3915 76.6503 63.1482 77.4068C63.2543 77.5153 63.3651 77.6213 63.4736 77.7274C63.7353 77.9843 64.0064 78.2483 64.2515 78.5264C64.8268 79.1769 64.8197 79.9971 64.2398 80.5651C63.6575 81.1355 62.8748 81.1214 62.2005 80.5251C62.0756 80.4143 61.9553 80.2917 61.8398 80.1739L61.7054 80.0372C60.977 79.3089 60.2461 78.5806 59.5177 77.8523C57.7943 76.1341 56.012 74.3569 54.284 72.5892C52.8624 71.135 51.2122 70.4279 49.2389 70.4279C49.1988 70.4279 49.1564 70.4279 49.114 70.4279C46.3227 70.4703 43.5503 70.4703 40.8792 70.4279C38.8541 70.3973 37.1614 71.102 35.7092 72.5869C33.9175 74.4159 32.0715 76.2566 30.2893 78.0338C29.5797 78.7409 28.8701 79.448 28.1628 80.1574C27.2245 81.0978 26.4347 81.2322 25.7534 80.5675C25.4611 80.2823 25.3126 79.9736 25.3102 79.6459C25.3079 79.184 25.5908 78.6607 26.1542 78.0951C26.4984 77.7509 26.8426 77.4068 27.1891 77.0627C27.9294 76.3274 28.6956 75.5661 29.4311 74.8C29.7494 74.4701 29.9191 74.0647 29.9074 73.6593C29.8979 73.2892 29.7376 72.9428 29.4571 72.6812C28.8559 72.1202 28.052 72.1603 27.4155 72.7872C26.2367 73.9421 24.8717 75.2879 23.5822 76.6432C22.6179 77.6567 22.2502 78.9247 22.5189 80.3082C22.8443 81.9793 23.8627 83.1319 25.4658 83.6386C25.935 83.7871 26.3923 83.8602 26.8379 83.8602C27.9176 83.8602 28.9172 83.4265 29.7801 82.5733C32.1281 80.2493 34.9006 77.4964 37.6423 74.6916C38.6066 73.7064 39.6698 73.2586 40.9759 73.2822C43.7059 73.334 46.3911 73.334 48.956 73.2822C50.3116 73.2492 51.396 73.7064 52.3721 74.7058C54.8309 77.2277 57.4548 79.8581 60.1707 82.5261C61.2551 83.5915 62.6956 84.0393 64.1219 83.7564C65.5081 83.4807 66.6303 82.5591 67.1984 81.2227C67.9316 79.4974 67.5403 77.7486 66.0951 76.2944C64.9352 75.1277 63.773 73.9657 62.6107 72.8037C60.6446 70.8404 58.6124 68.8087 56.6344 66.7912C56.0521 66.1972 55.5358 65.5467 54.9889 64.8585C54.7626 64.5733 54.5315 64.2834 54.2934 63.9911C58.5817 61.7897 61.7667 58.6102 64.0229 54.2781C64.3223 54.5256 64.6146 54.7613 64.9022 54.9923C65.5812 55.5367 66.2224 56.0529 66.8047 56.6233C69.8058 59.5695 72.8258 62.5981 75.7444 65.5255L76.3031 66.0864C77.2061 66.9939 78.1891 67.4652 79.3042 67.5312C81.0512 67.6349 82.7179 66.6356 83.4558 65.047C84.2291 63.3877 83.899 61.5399 82.5977 60.2223C80.2425 57.8395 77.6823 55.2798 74.535 52.171L74.5421 52.1663ZM33.9033 58.622C34.2452 57.1748 35.0043 56.06 36.0392 55.4872C36.5602 55.1997 37.1308 55.0536 37.7343 55.0536C38.1751 55.0536 38.6348 55.1313 39.0993 55.2892C39.8042 55.5273 40.4407 56.1213 40.9829 56.6586C41.2705 56.9438 41.4874 57.2974 41.7161 57.6698C41.8576 57.9007 42.0061 58.1412 42.1711 58.3627C43.5243 60.1658 45.3773 60.8634 47.6853 60.4321C47.8928 60.3944 48.1167 60.3449 48.3737 60.286C49.1965 60.0927 49.7859 60.1516 50.1796 60.4627C50.5898 60.7856 50.7949 61.3914 50.809 62.3106C48.5835 63.0884 46.2355 63.3995 43.8284 63.2392C40.4289 63.0106 37.2486 61.8816 34.3725 59.8829C33.8845 59.5435 33.7619 59.2159 33.901 58.6267L33.9033 58.622ZM47.7678 57.4694C47.6429 57.5001 47.5203 57.5307 47.3954 57.559C45.8583 57.9055 45.118 57.6132 44.2245 56.3098C44.0996 56.126 43.977 55.9398 43.8567 55.7536C43.7224 55.5485 43.5903 55.3435 43.4489 55.1408C42.0485 53.1185 39.79 52.0107 37.416 52.1781C35.0396 52.3454 32.9627 53.7549 31.857 55.9516C31.725 56.2132 31.6095 56.4795 31.4775 56.7883C31.4327 56.892 31.3855 57.0004 31.336 57.1159C28.7545 54.1391 27.2882 50.8653 26.8497 47.1154C27.0642 47.1436 27.274 47.1743 27.4768 47.2049C27.9978 47.2827 28.4881 47.3558 28.9809 47.3699C31.7816 47.4453 33.5803 45.9722 34.0519 43.2123C34.1603 42.5735 34.2216 41.9159 34.2805 41.2819C34.323 40.8294 34.3654 40.3604 34.4267 39.9055C34.4927 39.4152 34.5847 38.8425 34.7827 38.3192C35.0821 37.5249 35.6715 37.1855 36.5272 37.3128C36.9728 37.3788 37.4231 37.4754 37.8592 37.5674C38.1799 37.6357 38.5123 37.7088 38.8423 37.7654C39.7759 37.9327 40.469 37.5179 40.6529 36.6859C40.825 35.901 40.3558 35.2293 39.4859 35.0124C38.6089 34.7932 37.6683 34.5764 36.73 34.4656C34.5469 34.2087 32.7316 35.3495 32.1046 37.3694C31.6896 38.7058 31.527 40.1105 31.4138 41.428C31.2818 42.9624 30.9517 43.8345 30.3435 44.2493C29.7423 44.6618 28.8276 44.6571 27.3778 44.2328C26.8284 44.0725 26.7035 43.884 26.7695 43.323C27.7526 34.8592 33.5709 28.5733 41.9542 26.9187C43.8662 26.5416 45.217 27.0389 46.3345 28.5308C47.2539 29.7612 47.2845 31.1305 46.4146 32.2854C45.903 32.9666 45.2241 33.5441 44.5663 34.105L44.5074 34.1545C43.7011 34.8427 43.522 35.5946 44.0005 36.2734C44.5027 36.9828 45.3561 37.0677 46.1718 36.4902C47.8904 35.2788 50.1395 33.2801 49.8354 29.9285C49.7717 29.2356 49.5313 28.5638 49.3002 27.911C49.213 27.6682 49.1305 27.4372 49.0574 27.2062C52.0915 27.4136 57.0706 30.5484 59.3031 33.6737C58.1385 34.7932 57.436 36.0189 57.1531 37.4189C56.3044 41.6331 58.2894 45.6635 62.2099 47.6857C62.9361 48.0605 63.0021 48.3834 62.8229 49.1023C61.5828 54.0707 58.7397 57.9597 54.3783 60.6607C54.2015 60.7715 54.0034 60.8799 53.7748 60.9978C53.7418 61.0143 53.6946 61.0237 53.6357 61.0355H53.6168C53.5767 60.9318 53.539 60.8281 53.5013 60.722C53.3952 60.4298 53.2868 60.1257 53.1571 59.8358C52.3862 58.1247 51.0707 57.2219 49.3898 57.2219C49.0268 57.2219 48.6472 57.2644 48.2511 57.3492C48.0908 57.3846 47.9305 57.4223 47.7702 57.4624L47.7678 57.4694ZM60.9982 36.1721C62.4763 38.8354 63.2284 41.718 63.2897 44.9729C61.5098 44.0749 60.1825 42.154 59.8831 40.0233C59.6615 38.4394 60.067 37.0465 60.9982 36.1744V36.1721ZM11.995 63.9015C11.863 64.0382 11.7239 64.1796 11.5777 64.2975C10.9412 64.8137 10.1444 64.7854 9.59977 64.2291C9.03869 63.6564 9.03162 62.8668 9.58092 62.2611C10.0123 61.7874 10.4791 61.3301 10.9318 60.887C11.118 60.7032 11.3043 60.5217 11.4905 60.3378C12.0681 59.7627 12.6433 59.1853 13.2186 58.6102C14.6826 57.1465 16.1961 55.631 17.6931 54.1532C19.0817 52.7838 19.7489 51.1929 19.7323 49.2908C19.7064 46.3588 19.7064 43.4715 19.7323 40.7068C19.7512 38.7977 19.0887 37.2068 17.7096 35.8397C16.1984 34.3431 14.6944 32.8393 13.1926 31.338C12.0728 30.2184 10.953 29.0989 9.83081 27.984C9.16128 27.317 9.00568 26.8056 9.3098 26.2658C9.4819 25.9618 9.76952 25.7214 10.1019 25.4409C10.2387 25.3254 10.3801 25.2075 10.5216 25.0779C10.7054 25.1958 10.8775 25.2995 11.0402 25.3985C11.3891 25.6082 11.6909 25.7897 11.9266 26.0207C13.169 27.2463 14.4209 28.5002 15.635 29.7117C17.4267 31.5053 19.2797 33.3579 21.1304 35.1539C22.0639 36.0589 23.1295 36.8485 24.1645 37.6122L24.544 37.8926C24.8859 38.1472 24.9401 38.3027 24.8199 38.6893C23.5256 42.8564 23.5232 47.0824 24.8128 51.2518C24.9472 51.6878 24.8552 51.8387 24.4898 52.1027L24.3861 52.1781C23.3441 52.9276 22.2643 53.7054 21.3331 54.6105C19.11 56.7718 16.8821 59.0062 14.7297 61.1651C13.8669 62.0301 13.004 62.8951 12.1388 63.7578C12.0917 63.8049 12.0445 63.852 11.9974 63.9015H11.995ZM49.5902 76.6055C49.312 76.1671 48.626 75.7782 48.0908 75.7546C46.7588 75.6981 45.4033 75.7028 44.0925 75.7098C43.3829 75.7122 42.6733 75.7169 41.9613 75.7098H41.9401C41.2281 75.7098 40.7095 76.0092 40.3558 76.6243L40.0234 77.2041C39.1346 78.7527 38.2152 80.3554 37.2958 81.9227C36.9257 82.552 36.9257 83.1389 37.2958 83.7659C38.2859 85.4511 39.2737 87.1764 40.2285 88.8427L40.3558 89.0619C40.7189 89.6936 41.2352 89.9906 41.9613 89.9788C42.6756 89.9623 43.4017 89.967 44.1043 89.9694C44.4107 89.9694 44.7149 89.9717 45.0213 89.9717H45.0261C45.3231 89.9717 45.6201 89.9788 45.9172 89.9859C46.2119 89.9929 46.5113 90 46.8107 90C47.2398 90 47.6712 89.9859 48.0932 89.934C48.5953 89.8727 49.2884 89.5545 49.5878 89.0855C50.6864 87.3602 51.7285 85.5501 52.6903 83.7046C52.9331 83.2379 52.9355 82.4507 52.6903 81.9864C51.7308 80.1432 50.6864 78.3331 49.5878 76.6055H49.5902ZM44.0288 78.4792C44.6489 78.517 45.2925 78.5547 45.9361 78.5075C47.136 78.4133 47.8268 79.1486 48.6826 80.5345C49.536 81.9204 49.569 83.6433 48.7651 85.0339C47.8433 86.6296 46.905 87.174 45.0496 87.1929C43.1235 87.2023 42.1546 86.6602 41.2258 85.0103C40.2875 83.344 40.2898 82.2127 41.2328 80.6901C42.032 79.3985 42.6992 78.4745 43.885 78.4745C43.9322 78.4745 43.9793 78.4745 44.0288 78.4792ZM89.9979 45.0083C89.9979 44.5369 89.9979 44.0655 89.9979 43.5941C90.0003 42.5358 90.0026 41.4422 89.9932 40.3651C89.9767 38.7788 89.0573 37.8667 87.4659 37.8596C84.413 37.8478 81.3199 37.8478 78.2669 37.8596C76.6921 37.8667 75.7468 38.8024 75.7397 40.3627C75.7279 43.2146 75.7279 46.2244 75.7397 49.5595C75.7444 51.1764 76.6827 52.105 78.3094 52.1121C79.8205 52.1168 81.3435 52.1192 82.8641 52.1192C84.3847 52.1192 85.9076 52.1168 87.4188 52.1121C89.0643 52.1074 89.979 51.2023 89.9932 49.5642C90.0026 48.5107 90.0003 47.4383 89.9979 46.4012C89.9979 45.9369 89.9979 45.4726 89.9979 45.0106V45.0083ZM87.084 40.7799V49.2107H78.6677V40.7799H87.084ZM54.7956 43.1038C54.1331 42.2412 53.15 41.4422 52.0986 40.9095C50.5921 40.1482 49.1682 40.5253 48.0932 41.9701C47.261 43.0897 46.292 43.7331 45.0473 43.9971C43.9015 44.2399 43.0858 44.9894 42.6261 46.2221C42.098 47.6339 42.2041 48.9773 42.9232 50.0026C43.6634 51.0609 45.0025 51.6643 46.6032 51.6643C46.6174 51.6643 46.6339 51.6643 46.648 51.6643H46.6645C46.8626 51.636 47.0606 51.6124 47.2586 51.5888C47.6924 51.5393 48.1403 51.4851 48.5741 51.3885C50.3045 50.9949 52.5135 50.3326 54.2368 48.6874C55.9649 47.0399 56.1794 44.8998 54.7979 43.0991L54.7956 43.1038ZM46.2732 46.6369C47.9305 46.1867 49.213 45.3076 50.0806 44.0254C50.2833 43.7261 50.4884 43.5611 50.71 43.5187C50.7501 43.5116 50.7878 43.5069 50.8302 43.5069C51.0448 43.5069 51.2876 43.6082 51.6035 43.8251C52.3791 44.3554 52.7846 44.9423 52.7775 45.5197C52.7705 46.0854 52.365 46.6487 51.6059 47.1436C50.1631 48.0841 48.5034 48.6309 46.6716 48.7676C46.5938 48.7652 46.5184 48.7629 46.4429 48.7605C45.7192 48.7393 45.3042 48.6945 45.1958 48.0228C45.0756 47.2898 45.3985 46.875 46.2755 46.6369H46.2732Z" fill="#0072DA"></path>                            </svg>
                        </div>
                        <h2 class="diversity-title">Diversity and Inclusion</h2>
                        <p class="diversity-description">
                            Krystelis is an equal opportunity employer. We value people from all backgrounds and experiences and we keep diversity, inclusion, equity, and mutual respect top of our minds. We are committed to providing a workplace that is free from discrimination and abusive, offensive, or harassing behaviour.
                        </p>
                        <p class="diversity-description">
                            Krystelis does not discriminate in employment based on race, colour, religion, sex (including pregnancy and gender identity), national origin, political affiliation, sexual orientation, marital status, disability, genetic information, age, membership in an employee organisation, retaliation, parental status, military service, or other non-merit factors. Learn more about our <a href="#" class="business-code-link">Business Code of Conduct</a>.
                        </p>
                    </div>
                </section>

                <!-- Employee Testimonials Section -->
                <section class="employee-testimonials">
                    <h2 class="testimonials-heading">What our employees are saying about us...</h2>

                    <div class="testimonials-slider">
                        <div class="testimonial-container">
                            <!-- Slide 1 -->
                            <div class="testimonial-slide active">
                                <div class="testimonial-content">
                                    <div class="testimonial-text">
                                        <p>"If I have to zero down to a single best thing I love about Krystelis, it would be the "space to think and to work" that I have received here. It's pivotal for me to have that space and a culture of mutual respect at work. Krystelis has been standing tall and true to "respect", which is one of our core values. It really makes a difference in my motivation, productivity, and happiness."</p>
                                        <div class="testimonial-author">
                                            <span class="author-name">Kamakshi Sharma</span>
                                            <span class="author-title">Senior Associate (PLS)</span>
                                        </div>
                                    </div>
                                    <div class="testimonial-image">
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Kamakshi-300x300.jpg" alt="Kanakshi Sharma">
                                    </div>
                                </div>
                            </div>

                            <!-- Slide 2 -->
                            <div class="testimonial-slide">
                                <div class="testimonial-content">
                                    <div class="testimonial-text">
                                        <p>"The best part of working at Krystelis is the opportunity to constantly learn and grow, which helps me develop both professionally and personally. I enjoy the collaborative environment in my workplace, and working with supportive colleagues makes each day enjoyable. Also, I appreciate the work-life balance that my job offers, which allows me to maintain a healthy lifestyle."</p>
                                        <div class="testimonial-author">
                                            <span class="author-name">Ridhima Mehra</span>
                                            <span class="author-title">Principal Medical Writer (CTT)</span>
                                        </div>
                                    </div>
                                    <div class="testimonial-image">
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Ridhima-Mehra-281x300.jpg" alt="Sushma Mehra">
                                    </div>
                                </div>
                            </div>

                            <!-- Slide 3 -->
                            <div class="testimonial-slide">
                                <div class="testimonial-content">
                                    <div class="testimonial-text">
                                        <p>"Krystelis, as the name suggests, stands out with its crystal-clear values and vision for the company and its employees.</p>
                                        <p>The leadership team here is the best example of influential leaders who foster an amazing work culture and energise us to help achieve the company's vision and goals with enthusiasm. I am really grateful to be part of this team and am fully committed to offer my contribution in its successful journey".</p>
                                        <div class="testimonial-author">
                                            <span class="author-name">Ankit Gupta</span>
                                            <span class="author-title">Senior Manager (CTT)</span>
                                        </div>
                                    </div>
                                    <div class="testimonial-image">
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Ankit-Gupta-300x300.jpg" alt="Ankit Gupta">
                                    </div>
                                </div>
                            </div>

                            <!-- Slide 4 -->
                            <div class="testimonial-slide">
                                <div class="testimonial-content">
                                    <div class="testimonial-text">
                                        <p>"I have been happily working with Krystelis for the past 7 months, and I would like to express my gratitude towards the leadership team for providing me equal opportunities, constant support, a favourable environment, openness to work, and sufficient time for personal growth.</p>
                                        <p>Krystelis not only makes clinical research crystal clear but also cultivates a crystal clear environment and work culture for their employees and clients."</p>
                                        <div class="testimonial-author">
                                            <span class="author-name">Kamlesh Choudhary</span>
                                            <span class="author-title">Senior Associate (CTT)</span>
                                        </div>
                                    </div>
                                    <div class="testimonial-image">
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Kamlesh.jpg" alt="Kamlesh Choudhary">
                                    </div>
                                </div>
                            </div>

                            <!-- Slide 5 -->
                            <div class="testimonial-slide">
                                <div class="testimonial-content">
                                    <div class="testimonial-text">
                                        <p>"At Krystelis, I love the collaborative and supportive environment. Every day brings new challenges and opportunities to grow, and I am surrounded by a team that feels like family."</p>
                                        <div class="testimonial-author">
                                            <span class="author-name">Lakshmi Siva</span>
                                            <span class="author-title">Principal Medical Writer (Writing Services)</span>
                                        </div>
                                    </div>
                                    <div class="testimonial-image">
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Lakshmi-Siva.jpg" alt="Lakshmi Siva">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Navigation arrows -->
                        <button class="testimonial-nav prev" onclick="changeSlide(-1)">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                        <button class="testimonial-nav next" onclick="changeSlide(1)">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                </section>

                                  <!-- Event Gallery Section Start -->
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }

                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                        background-color: #f9fafb;
                        min-height: 100vh;
                    }

                    /* Header */
                    .header {
                        background: white;
                        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                        border-bottom: 1px solid #e5e7eb;
                        padding: 1rem 0;
                    }

                    <!-- .header-content {
                        max-width: 1280px;
                        margin: 0 auto;
                        padding: 0 1rem;
                    } -->

                    .header h1 {
                        font-size: 1.5rem;
                        font-weight: 700;
                        color: #111827;
                        margin-bottom: 0.25rem;
                    }

                    .header p {
                        color: #6b7280;
                        font-size: 0.875rem;
                    }

                    /* Main Gallery Container */
                    .gallery-container {
                        <!-- max-width: 1280px; -->
                        margin: 0 auto;
                        padding: 2rem 1rem;
                    }

                    /* Carousel Container */
                    .carousel-container {
                        position: relative;
                        padding: 0 3rem;
                    }

                    /* Navigation Arrows */
                    .carousel-nav {
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        z-index: 10;
                        background: white;
                        border: 1px solid #e5e7eb;
                        border-radius: 50%;
                        width: 48px;
                        height: 48px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        transition: all 0.3s ease;
                    }

                    .carousel-nav:hover {
                        background: #f9fafb;
                        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
                    }

                    .carousel-nav:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }

                    .carousel-nav.prev {
                        left: 0;
                    }

                    .carousel-nav.next {
                        right: 0;
                    }

                    .carousel-nav svg {
                        width: 24px;
                        height: 24px;
                        color: #374151;
                    }

                    /* Images Container */
                    .images-container {
                        <!-- overflow: hidden; -->
                        border-radius: 15px;
                    }

                    .images-track {
                        display: flex;
                        transition: transform 0.3s ease-in-out;
                    }

                    .image-item {
                        flex: none;
                        padding: 0 0.5rem;
                        width: 100%;
                    }

                    .image-card {
                        position: relative;
                        background: white;
                        border-radius: 15px;
                        overflow: hidden;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        transition: all 0.3s ease;
                        cursor: pointer;
                        height: 280px;
                        display: flex;
                        align-items: stretch;
                  
                    }

                    .image-card:hover {
                        transform: scale(1.05);
                        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
                    }

                    .image-card img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        display; block;
                    }

                    <!-- .image-overlay {
                        position: absolute;
                        inset: 0;
                        background: rgba(0, 0, 0, 0);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        opacity: 0;
                        transition: all 0.3s ease;
                    } -->

                    .image-card:hover .image-overlay {
                        background: rgba(0, 0, 0, 0.2);
                        opacity: 1;
                    }

                    .image-overlay .icon {
                        background: rgba(255, 255, 255, 0.8);
                        border-radius: 50%;
                        padding: 8px;
                        width: 40px;
                        height: 40px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    <!-- .image-title {
                        padding: 0.75rem;
                    }

                    .image-title p {
                        font-size: 0.875rem;
                        font-weight: 500;
                        color: #111827;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    } -->

                    /* Carousel Indicators */
                    <!-- .carousel-indicators {
                        display: flex;
                        justify-content: center;
                        gap: 0.5rem;
                        margin-top: 1.5rem;
                    } -->

                    .indicator {
                        width: 12px;
                        height: 12px;
                        border-radius: 50%;
                        background: #d1d5db;
                        cursor: pointer;
                        transition: background 0.3s ease;
                    }

                    .indicator.active {
                        background: #3b82f6;
                    }

                    .indicator:hover {
                        background: #9ca3af;
                    }

                    /* Modal */
                    .modal {
                        position: fixed;
                        inset: 0;
                        background: rgba(0, 0, 0, 0.9);
                        z-index: 50;
                        display: none;
                        align-items: center;
                        justify-content: center;
                        padding: 1rem;
                    }

                    .modal.active {
                        display: flex;
                    }

                    .modal-content {
                        position: relative;
                        max-width: 80rem;
                        max-height: 100%;
                        width: 100%;
                    }

                    .modal-header {
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        z-index: 10;
                        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), transparent);
                        padding: 1rem;
                        color: white;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    }

                    .modal-title {
                        display: flex;
                        align-items: center;
                        gap: 1rem;
                    }

                    .modal-title h3 {
                        font-size: 1.125rem;
                        font-weight: 600;
                    }

                    .modal-title span {
                        font-size: 0.875rem;
                        color: #d1d5db;
                    }

                    .modal-actions {
                        display: flex;
                        align-items: center;
                        gap: 0.5rem;
                    }

                    .modal-btn {
                        padding: 0.5rem;
                        border-radius: 50%;
                        background: transparent;
                        border: none;
                        color: white;
                        cursor: pointer;
                        transition: background 0.3s ease;
                    }

                    .modal-btn:hover {
                        background: rgba(255, 255, 255, 0.2);
                    }

                    .modal-nav {
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        z-index: 10;
                        background: rgba(0, 0, 0, 0.5);
                        border: none;
                        border-radius: 50%;
                        width: 48px;
                        height: 48px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        color: white;
                        transition: background 0.3s ease;
                    }

                    .modal-nav:hover {
                        background: rgba(0, 0, 0, 0.7);
                    }

                    .modal-nav.prev {
                        left: 1rem;
                    }

                    .modal-nav.next {
                        right: 1rem;
                    }

                    .modal-image-container {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 100%;
                    }

                    .modal-image {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                        border-radius: 8px;
                    }

                    .modal-counter {
                        position: absolute;
                        bottom: 1rem;
                        left: 50%;
                        transform: translateX(-50%);
                        background: rgba(0, 0, 0, 0.5);
                        color: white;
                        padding: 0.5rem 1rem;
                        border-radius: 20px;
                        font-size: 0.875rem;
                    }

                    .modal-thumbnails {
                        position: absolute;
                        bottom: 1rem;
                        left: 1rem;
                        right: 1rem;
                        display: flex;
                        gap: 0.5rem;
                        overflow-x: auto;
                        padding-bottom: 0.5rem;
                    }

                    .modal-thumbnail {
                        flex: none;
                        width: 64px;
                        height: 48px;
                        border-radius: 4px;
                        overflow: hidden;
                        border: 2px solid transparent;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        opacity: 0.6;
                    }

                    .modal-thumbnail:hover {
                        opacity: 0.8;
                    }

                    .modal-thumbnail.active {
                        border-color: white;
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                        opacity: 1;
                    }

                    .modal-thumbnail img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }

                    /* Responsive Design */
                    @media (max-width: 640px) {
                        .image-item {
                            width: 100%;
                        }
                        
                        .carousel-container {
                            padding: 0 2rem;
                            margin-top: 5rem;
                        }
                    }

                    @media (min-width: 641px) and (max-width: 768px) {
                        .image-item {
                            width: 50%;
                        }
                    }

                    @media (min-width: 769px) and (max-width: 1024px) {
                        .image-item {
                            width: 33.333%;
                        }
                    }

                    @media (min-width: 1025px) and (max-width: 1280px) {
                        .image-item {
                            width: 25%;
                        }
                    }

                    @media (min-width: 1281px) {
                        .image-item {
                            width: 20%;
                        }
                    }

                    /* Hide scrollbar for thumbnails */
                    .modal-thumbnails::-webkit-scrollbar {
                        display: none;
                    }

                    .modal-thumbnails {
                        -ms-overflow-style: none;
                        scrollbar-width: none;
                    }
                </style>
              
                <main class="gallery-container">
                    <div class="carousel-container">
                        <button class="carousel-nav prev" id="prevBtn">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                        <button class="carousel-nav next" id="nextBtn">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                        <div class="images-container">
                            <div class="images-track" id="imagesTrack">
                                <!-- Images will be populated by JavaScript -->
                            </div>
                        </div>
                        <!-- <div class="carousel-indicators" id="indicators">
                            <!-- Indicators will be populated by JavaScript -->
                        <!-- </div> --> 
                    </div>
                </main>
                <div class="modal" id="modal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <div class="modal-title">
                                <h3>Ridhima Mehra</h3>
                                <span>Principal Medical Writer (CTI)</span>
                            </div>
                            <div class="modal-actions">
                                <button class="modal-btn">
                                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                                    </svg>
                                </button>
                                <button class="modal-btn">
                                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </button>
                                <button class="modal-btn">
                                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                    </svg>
                                </button>
                                <button class="modal-btn" id="closeModal">
                                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <button class="modal-nav prev" id="modalPrev">
                            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                        </button>
                        <button class="modal-nav next" id="modalNext">
                            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                        <div class="modal-image-container">
                            <img class="modal-image" id="modalImage" src="" alt="">
                        </div>
                        <div class="modal-counter" id="modalCounter">1 / 17</div>
                        <div class="modal-thumbnails" id="modalThumbnails">
                            <!-- Thumbnails will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
                <script>
                    // Sample images data
                    const images = [
                        {
                            id: 1,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/1-300x300.jpeg',
                            alt: 'Conference group photo',
                            <!-- title: 'Team Meeting' -->
                        },
                        {
                            id: 2,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/2-scaled.jpeg',
                            alt: 'Conference dining',
                            <!-- title: 'Business Lunch' -->
                        },
                        {
                            id: 3,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/3-300x300.jpeg',
                            alt: 'Presentation',
                            <!-- title: 'Presentation Session' -->
                        },
                        {
                            id: 4,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/4-300x300.jpeg',
                            alt: 'Team discussion',
                            <!-- title: 'Panel Discussion' -->
                        },
                        {
                            id: 5,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/5-300x300.jpeg',
                            alt: 'Speaker presenting',
                            <!-- title: 'Keynote Speech' -->
                        },
                        {
                            id: 6,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/6-300x300.jpeg',
                            alt: 'Networking event',
                            <!-- title: 'Networking Session' -->
                        },
                        {
                            id: 7,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/7-300x300.jpeg',
                            alt: 'Workshop',
                            <!-- title: 'Interactive Workshop' -->
                        },
                        {
                            id: 8,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/8-300x300.jpeg',
                            alt: 'Awards ceremony',
                            <!-- title: 'Awards Ceremony' -->
                        },
                        {
                            id: 9,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/9-300x300.jpeg',
                            alt: 'Coffee break',
                            <!-- title: 'Coffee Break' -->
                        },
                        {
                            id: 10,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/1-300x300.jpeg',
                            alt: 'Group discussion',
                            <!-- title: 'Breakout Session' -->
                        },
                        {
                            id: 11,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/2-scaled.jpeg',
                            alt: 'Exhibition',
                            <!-- title: 'Product Exhibition' -->
                        },
                        {
                            id: 12,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/3-300x300.jpeg',
                            alt: 'Closing ceremony',
                            <!-- title: 'Closing Ceremony' -->
                        },
                        {
                            id: 13,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/4-300x300.jpeg',
                            alt: 'Team building',
                            <!-- title: 'Team Building' -->
                        },
                        {
                            id: 14,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/6-300x300.jpeg',
                            alt: 'Innovation lab',
                            <!-- title: 'Innovation Lab' -->
                        },
                        {
                            id: 15,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/7-300x300.jpeg',
                            alt: 'Training session',
                            <!-- title: 'Training Session' -->
                        },
                        {
                            id: 16,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/8-300x300.jpeg',
                            alt: 'Q&A session',
                            <!-- title: 'Q&A Session' -->
                        },
                        {
                            id: 17,
                            src: '<?php echo get_template_directory_uri(); ?>/assets/images/9-300x300.jpeg',
                            alt: 'Farewell dinner',
                            <!-- title: 'Farewell Dinner' -->
                        }
                    ];

                    // Global variables
                    let currentCarouselIndex = 0;
                    let currentModalIndex = 0;
                    let visibleCount = getVisibleCount();

                    // Get visible count based on screen size
                    function getVisibleCount() {
                        const width = window.innerWidth;
                        if (width < 641) return 1;
                        if (width < 769) return 2;
                        if (width < 1025) return 3;
                        if (width < 1281) return 4;
                        return 5;
                    }

                    // Initialize gallery
                    function initGallery() {
                        populateImages();
                        populateIndicators();
                        populateModalThumbnails();
                        updateCarousel();
                        updateIndicators();
                    }

                    // Populate images
                    function populateImages() {
                        const imagesTrack = document.getElementById('imagesTrack');
                        imagesTrack.innerHTML = '';
                        
                        images.forEach((image, index) => {
                            const imageItem = document.createElement('div');
                            imageItem.className = 'image-item';
                            imageItem.innerHTML = `
                                <div class="image-card" onclick="openModal(${index})">
                                    <img src="${image.src}" alt="${image.alt}">
                                    <div class="image-overlay">
                                        <div class="icon">
                                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <!-- <div class="image-title">
                                        <p>${image.title}</p>
                                    </div> -->
                                </div>
                            `;
                            imagesTrack.appendChild(imageItem);
                        });
                    }

                    // Populate indicators
                    function populateIndicators() {
                        const indicators = document.getElementById('indicators');
                        indicators.innerHTML = '';
                        
                        const totalSlides = Math.ceil(images.length / visibleCount);
                        for (let i = 0; i < totalSlides; i++) {
                            const indicator = document.createElement('div');
                            indicator.className = 'indicator';
                            indicator.onclick = () => goToSlide(i);
                            indicators.appendChild(indicator);
                        }
                    }

                    // Populate modal thumbnails
                    function populateModalThumbnails() {
                        const thumbnails = document.getElementById('modalThumbnails');
                        thumbnails.innerHTML = '';
                        
                        images.forEach((image, index) => {
                            const thumbnail = document.createElement('div');
                            thumbnail.className = 'modal-thumbnail';
                            thumbnail.innerHTML = `<img src="${image.src}" alt="${image.alt}">`;
                            thumbnail.onclick = () => goToModalImage(index);
                            thumbnails.appendChild(thumbnail);
                        });
                    }

                    // Update carousel position
                    function updateCarousel() {
                        const imagesTrack = document.getElementById('imagesTrack');
                        const translateX = -(currentCarouselIndex * (100 / visibleCount));
                        imagesTrack.style.transform = `translateX(${translateX}%)`;
                        
                        // Update navigation buttons
                        const prevBtn = document.getElementById('prevBtn');
                        const nextBtn = document.getElementById('nextBtn');
                        
                        prevBtn.disabled = currentCarouselIndex === 0;
                        nextBtn.disabled = currentCarouselIndex >= images.length - visibleCount;
                    }

                    // Update indicators
                    function updateIndicators() {
                        const indicators = document.querySelectorAll('.indicator');
                        const activeIndex = Math.floor(currentCarouselIndex / visibleCount);
                        
                        indicators.forEach((indicator, index) => {
                            indicator.classList.toggle('active', index === activeIndex);
                        });
                    }

                    // Go to specific slide
                    function goToSlide(slideIndex) {
                        currentCarouselIndex = slideIndex * visibleCount;
                        updateCarousel();
                        updateIndicators();
                    }

                    // Carousel navigation
                    function nextSlide() {
                        if (currentCarouselIndex < images.length - visibleCount) {
                            currentCarouselIndex++;
                            updateCarousel();
                            updateIndicators();
                        }
                    }

                    function prevSlide() {
                        if (currentCarouselIndex > 0) {
                            currentCarouselIndex--;
                            updateCarousel();
                            updateIndicators();
                        }
                    }

                    // Modal functions
                    function openModal(index) {
                        currentModalIndex = index;
                        const modal = document.getElementById('modal');
                        const modalImage = document.getElementById('modalImage');
                        const modalCounter = document.getElementById('modalCounter');
                        
                        modal.classList.add('active');
                        modalImage.src = images[index].src;
                        modalImage.alt = images[index].alt;
                        modalCounter.textContent = `${index + 1} / ${images.length}`;
                        
                        updateModalThumbnails();
                    }

                    function closeModal() {
                        document.getElementById('modal').classList.remove('active');
                    }

                    function nextModalImage() {
                        currentModalIndex = (currentModalIndex + 1) % images.length;
                        updateModalImage();
                    }

                    function prevModalImage() {
                        currentModalIndex = (currentModalIndex - 1 + images.length) % images.length;
                        updateModalImage();
                    }

                    function goToModalImage(index) {
                        currentModalIndex = index;
                        updateModalImage();
                    }

                    function updateModalImage() {
                        const modalImage = document.getElementById('modalImage');
                        const modalCounter = document.getElementById('modalCounter');
                        
                        modalImage.src = images[currentModalIndex].src;
                        modalImage.alt = images[currentModalIndex].alt;
                        modalCounter.textContent = `${currentModalIndex + 1} / ${images.length}`;
                        
                        updateModalThumbnails();
                    }

                    function updateModalThumbnails() {
                        const thumbnails = document.querySelectorAll('.modal-thumbnail');
                        thumbnails.forEach((thumbnail, index) => {
                            thumbnail.classList.toggle('active', index === currentModalIndex);
                        });
                    }

                    // Event listeners
                    document.getElementById('prevBtn').addEventListener('click', prevSlide);
                    document.getElementById('nextBtn').addEventListener('click', nextSlide);
                    document.getElementById('closeModal').addEventListener('click', closeModal);
                    document.getElementById('modalPrev').addEventListener('click', prevModalImage);
                    document.getElementById('modalNext').addEventListener('click', nextModalImage);

                    // Close modal on background click
                    document.getElementById('modal').addEventListener('click', (e) => {
                        if (e.target.id === 'modal') {
                            closeModal();
                        }
                    });

                    // Handle window resize
                    window.addEventListener('resize', () => {
                        const newVisibleCount = getVisibleCount();
                        if (newVisibleCount !== visibleCount) {
                            visibleCount = newVisibleCount;
                            populateIndicators();
                            currentCarouselIndex = 0;
                            updateCarousel();
                            updateIndicators();
                        }
                    });

                    // Initialize when page loads
                    document.addEventListener('DOMContentLoaded', initGallery);
                </script>
                <!-- Event Gallery Section End -->

                <!-- Interested in working with us? -->
                <section class="interested">
                    <h2 class="interested">Interested in working with us?</h2>
                </section>

                <!-- Job Application Form Section -->
                <section class="job-application-form">
                    <div class="form-container">
                        <form id="career-application-form" class="application-form" method="post" enctype="multipart/form-data">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="applicant-name">Name <span class="required">*</span></label>
                                    <input type="text" id="applicant-name" name="applicant_name" required>
                                </div>
                                <div class="form-group">
                                    <label for="applicant-mobile">Mobile <span class="required">*</span></label>
                                    <div class="mobile-input-group">
                                        <select id="country-code" name="country_code" class="country-code">
                                            <option value="+91" data-flag="🇮🇳">+91</option>
                                            <option value="+1" data-flag="🇺🇸">+1</option>
                                            <option value="+44" data-flag="🇬🇧">+44</option>
                                            <option value="+61" data-flag="🇦🇺">+61</option>
                                            <option value="+49" data-flag="🇩🇪">+49</option>
                                        </select>
                                        <input type="tel" id="applicant-mobile" name="applicant_mobile" placeholder="9876543210" required>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="applicant-email">Email <span class="required">*</span></label>
                                    <input type="email" id="applicant-email" name="applicant_email" required>
                                </div>
                                <div class="form-group">
                                    <label for="current-location">Current Location <span class="required">*</span></label>
                                    <input type="text" id="current-location" name="current_location" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="current-salary">Current Annual Salary</label>
                                    <input type="text" id="current-salary" name="current_salary">
                                    <small class="form-note">In Indian Currency</small>
                                </div>
                                <div class="form-group">
                                    <label for="expected-salary">Expected Annual Salary</label>
                                    <input type="text" id="expected-salary" name="expected_salary">
                                    <small class="form-note">In Indian Currency</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="area-of-interest">Area of Interest</label>
                                    <textarea id="area-of-interest" name="area_of_interest" rows="4" maxlength="200"></textarea>
                                    <small class="form-note"><span id="interest-count">0</span> of 200 max words</small>
                                </div>
                                <div class="form-group">
                                    <label for="notice-period">Notice Period</label>
                                    <select id="notice-period" name="notice_period">
                                        <option value="">Select Notice Period</option>
                                        <option value="immediate">Immediate</option>
                                        <option value="15-days">15 Days</option>
                                        <option value="1-month">1 Month</option>
                                        <option value="2-months">2 Months</option>
                                        <option value="3-months">3 Months</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="applicant-message">Message <span class="required">*</span></label>
                                    <textarea id="applicant-message" name="applicant_message" rows="6" maxlength="200" required style="min-height: 120px; resize: vertical;"></textarea>
                                    <small class="form-note"><span id="message-count">0</span> of 200 max words</small>
                                </div>
                                <div class="form-group">
                                    <label for="cv-upload">Attach your CV <span class="required">*</span></label>
                                    <div class="file-upload-area" id="file-upload-area">
                                        <div class="upload-icon">📄</div>
                                        <p class="upload-text">Click or drag a file to this area to upload.</p>
                                        <small class="upload-note">Only pdf & docs files are accepted here</small>
                                        <input type="file" id="cv-upload" name="cv_file" accept=".pdf,.doc,.docx" required hidden>
                                    </div>
                                    <div class="file-info" id="file-info" style="display: none;">
                                        <span class="file-name"></span>
                                        <button type="button" class="remove-file" onclick="removeFile()">×</button>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group full-width">
                                    <button type="submit" class="submit-btn">Submit your interest</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </section>

                <!-- Lightbox Modal -->
                <div id="lightbox" class="lightbox-overlay">
                  <div class="lightbox-container">
                    <button class="lightbox-close" onclick="closeLightbox()">&times;</button>

                    <div class="lightbox-content">
                      <button class="lightbox-nav lightbox-prev" onclick="changeLightboxImage(-1)">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </button>

                      <div class="lightbox-image-container">
                        <img id="lightbox-image" src="" alt="">
                      </div>

                      <button class="lightbox-nav lightbox-next" onclick="changeLightboxImage(1)">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </button>
                    </div>

                    <div class="lightbox-counter">
                      <span id="lightbox-current">1</span> / <span id="lightbox-total">17</span>
                    </div>

                    <div class="lightbox-controls">
                      <button class="lightbox-btn" onclick="closeLightbox()">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- CTA Section -->
                <section class="careers-cta-section">
                    <div class="careers-cta-background">
                        <div class="careers-cta-content">
                            <p class="careers-cta-subtitle">INTERESTED IN LEARNING MORE?</p>
                            <h2 class="careers-cta-title">Get In Touch</h2>
                            <a href="<?php echo esc_url(home_url('/contact')); ?>" class="careers-cta-button">CONTACT US</a>
                        </div>
                    </div>
                </section>

            </div>
        </article>
    <?php endwhile; ?>
</main>

<style>

.mobile-values-section {
  display: none;
}

@media screen and (max-width: 767px) {
  .mobile-values-section {
    display: flex;
    flex-direction: column;
    padding: 20px;
  }

  .value-card {
    background: white;
    border: 2px solid #ff6b35;
    border-radius: 12px;
    padding: 40px 20px 30px;
    margin-bottom: 60px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
}


.icon-circle {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #8db4ff, #a8c5ff);
    border-radius: 50%;
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    z-index: 2;
}


  .icon {
    width: 32px;
    height: 32px;
    stroke: white;
    stroke-width: 2;
    fill: none;
}

.icon-text {
    position: absolute;
    bottom: 20px;
    color: white;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;
    text-align: center;
    width: 100%;
}


  .value-title {
    color: #ff6b35;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 15px;
    text-transform: capitalize;
  }

  .value-description {
    color: #666;
    font-size: 16px;
    line-height: 1.5;
    max-width: 280px;
    margin: 0 auto;
  }
}

/* Top section styling */
.top-section {
  margin: -40px 5px 0 5px;
  padding: 1rem 0;
  text-align: center;
  opacity: 0;
    transform: translateY(-120px);
    transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.top-section.animated {
    opacity: 1;
    transform: translateY(0);
}


.top-subtitle {
  color: #ff5f1f;
  font-size: 1.75rem;
  font-family: "Maven Pro", sans-serif;
  text-align: left;
  font-weight: 500;
  margin: 0.1rem 0 0 25px;
}

/* Mobile responsive style */
@media (max-width: 768px) {
  .top-subtitle {
    font-size: 1.5rem;
    align-items: center;
  }
}

/* Full-width careers hero section */
.careers-hero {
  background-color: #ffffff;
  margin: 0px 5px 0 5px;
  padding: 1rem 0;
}

.entry-title {
  color: #0070f3;
  font-size: 3.50rem;
  font-weight: 600;
  margin-top: -10px;
  margin: 0 0 0 10px;
  font-family: "Maven Pro", sans-serif;
}

.careers-hero__content {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
  align-items: flex-start;
  width: 100%;
  padding: 0 20px;
  min-height: 500px;
}

.careers-hero__text {
  flex: 1 1 400px;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 100%;
  opacity: 0;
  transform: translateY(120px);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.careers-hero__text.animated {
  opacity: 1;
  transform: translateY(0);
}

.careers-hero__highlight {
    color: #ff945f;
    font-size: 26px;
    font-weight: 500;
    margin: 5px 0 10px 10px;
    font-family: "georgia", sans-serif;
}

.careers-hero__paragraph {
  font-size: 20.002px;
  color: #525252;
  margin-bottom: 1rem;
  line-height: 1.5;
  font-family: "Georgia", sans-serif;
  margin: 0 0 0 10px;
}

.careers-hero__image {
  flex: 1 1 400px;
  min-width: 300px;
  text-align: center;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  height: 100%;
}

.careers-hero__image img {
  width: 100%;
  max-width: 100%;
  height: auto;
  margin: 0 10px 10px 5px;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  opacity: 0;
  transform: scale(0.85);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1), transform 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}
.careers-hero__image img.animated {
  opacity: 1;
  transform: scale(1);
}


@media (max-width: 768px) {
  .careers-hero__image {
    order: 1;
  }
  .careers-hero__text {
    order: 2;
    align-items: center;
  }

   .careers-hero {
    margin: 10px 5px 0 5px;
    padding: 2rem 0;
  }

  .careers-hero__content {
    flex-direction: column;
    gap: 2rem;
    padding: 0 10px;
    min-height: unset;
  }

  .careers-hero__text,
  .careers-hero__image {
    flex: 1 1 100%;
    min-width: 0;
    height: auto;
  }

  /*.careers-hero__image {
    order: 1;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .careers-hero__image img {
    width: 90vw;
    max-width: 320px;
    height: auto;
    margin: 0 auto 10px auto;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
    display: block;
  }*/

  .careers-hero__text {
    order: 2;
    padding: 0;
    margin: 0;
  }

  .entry-title {
    font-size: 2rem;
    margin: 0 0 0 5px;
  }

  .careers-hero__highlight {
    font-size: 20px;
    margin: 5px 0 10px 5px;
  }

  .careers-hero__paragraph {
    font-size: 16px;
    align-items: center;
  }
}

/* Company Values Section */
.company-values {
  margin: 2rem 5px 0 5px;
  padding: 2rem 0;
  text-align: center;
  opacity: 0;
  transform: translateY(120px);
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.company-values.animated {
  opacity: 1;
  transform: translateY(0);
}

.company-values__heading {
  color: #0072DA;
  font-size: 3.3rem;
  font-weight: 600;
  font-family: "Maven Pro", sans-serif;
  margin-bottom: 2rem;
  text-align: center;
  line-height: 1.3;
}

.value-arc-wrapper {
  position: relative;
  width: 100%;
  max-width: 900px;
  height: 500px;
  margin: 4rem auto;
}

.value-radio {
  display: none;
}

.value-label {
  position: absolute;
  width: 180px;
  height: 180px;
  background: #abc7fe;
  border-radius: 50%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: 500;
  box-shadow: 0 0 30px rgba(0, 113, 255, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 10px;
  line-height: 1.2;
  opacity: 0;
  transform: scale(0.5);
  animation: fadeInScale 0.6s ease forwards;
}

.value-icon {
  font-size: 24px;
  margin-bottom: 4px;
  display: block;
}

.value-text {
    font-size: 18px;
    font-weight: 400;
    text-align: center;
    line-height: 1.1;
}

.value-tooltip {
  position: absolute;
  top: 130px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 400;
  line-height: 1.4;
  width: 200px;
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.value-tooltip::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid rgba(0, 0, 0, 0.9);
}


#v1:checked + label { background: #ff7f3f; box-shadow: 0 0 30px rgba(255, 127, 63, 0.5); }
#v2:checked + label { background: #ff7f3f; box-shadow: 0 0 30px rgba(255, 127, 63, 0.5); }
#v3:checked + label { background: #ff7f3f; box-shadow: 0 0 30px rgba(255, 127, 63, 0.5); }
#v4:checked + label { background: #ff7f3f; box-shadow: 0 0 30px rgba(255, 127, 63, 0.5); }
#v5:checked + label { background: #ff7f3f; box-shadow: 0 0 30px rgba(255, 127, 63, 0.5); }

#v1:checked + label .value-tooltip { opacity: 1; visibility: visible; }
#v2:checked + label .value-tooltip { opacity: 1; visibility: visible; }
#v3:checked + label .value-tooltip { opacity: 1; visibility: visible; }
#v4:checked + label .value-tooltip { opacity: 1; visibility: visible; }
#v5:checked + label .value-tooltip { opacity: 1; visibility: visible; }

/* Position on arc with staggered animations - Outside semicircle layout */
label[for="v1"] {
    bottom: 32px;
    left: calc(36% - 370px);
    animation-delay: 0.1s;
}

label[for="v2"] {
    top: 30px;
    left: calc(50% - 360px);
    animation-delay: 0.2s;
}

label[for="v3"] {
  top: -80px;
  left: calc(50% - 80px);
  animation-delay: 0.3s;
}
label[for="v4"] {
    top: 32px;
    left: calc(61% + 85px);
    animation-delay: 0.4s;
}
label[for="v5"] {
    bottom: 25px;
    left: calc(61% + 220px);
    animation-delay: 0.5s;
}

.semicircle {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 540px;
  height: 350px;
  border-left: 6px solid #ff7f3f;
  border-right: 6px solid #ff7f3f;
  border-top: 6px solid #ff7f3f;
  border-radius: 350px 350px 0 0;
  opacity: 0;
  animation: drawArc 1.5s ease 0.6s forwards;
}

/* Keyframe Animations */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes drawArc {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
}

/* Semicircle Content Styling */
.semicircle-content {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  width: 400px;
  opacity: 0;
  animation: fadeInContent 1s ease 1.2s forwards;
}

.semicircle-heading {
  color: #ff5f1f;
  font-size: 2.25rem;
  font-weight: 600;
  font-family: "Maven Pro", sans-serif;
  margin: 0 0 10px 0;
}

.semicircle-paragraph {
  color: #525252;
  font-size: 20.002px;
  font-family: "Georgia", serif;
  line-height: 1.6;
  margin-top: 20px;
}

@keyframes fadeInContent {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@media (max-width: 768px) {
  .company-values {
    margin: 1rem 2px 0 2px;
    padding: 1.5rem 12px;
  }

  .company-values__heading {
    font-size: 1.75rem;
    text-align: center;
    margin-top: -50px;
    margin-bottom: 1.2rem;
    line-height: 1.2;
  }
    .value-arc-wrapper {
      display: none;
        height: 420px;
        max-width: 100%;
        display: none;
        margin: 2rem auto;
    }
}

@media (min-width: 769px) and (max-width: 1023px) {
  .value-arc-wrapper {
    display: none;
    height: 420px;
    max-width: 100%;
    margin: 2rem auto;
  }
  
  .mobile-values-section {
    display: none;
  }
}

/* Default: hidden on desktop */
.mobile-values-section {
  display: none;
}

/* Show only on screens smaller than 768px */
@media (max-width: 768px) {
  .mobile-values-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 20px;
    <!-- gap: 40px; -->
  }

  .value-card {
    border: 1px solid #f37124;
    padding: 80px 20px 30px;
    width: 90%;
    max-width: 400px;
    background: #fff;
    position: relative;
    text-align: center;
  }

  .icon-circle {
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: #bcd6ff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 20px rgba(0, 122, 255, 0.2);
  }

  .icon-circle img {
    width: 40px;
    height: 40px;
    margin-bottom: 5px;
  }

  .icon-label {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
  }

  .value-title {
    margin-top: 60px;
    font-size: 22px;
    color: #f37124;
    font-weight: bold;
  }

  .value-description {
    font-size: 15px;
    color: #333;
    margin-top: 10px;
    line-height: 1.5;
  }
}

@media (min-width: 767px) and (max-width: 1023px) {
  .mobile-values-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 20px;
    gap: 40px;
  }
  .icon{
    width: 50px;
    height: 50px;
  }
  .value-card {
    border: 1px solid #f37124;
    border-radius: 12px;
    padding: 80px 20px 30px;
    width: 100%;
    max-width: 500px;
    background: #fff;
    position: relative;
    text-align: center;
  }

  .icon-circle {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: #bcd6ff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 20px rgba(0, 122, 255, 0.2);
  }

  .icon-circle img {
    width: 50px;
    height: 50px;
    margin-bottom: 5px;
  }

  .icon-text{
    font-size: 12px;
    color:rgb(255, 255, 255);
    text-weight: 500;
  }

  .icon-label {
    color: #fff;
    font-size: 20px;
    font-weight: 500;
  }
  
  .value-title {
    margin-top: 60px;
    font-size: 22px;
    color: #f37124;
    font-weight: bold;
  }

  .value-description {
    font-size: 15px;
    color: #333;
    margin-top: 10px;
    line-height: 1.5;
  }
}

.positive-culture__heading {
  color: #0072DA;
  font-size: 3.5rem;
  font-weight: 600;
  text-align: center;
  margin-top: 5rem;
  margin-bottom: 3rem;
  font-family: "Maven Pro", sans-serif;
}

.culture-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.75rem;
  max-width: 100%;
  margin: 0 15px;
  padding: 0 5px;
}

.culture-box {
  background: white;
  padding: 2rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  text-align: left;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid #f0f0f0;
  height: 500px;
  display: flex;
  flex-direction: column;
}

.culture-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: rgba(74, 144, 226, 0.3);
  background-color: #0072DA;
  color: white;
}

.culture-box:hover .culture-title {
  color: white;
}

.culture-box:hover .culture-description {
  color: white;
}

.culture-box:hover .culture-icon {
  color: white;
}

.culture-box:hover .culture-icon svg path {
  fill: white !important;
}

.culture-icon {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 80px;
  height: 80px;
  color: #007aff;
  margin-bottom: 1.5rem;
  order: -1;
}

.culture-icon svg path {
  fill: #0072DA !important;
  transition: fill 0.3s ease;
}

.culture-title {
  color: #0072DA;
  font-size: 2rem;
  font-weight: 500;
  margin-bottom: 1rem;
  line-height: 1.6;
  font-family: "Maven Pro", sans-serif;
  text-transform: capitalize;
}

.culture-description {
  color: #525252;
  line-height: 1.6;
  font-size: 20px;
  font-family: Georgia, serif;
  flex-grow: 1;
  margin-bottom: 1.5rem;
}

@media (max-width: 480px) {
  .culture-box h3 {
    font-size: 1rem;
  }

  .culture-box p {
    font-size: 0.95rem;
  }
}

@media (min-width: 481px) and (max-width: 555px) {
  .culture-box h3 {
    font-size: 1.1rem;
    line-height: 1.3;
    word-wrap: break-word;
    white-space: normal;
  }

  .culture-box p {
    font-size: 1rem;
    line-height: 1.5;
  }
}



/* Responsive design for culture grid */
@media (max-width: 768px) {
  .culture-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 2px;
  }

  .positive-culture {
    margin: 10px 2px 40px 2px;
  }

  .positive-culture__heading {
    font-size: 2rem;
  }

  .culture-box {
    padding: 1.5rem;
    height: 400px;
  }
}

@media (max-width: 1024px) and (min-width: 769px) {
  .culture-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 0 2px;
  }

  .positive-culture {
    margin: 80px 2px 40px 2px;
  }

  .culture-box {
    height: 650px;
  }
}

@media (max-width: 1440px) and (min-width: 1025px) {
  .culture-grid {
    grid-template-columns: repeat(3, 1fr);
    padding: 0 2px;
  }

  .positive-culture {
    margin: 80px 2px 40px 2px;
  }

  .culture-box {
    height: 650px;
  }
}
/* Diversity and Inclusion Section */
.diversity-inclusion {
  margin: 40px 30px;
  background: white;
  padding: 4rem 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  cursor: pointer;
  opacity: 0;
  transform: translateY(120px);
  transition: opacity 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.diversity-inclusion.animated {
  opacity: 1;
  transform: translateY(0);
}

.diversity-inclusion:hover {
  background: #007aff;
}

.diversity-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.diversity-icon {
  color: #0072DA;
  margin-bottom: 2rem;
  transition: color 0.3s ease;
}

.diversity-icon svg path {
  fill: #0072DA !important;
  transition: fill 0.3s ease;
}

.diversity-inclusion:hover .diversity-icon {
  color: white;
}

.diversity-inclusion:hover .diversity-icon svg path {
  fill: white !important;
}

.diversity-title {
  color: #0072DA;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  font-family: "Maven Pro", sans-serif;
  letter-spacing: 2px;
  transition: color 0.3s ease;
}

.diversity-inclusion:hover .diversity-title {
  color: white;
}

.diversity-description {
  color: #525252;
  font-size: 20px;
  line-height: 1.6;
  font-family: Georgia, serif;
  margin-bottom: 1.5rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  transition: color 0.3s ease;
}

.diversity-inclusion:hover .diversity-description {
  color: white;
}

.business-code-link {
  color: #0072DA;
  text-decoration: underline;
  transition: color 0.3s ease;
}

.diversity-inclusion:hover .business-code-link {
  color: white;
}

.business-code-link:hover {
  text-decoration: none;
}

/* Responsive design for diversity section */
@media (max-width: 768px) {
  .diversity-inclusion {
    padding: 3rem 30px;
  }

  .diversity-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }

  .diversity-description {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .diversity-inclusion {
    padding: 2rem 30px;
  }

  .diversity-title {
    font-size: 1.8rem;
  }

  .diversity-description {
    font-size: 15px;
  }
}

/* Employee Testimonials Section */
.employee-testimonials {
  margin: 60px 10px 40px 10px;
  padding: 3rem 0;
}

.testimonials-heading {
  color: #0072DA;
  font-size: 3.50rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 1rem;
  font-family: "Maven Pro", sans-serif;
  opacity: 0;
  transform: translateY(-120px);
  transition: opacity 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.testimonials-heading.animated {
  opacity: 1;
  transform: translateY(0);
}

.testimonials-slider {
  position: relative;
  width: 100%;
  margin: 0 auto;
  <!-- overflow: hidden; -->
}

.testimonial-container {
  position: relative;
  width: 100%;
  height: 450px;
}

.testimonial-slide {
  position: absolute;
  top: 0;
  left: 0;
  margin-right: 10px;
  height: 100%;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.5s ease-in-out;
  background: white;
  border: 2px solid #0072DA;
  border-radius: 15px;
  padding: 3rem 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.testimonial-slide.active {
  opacity: 1;
  transform: translateX(0);
}

.testimonial-content {
  display: flex;
  align-items: center;
  gap: 2rem;
  height: 100%;
}

.testimonial-text {
  flex: 2;
  padding-right: 1rem;
}

.testimonial-text p {
  color: #525252;
  font-size: 20px;
  line-height: 1.6;
  font-family: Georgia, serif;
  font-style: italic;
  margin-bottom: 1.2rem;
}

.testimonial-author {
  text-align: right;
  margin-top: 2rem;
}

.author-name {
  display: block;
  color: #ff6b35;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: "Maven Pro", sans-serif;
  margin-bottom: 0.25rem;
}

.author-title {
  display: block;
  color: #ff6b35;
  font-size: 0.9rem;
  font-family: "Maven Pro", sans-serif;
}

.testimonial-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.testimonial-image img {
  width: 280px;
  height: 320px;
  border-radius: 15px;
  object-fit: cover;
}

.testimonial-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 107, 53, 0.9);
  color: white;
  border: none;
  width: 60px;
  height: 60px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  border-radius: 50%;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.testimonial-nav:hover {
  background: rgba(255, 107, 53, 1);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.testimonial-nav.prev {
  left: 10px;
}

.testimonial-nav.next {
  right: 10px;
}

@media (max-width: 768px) {
  .employee-testimonials {
    margin: 40px 2px 20px 2px;
    padding: 2rem 0;
  }

  .testimonials-heading {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .testimonial-container {
    height: auto;
    min-height: 900px;
    margin-bottom: 50px;
  }

  .testimonial-slide {
    padding: 1.5rem;
    height: auto;
    min-height: 900px;
  }

  .testimonial-content {
    flex-direction: column-reverse; /* Change from column to column-reverse */
    gap: 1.5rem;
    text-align: center;
  }

  .testimonial-text {
    padding-right: 0;
  }

  .testimonial-text p {
    font-size: 16px;
  }

  .testimonial-author {
    text-align: center;
    margin-top: 1rem;
  }

  .testimonial-image img {
    width: 200px;
    height: 200px;
  }

  .testimonial-nav {
    width: 50px;
    height: 50px;
  }

  .testimonial-nav.prev {
    left: 5px;
  }

  .testimonial-nav.next {
    right: 5px;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .careers-hero {
    margin: 10px 5px 0 5px;
    padding: 2rem 0;
  }

  .careers-hero__content {
    flex-direction: column;
    gap: 2rem;
    padding: 0 10px;
  }

  .careers-hero__text,
  .careers-hero__image {
    flex: 1 1 100%;
  }

  .entry-title {
    font-size: 2rem;
  }

  /* Testimonials responsive */
  .employee-testimonials {
    margin: 40px 2px 20px 2px;
    padding: 2rem 0;
  }

  .testimonials-heading {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .testimonial-container {
    height: auto;
    min-height: 500px;
  }

  .testimonial-slide {
    padding: 1.5rem;
    height: auto;
    min-height: 500px;
  }

  .testimonial-content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }

  .testimonial-text {
    padding-right: 0;
  }

  .testimonial-text p {
    font-size: 16px;
  }

  .testimonial-author {
    text-align: center;
    margin-top: 1rem;
  }

  .testimonial-image img {
    width: 200px;
    height: 200px;
  }

  .testimonial-nav {
    width: 50px;
    height: 50px;
  }

  .testimonial-nav.prev {
    left: 5px;
  }

  .testimonial-nav.next {
    right: 5px;
  }
}



.gallery-cta {
  background: white;
  padding: 2rem;
  text-align: center;
}

.gallery-cta h2 {
  color: #0072DA;
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0;
  font-family: "Maven Pro", sans-serif;
} -->

/* Lightbox Styles */
.lightbox-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lightbox-overlay.active {
  display: flex;
  opacity: 1;
  align-items: center;
  justify-content: center;
}

.lightbox-container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.lightbox-close {
  position: absolute;
  top: -50px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 40px;
  cursor: pointer;
  z-index: 10001;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.lightbox-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.lightbox-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.lightbox-image-container {
  max-width: 80vw;
  max-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lightbox-image-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.lightbox-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10001;
}

.lightbox-prev {
  left: -70px;
}

.lightbox-next {
  right: -70px;
}

.lightbox-nav:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.lightbox-counter {
  position: absolute;
  top: -50px;
  left: 0;
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.lightbox-controls {
  position: absolute;
  bottom: -60px;
  display: flex;
  gap: 15px;
  align-items: center;
}

.lightbox-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.lightbox-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}



@media (min-width: 769px) and (max-width: 850px) {
    .testimonial-slide {
        padding: 1.5rem;
        height: auto;
        min-height: 600px;
        margin-bottom: 4rem;
    }
}

/* Job Application Form Styles */
.job-application-form {
  margin: 40px 2px;
  padding: 40px 0;
  background: white;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2px;
}

/* intrested in working with us */
.interested {
    color: #0072DA;
    font-size: 3.50rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    font-family: "Maven Pro", sans-serif;
}

/* Mobile responsive style */
@media (max-width: 768px) {
    .interested {
        font-size: 2rem;
        padding: 0 1rem;
    }
}

.application-form {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-row {
  display: flex;
  gap: 30px;
  margin-bottom: 25px;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  flex: 1 1 100%;
}

.form-group label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  font-family: "Maven Pro", sans-serif;
}

.required {
  color: red;
  font-size: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 10px 14px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  font-family: "Maven Pro", sans-serif;
  transition: all 0.3s ease;
  background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #0072DA;
  box-shadow: 0 0 0 3px rgba(0, 114, 218, 0.1);
}

.mobile-input-group {
  display: flex;
  gap: 10px;
}

.country-code {
  flex: 0 0 100px;
  padding: 12px 8px;
}

.mobile-input-group input {
  flex: 1;
}

.form-note {
  font-size: 12px;
  color: #888888;
  margin-top: 4px;
  font-family: "Maven Pro", sans-serif;
}

.file-upload-area {
  border: 2px dashed #e1e5e9;
  border-radius: 8px;
  padding: 15px 10px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafbfc;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
}

.file-upload-area:hover {
  border-color: #0072DA;
  background: #f8f9ff;
}

.file-upload-area.dragover {
  border-color: #0072DA;
  background: #f0f4ff;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.upload-text {
  font-size: 13px;
  color: #333;
  margin-bottom: 4px;
  font-weight: 500;
  line-height: 1.2;
}

.upload-note {
  font-size: 11px;
  color: #666;
  line-height: 1.2;
}

.file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f0f4ff;
  border: 2px solid #0072DA;
  border-radius: 8px;
  margin-top: 10px;
}

.file-name {
  font-size: 14px;
  color: #0072DA;
  font-weight: 500;
}

.remove-file {
  background: #ff4757;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn {
  display: inline-block;
  
  width: 200px;
  height: 50px;
  text-align: Center;
  background: #066aab;
  color: white;
  border: none;
  border-radius: 2px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: "Maven Pro", sans-serif;
  text-transform: none;
}

.submit-btn:hover {
  background: #005bb5;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 114, 218, 0.3);
}

/* Form responsive design */
@media (max-width: 768px) {
  .job-application-form {
    margin: 20px 2px;
  }

  .form-container {
    padding: 0 2px;
  }

  .application-form {
    padding: 15px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .mobile-input-group {
    flex-direction: column;
  }

  .country-code {
    flex: 1;
  }

  .submit-btn {
    padding: 12px 24px;
    font-size: 16px;
    width: auto;
  }
}

/* CTA Section Styling */
.cta-section {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 4rem 1rem;
}

.careers-cta-section {
    width: 100%;
    display: flex !important
    justify-content: center;
    padding: 4rem 3rem;
    margin: 40px 10px;
    background: white;
    min-height: 200px;

.cta-background {
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;
    padding: 7rem 2rem;
    border-radius: 15px;
    background: linear-gradient(180deg, #39A1FF 0%, #0072DA 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    background-image:
        url('<?php echo get_template_directory_uri(); ?>/assets/images/Bg-Shape-Orange.png'),
        url('<?php echo get_template_directory_uri(); ?>/assets/images/Bg-Shape-blue.png');
    background-position:
        top right,
        bottom left;
    background-repeat: no-repeat, no-repeat;
    background-size: 300px auto, 250px auto;
}

.cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 600px;
    margin: 0 auto;
}

.cta-subtitle {
    font-size: 2rem;
    font-weight: 400;
    letter-spacing: 2px;
    margin-bottom: 1rem;
    opacity: 0.9;
    font-family: "Maven Pro", sans-serif;
    
}

.cta-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.2;
    font-family: "Maven Pro", sans-serif;
    
}

.cta-button {
    display: inline-block;
    background: white;
    color: #0072DA;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: "Maven Pro", sans-serif;
    letter-spacing: 1px;
}

.cta-button:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    color: #0072DA;
    text-decoration: none;
}

/* CTA Section Responsive */
@media (max-width: 768px) {
    .cta-section {
        padding: 2rem 0.5rem;
    }

    .cta-background {
        padding: 4rem 1.5rem;
        border-radius: 15px;
        background-size: 200px auto, 150px auto;
    }

    .cta-title {
        font-size: 2.7rem;
    }

    .cta-subtitle {
        font-size: 0.9rem;
    }

    .cta-button {
        padding: 0.875rem 2rem;
        font-size: 0.9rem;
    }
}

/* Careers CTA Section Styling */
.careers-cta-section {
    width: 100%;
    display: flex !important;
    justify-content: center;
    padding: 4rem 1rem;
    margin: 40px 30px;
    background: red; /* Temporary debug color */
    min-height: 200px; /* Ensure it has height */
}

.careers-cta-background {
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;
    padding: 7rem 2rem;
    border-radius: 20px;
    background: linear-gradient(180deg, #39A1FF 0%, #0072DA 100%) !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    min-height: 300px; /* Ensure it has height */
    opacity: 0;
  transform: translateY(-120px);
  transition: opacity 2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.careers-cta-background.animated {
  opacity: 1;
  transform: translateY(0);
}


.careers-cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white !important;
    max-width: 600px;
    margin: 0 auto;
    display: block !important;
}

.careers-cta-subtitle {
    font-size: 1.3rem !important;
    font-family: 'Maven Pro', sans-serif !important;
    font-weight: 500 !important;
    letter-spacing: 5px !important;
    text-transform: uppercase !important;
    margin-bottom: 1rem !important;
    opacity: 1 !important;
    color: white !important;
    display: block !important;
    opacity: 0;
  transform: translateY(-120px);
  transition: opacity 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.careers-cta-subtitle.animated {
  opacity: 1 !important;
  transform: translateY(0);
}

.careers-cta-title {
    font-size: 3rem !important;
    font-weight: 700 !important;
    margin-bottom: 2rem !important;
    font-family: 'Maven Pro', sans-serif !important;
    color: white !important;
    line-height: 1.2 !important;
    display: block !important;
}

.careers-cta-button {
  display: inline-block;
    background: white;
    color:rgb(71, 67, 67);
    padding: 1.5rem 2rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.3rem;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 20px 0072DA;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 10px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    opacity: 0;
  transform: translateY(180px);
  transition: opacity 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.careers-cta-button.animated {
  opacity: 1;
  transform: translateY(0);
}


.careers-cta-button:hover {
    background: #ffffff;
    color: #0072DA;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 136, 255, 0.4);
    text-decoration: none;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 20px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}

/* Responsive adjustments for careers CTA */
@media (max-width: 768px) {
    .careers-cta-section {
        padding: 2rem 0.5rem;
        margin: 40px 5px;
    }

    .careers-cta-background {
        padding: 4rem 1.5rem;
        border-radius: 15px;
        background-size: 200px auto, 150px auto;
    }

    .careers-cta-title {
        font-size: 2.5rem;
    }

    .careers-cta-subtitle {
        font-size: 0.9rem;
    }

    .careers-cta-button {
        padding: 0.875rem 2rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .careers-cta-background {
        padding: 3rem 1rem;
        border-radius: 12px;
    }

    .careers-cta-title {
        font-size: 2rem;
    }

    .careers-cta-subtitle {
        font-size: 0.8rem;
        letter-spacing: 1px;
    }

    .careers-cta-button {
        padding: 0.75rem 1.5rem;
        font-size: 0.85rem;
    }
}

/* Show only on mobile */
.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }

  .value-box {
    border: 1px solid #ffa76b;
    margin: 20px auto;
    padding: 20px 15px 30px;
    text-align: center;
    max-width: 90%;
    background-color: #fff;
    border-radius: 8px;
  }

  .value-box .icon-circle {
    background: #aac7ff;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin: -60px auto 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  }

  .value-box .icon-circle img {
    width: 40px;
    height: auto;
  }

  .value-box .icon-circle p {
    color: white;
    font-size: 14px;
    margin-top: 6px;
  }

  .value-box h3 {
    color: #ff5c00;
    font-size: 22px;
    margin: 15px 0 10px;
  }

  .value-box p.description {
    font-size: 16px;
    color: #333;
    line-height: 1.5;
  }
}
/* Default: hide mobile-only sections */
.mobile-only {
  display: none;
}

/* Show only on mobile devices */
@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .value-arc-wrapper {
    display: none;
  }

  .value-arc-center {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 380px;
    height: 200px;
    border-top: 5px solid #ff6a00;
    border-radius: 200px 200px 0 0;
    background: #fff;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;
  }

  .value-arc-center h3 {
    color: #ff6a00;
    font-size: 1.4rem;
    margin-bottom: 10px;
  }

  .value-arc-center p {
    font-size: 0.95rem;
    color: #333;
    max-width: 320px;
    line-height: 1.4;
  }

  .value-arc-item {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: rotate(var(--angle)) translateX(-180px) rotate(calc(-1 * var(--angle)));
    width: 100px;
    height: 100px;
    background-color: #a6c8ff;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    text-align: center;
    padding: 8px;
  }

  .value-arc-item span {
    font-size: 0.85rem;
    margin-top: 6px;
  }
}

.positive-culture__heading {
  opacity: 0;
  transform: translateY(120px);
  transition: opacity 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.positive-culture__heading.animated {
  opacity: 1;
  transform: translateY(0);
}

</style>

<script>
let currentSlideIndex = 0;
const slides = document.querySelectorAll('.testimonial-slide');
const totalSlides = slides.length;
let autoSlideInterval;

// Function to show a specific slide
function showSlide(index) {
    // Remove active class from all slides
    slides.forEach(slide => slide.classList.remove('active'));

    // Add active class to current slide
    slides[index].classList.add('active');

    currentSlideIndex = index;
}

// Function to go to next slide
function nextSlide() {
    const nextIndex = (currentSlideIndex + 1) % totalSlides;
    showSlide(nextIndex);
}

// Function to go to previous slide
function prevSlide() {
    const prevIndex = (currentSlideIndex - 1 + totalSlides) % totalSlides;
    showSlide(prevIndex);
}

// Function for manual navigation
function changeSlide(direction) {
    clearInterval(autoSlideInterval);
    if (direction === 1) {
        nextSlide();
    } else {
        prevSlide();
    }
    startAutoSlide();
}

// Function to start auto-sliding
function startAutoSlide() {
    autoSlideInterval = setInterval(nextSlide, 3000); // Change slide every 3 seconds
}

// Initialize the slider when page loads
document.addEventListener('DOMContentLoaded', function() {
    showSlide(0); // Show first slide
    startAutoSlide(); // Start auto-sliding

    // Pause auto-slide on hover
    const slider = document.querySelector('.testimonials-slider');
    if (slider) {
        slider.addEventListener('mouseenter', () => {
            clearInterval(autoSlideInterval);
        });

        slider.addEventListener('mouseleave', () => {
            startAutoSlide();
        });
    }
});

// Gallery Horizontal Scroll JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        const galleryContainer = document.querySelector('.culture-gallery-wrapper');
        if (!galleryContainer) return;
    
        const galleryTrack = galleryContainer.querySelector('.gallery-track');
        const prevButton = galleryContainer.querySelector('.prev');
        const nextButton = galleryContainer.querySelector('.next');
        const galleryItems = galleryContainer.querySelectorAll('.gallery-item');
    
        if (!galleryTrack || !prevButton || !nextButton || galleryItems.length === 0) {
            return;
        }
    
        let currentIndex = 0;
        const totalItems = galleryItems.length;
        let itemWidth = galleryItems[0].offsetWidth + parseInt(window.getComputedStyle(galleryItems[0]).marginRight, 10);
        let autoScrollInterval;
    
        function updateGalleryPosition() {
            const newTransform = -currentIndex * itemWidth;
            galleryTrack.style.transform = `translateX(${newTransform}px)`;
        }
    
        function slide(direction) {
            currentIndex += direction;
    
            // Loop the gallery
            if (currentIndex < 0) {
                currentIndex = totalItems - 1;
            } else if (currentIndex >= totalItems) {
                currentIndex = 0;
            }
    
            updateGalleryPosition();
        }
    
        function startAutoScroll() {
            stopAutoScroll(); // Clear any existing interval
            autoScrollInterval = setInterval(() => {
                slide(1); // Move to the next slide
            }, 3000); // Change slide every 3 seconds
        }
    
        function stopAutoScroll() {
            clearInterval(autoScrollInterval);
        }
    
        // Event listeners for buttons and mouse hover
        prevButton.addEventListener('click', () => {
            stopAutoScroll();
            slide(-1);
            startAutoScroll();
        });
    
        nextButton.addEventListener('click', () => {
            stopAutoScroll();
            slide(1);
            startAutoScroll();
        });
    
        galleryContainer.addEventListener('mouseenter', stopAutoScroll);
        galleryContainer.addEventListener('mouseleave', startAutoScroll);
    
        // Recalculate width on window resize
        window.addEventListener('resize', () => {
            itemWidth = galleryItems[0].offsetWidth + parseInt(window.getComputedStyle(galleryItems[0]).marginRight, 10);
            updateGalleryPosition();
        });
    
        startAutoScroll();
    });

// Gallery images array for lightbox
const galleryImages = [
  '<?php echo get_template_directory_uri(); ?>/assets/images/2-scaled.jpeg',
  '<?php echo get_template_directory_uri(); ?>/assets/images/3-300x300.jpeg',
  '<?php echo get_template_directory_uri(); ?>/assets/images/4-300x300.jpeg',
  '<?php echo get_template_directory_uri(); ?>/assets/images/5-300x300.jpeg',
  '<?php echo get_template_directory_uri(); ?>/assets/images/6-300x300.jpeg',
  '<?php echo get_template_directory_uri(); ?>/assets/images/8-300x300.jpeg',
  '<?php echo get_template_directory_uri(); ?>/assets/images/9-300x300.jpeg',
];

let currentLightboxIndex = 0;

// Lightbox functionality
function openLightbox(index) {
  currentLightboxIndex = index;
  const lightbox = document.getElementById('lightbox');
  const lightboxImage = document.getElementById('lightbox-image');
  const currentCounter = document.getElementById('lightbox-current');

  lightboxImage.src = galleryImages[index];
  currentCounter.textContent = index + 1;

  lightbox.style.display = 'flex';
  setTimeout(() => {
    lightbox.classList.add('active');
  }, 10);

  // Prevent body scroll
  document.body.style.overflow = 'hidden';
}

function closeLightbox() {
  const lightbox = document.getElementById('lightbox');
  lightbox.classList.remove('active');

  setTimeout(() => {
    lightbox.style.display = 'none';
    document.body.style.overflow = 'auto';
  }, 300);
}

function changeLightboxImage(direction) {
  currentLightboxIndex += direction;

  if (currentLightboxIndex < 0) {
    currentLightboxIndex = galleryImages.length - 1;
  } else if (currentLightboxIndex >= galleryImages.length) {
    currentLightboxIndex = 0;
  }

  const lightboxImage = document.getElementById('lightbox-image');
  const currentCounter = document.getElementById('lightbox-current');

  lightboxImage.src = galleryImages[currentLightboxIndex];
  currentCounter.textContent = currentLightboxIndex + 1;
}

// Keyboard navigation for lightbox
document.addEventListener('keydown', function(e) {
  const lightbox = document.getElementById('lightbox');
  if (lightbox && lightbox.classList.contains('active')) {
    if (e.key === 'Escape') {
      closeLightbox();
    } else if (e.key === 'ArrowLeft') {
      changeLightboxImage(-1);
    } else if (e.key === 'ArrowRight') {
      changeLightboxImage(1);
    }
  }
});

// Close lightbox when clicking outside the image
document.addEventListener('DOMContentLoaded', function() {
  const lightbox = document.getElementById('lightbox');
  if (lightbox) {
    lightbox.addEventListener('click', function(e) {
      if (e.target === this) {
        closeLightbox();
      }
    });
  }
});

// Form functionality
document.addEventListener('DOMContentLoaded', function() {
  // Character counters for textareas
  const areaOfInterest = document.getElementById('area-of-interest');
  const applicantMessage = document.getElementById('applicant-message');
  const interestCount = document.getElementById('interest-count');
  const messageCount = document.getElementById('message-count');

  if (areaOfInterest && interestCount) {
    areaOfInterest.addEventListener('input', function() {
      const wordCount = this.value.trim().split(/\s+/).filter(word => word.length > 0).length;
      interestCount.textContent = wordCount;

      if (wordCount > 200) {
        this.style.borderColor = '#ff4757';
        interestCount.style.color = '#ff4757';
      } else {
        this.style.borderColor = '#e1e5e9';
        interestCount.style.color = '#888888';
      }
    });
  }

  if (applicantMessage && messageCount) {
    applicantMessage.addEventListener('input', function() {
      const wordCount = this.value.trim().split(/\s+/).filter(word => word.length > 0).length;
      messageCount.textContent = wordCount;

      if (wordCount > 200) {
        this.style.borderColor = '#ff4757';
        messageCount.style.color = '#ff4757';
      } else {
        this.style.borderColor = '#e1e5e9';
        messageCount.style.color = '#888888';
      }
    });
  }

  // File upload functionality
  const fileUploadArea = document.getElementById('file-upload-area');
  const fileInput = document.getElementById('cv-upload');
  const fileInfo = document.getElementById('file-info');

  if (fileUploadArea && fileInput) {
    // Click to upload
    fileUploadArea.addEventListener('click', function() {
      fileInput.click();
    });

    // File input change
    fileInput.addEventListener('change', function() {
      handleFileSelect(this.files[0]);
    });

    // Drag and drop functionality
    fileUploadArea.addEventListener('dragover', function(e) {
      e.preventDefault();
      this.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
      e.preventDefault();
      this.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
      e.preventDefault();
      this.classList.remove('dragover');
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    });
  }

  function handleFileSelect(file) {
    if (file) {
      // Check file type
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!allowedTypes.includes(file.type)) {
        alert('Please select a PDF or Word document.');
        return;
      }

      // Check file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size should be less than 5MB.');
        return;
      }

      // Update UI
      fileUploadArea.style.display = 'none';
      fileInfo.style.display = 'flex';
      fileInfo.querySelector('.file-name').textContent = file.name;

      // Set the file to the input
      const dt = new DataTransfer();
      dt.items.add(file);
      fileInput.files = dt.files;
    }
  }
});

// Remove file function
function removeFile() {
  const fileUploadArea = document.getElementById('file-upload-area');
  const fileInput = document.getElementById('cv-upload');
  const fileInfo = document.getElementById('file-info');

  fileUploadArea.style.display = 'block';
  fileInfo.style.display = 'none';
  fileInput.value = '';
}

// Form submission
document.getElementById('career-application-form').addEventListener('submit', function(e) {
  e.preventDefault();

  // Basic validation
  const requiredFields = this.querySelectorAll('[required]');
  let isValid = true;

  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      field.style.borderColor = '#ff4757';
      isValid = false;
    } else {
      field.style.borderColor = '#e1e5e9';
    }
  });

  if (!isValid) {
    alert('Please fill in all required fields.');
    return;
  }

  // Show success message (you can replace this with actual form submission)
  alert('Thank you for your interest! Your application has been submitted successfully. We will get back to you soon.');

  // Reset form
  this.reset();
  removeFile();

  // Reset character counters
  const interestCount = document.getElementById('interest-count');
  const messageCount = document.getElementById('message-count');
  if (interestCount) interestCount.textContent = '0';
  if (messageCount) messageCount.textContent = '0';
});

document.querySelectorAll('.value-label').forEach(function(label) {
  label.addEventListener('click', function() {
    var heading = this.getAttribute('data-heading');
    var desc = this.getAttribute('data-description');
    document.querySelector('.semicircle-heading').textContent = heading;
    document.querySelector('.semicircle-paragraph').textContent = desc;
  });
});

document.addEventListener('DOMContentLoaded', function () {
  function animateTopSectionOnScroll() {
    var section = document.querySelector('.top-section');
    if (!section) return;
    var windowHeight = window.innerHeight;
    var position = section.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      section.classList.add('animated');
    }
  }
  window.addEventListener('scroll', animateTopSectionOnScroll);
  animateTopSectionOnScroll(); // Trigger on load
});

function animateOnScroll(selector, offset = 100) {
  var elements = document.querySelectorAll(selector);
  var windowHeight = window.innerHeight;
  elements.forEach(function(el) {
    var position = el.getBoundingClientRect().top;
    if (position < windowHeight - offset) {
      el.classList.add('animated');
    }
  });
}

// List of selectors to animate
var animatedSelectors = [
  '.top-section',
  '.careers-hero__text',
  '.careers-hero__image img',
  '.company-values',
  '.positive-culture__heading',
  '.culture-grid',
  '.diversity-inclusion',
  '.testimonials-heading',
  '.careers-cta-background',
  '.careers-cta-subtitle',
  '.careers-cta-button'
];

// On scroll and on load, animate all
function triggerAllAnimations() {
  animatedSelectors.forEach(function(selector) {
    animateOnScroll(selector);
  });
}
window.addEventListener('scroll', triggerAllAnimations);
document.addEventListener('DOMContentLoaded', triggerAllAnimations);


</script>

<?php get_footer();?>