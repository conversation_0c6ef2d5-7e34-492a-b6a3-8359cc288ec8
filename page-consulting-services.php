<?php
/**
 * Template Name: Consulting Services Page
 * Description: Custom Consulting Services page template for Krystelis_Custom Theme
 *
 * @package Krystelis_Custom
 */
get_header();
?>

<div class="consulting-top-section">
  <div class="consulting-top-heading">Making clinical research crystal clear</div>
  <div class="consulting-cards-row">
    <div class="consulting-card consulting-card-tech">
      <div class="consulting-card-icon">
        <!-- Gear Icon -->
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="24" cy="24" r="22" stroke="#FF6A18" stroke-width="4" fill="#F5F5F5"/><path d="M24 16V32M16 24H32" stroke="#FF6A18" stroke-width="3" stroke-linecap="round"/></svg>
      </div>
      <div class="consulting-card-label">Technology</div>
    </div>
    <div class="consulting-card consulting-card-consulting consulting-card-active">
      <div class="consulting-card-icon">
        <!-- Consulting Icon -->
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="8" y="8" width="32" height="32" rx="4" fill="#F5F5F5"/><path d="M16 32L32 16" stroke="#FF6A18" stroke-width="3" stroke-linecap="round"/><path d="M16 16L32 32" stroke="#FF6A18" stroke-width="3" stroke-linecap="round"/></svg>
      </div>
      <div class="consulting-card-label">Consulting</div>
    </div>
    <div class="consulting-card consulting-card-admin">
      <div class="consulting-card-icon">
        <!-- Admin Icon -->
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="24" cy="18" r="8" fill="#F5F5F5" stroke="#FF6A18" stroke-width="3"/><rect x="10" y="30" width="28" height="10" rx="5" fill="#F5F5F5" stroke="#FF6A18" stroke-width="3"/></svg>
      </div>
      <div class="consulting-card-label">Administrative Services</div>
    </div>
  </div>
</div>

<div class="consulting-main-section">
  <div class="consulting-main-content">
    <div class="consulting-main-left">
      <h1 class="consulting-main-title">Consulting services</h1>
      <p class="consulting-main-text">
        The challenges that life sciences organisations face are complex and require careful management of interdependencies. Solving these also requires collaboration across an organisation's functions and departments so that synergies can be leveraged. For example, patient engagement initiatives can give clear insights into patient communication activities downstream. We also understand that the challenges and solutions may differ between larger pharmaceutical companies and smaller ones. Our experts are experienced in working with different operating models and can provide the most appropriate and relevant approach for each customer.
      </p>
      <p class="consulting-main-text">
        Krystelis' operational services are based on a comprehensive understanding of current and upcoming regulatory requirements across all regions in which our customers operate. Several of our team members are recognised as global subject matter experts in their fields, particularly in clinical research transparency and medical writing. They contribute to the shaping of future regulations by producing thought papers, participating in industry forums, and commenting on proposed guidance from health authorities.
      </p>
    </div>
    <div class="consulting-main-right">
      <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Consulting-Services-Image.png" alt="Consulting team" class="consulting-main-img" />
    </div>
  </div>
</div>

<div class="consulting-services-section">
  <p class="consulting-services-intro">
    Krystelis is ideally positioned to provide regulatory consulting services in our areas of expertise, either alongside our operational services or as a standalone engagement.
  </p>
  <h2 class="consulting-services-title">These services could include:</h2>
  <div class="consulting-services-cards">
    <div class="consulting-service-card">
    <img class="consulting-service-icon" src="https://krystelis.com/wp-content/uploads/2022/06/Rectangle-Bullet.svg" alt="" />

      <span>Assessing how upcoming regulatory changes could impact business processes, for example, the introduction of EU–CTR 536/2014</span>
    </div>
    <div class="consulting-service-card">
    <img class="consulting-service-icon" src="https://krystelis.com/wp-content/uploads/2022/06/Rectangle-Bullet.svg" alt="" />

      <span>How to adapt processes in the most efficient way to ensure compliance with regulations</span>
    </div>
    <div class="consulting-service-card">
    <img class="consulting-service-icon" src="https://krystelis.com/wp-content/uploads/2022/06/Rectangle-Bullet.svg" alt="" />

      <span>Process design, including writing SOPs</span>
    </div>
    <div class="consulting-service-card">
    <img class="consulting-service-icon" src="https://krystelis.com/wp-content/uploads/2022/06/Rectangle-Bullet.svg" alt="" />

      <span>Implementation plans and change management including training on new regulations and associated processes</span>
    </div>
  </div>
</div>

<style>
.consulting-top-section {
  width: 100vw;
  max-width: 100%;
  margin: 0 auto 32px auto;
  padding: 0 0 24px 0;
  background: #fff;
}
.consulting-top-heading {
  color: #FF6A18;
  font-family: "Maven Pro", sans-serif;
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0 0 12px 20px;
  padding-left: 16px;
  animation: revealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both;

}
@keyframes revealDown {
  0% {
    opacity: 0;
    transform: translateY(-40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.consulting-cards-row {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  background: #F1F1F1;
  border-radius: 4px;
  padding: 15px 0;
  gap: 0;
}
.consulting-card {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 12px 0;
  transition: background 0.2s, box-shadow 0.2s;
}
.consulting-card-icon {
  margin-bottom: 10px;
}
.consulting-card-label {
  color: #FF6A18;
  font-family: "Maven Pro", sans-serif;
  font-size: 1.25rem;
  font-weight: 500;
  letter-spacing: 1px;
  margin-top: 4px;
}
.consulting-main-section {
  width: 100vw;
  max-width: 100%;
  background: #fff;
  padding: 0 0 40px 0;
  margin: 0 auto;
}
.consulting-main-content {
  display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 500px;
    max-width: 100%;
    margin: 0;
    padding: 0;
    gap: 0;
}
.consulting-main-left {
  /* flex: 1 1 0; */
  min-width: 260px;
  margin-left: 30px;
  position: relative;
  opacity: 0;
  transform: translateY(40px);
  animation: revealUp 1s cubic-bezier(0.23, 1, 0.32, 1) 0.3s both;
}
@keyframes revealUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.consulting-main-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 52px;
    font-weight: 600;
    color: #0072DA;
    margin: 30px 0 30px 0;
    line-height: 1.2;
  }
.consulting-main-text {
  font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    line-height: 1.5;
    margin: 0 0 20px 0;
}
.consulting-main-right {
  flex: 1 1 0;
  /* display: flex; */
  align-items: flex-start;
  margin-left: 70px;
}
.consulting-main-img {
  width: 100%;
  max-width: 650px;
  border-radius: 18px;
  object-fit: cover;
  box-shadow: 0 4px 24px rgba(0,0,0,0.07);
  opacity: 0;
  transform: scale(0.85);
  animation: zoomInImg 1s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both;
}

@keyframes zoomInImg {
  0% {
    opacity: 0;
    transform: scale(0.85);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive styles */
@media (max-width: 900px) {
  .consulting-cards-row {
    flex-direction: column;
    align-items: stretch;
    padding: 0;
    gap: 0;
  }
  .consulting-card {
    border-bottom: 1px solid #eee;
    border-radius: 0;
    padding: 18px 0;
  }
  .consulting-card:last-child {
    border-bottom: none;
  }
  .consulting-main-content {
    flex-direction: column;
    gap: 24px;
    padding: 24px 8px 0 8px;
  }
  .consulting-main-right {
    justify-content: center;
  }
  .consulting-main-img {
    max-width: 95vw;
  }
}
@media (max-width: 600px) {
  .consulting-top-heading {
    font-size: 1.2rem;
    padding-left: 8px;
  }
  .consulting-cards-row {
    margin: 0 2px;
    padding: 0;
  }
  .consulting-main-title {
    font-size: 2rem;
  }
  .consulting-main-text {
    font-size: 1rem;
  }
  .consulting-main-img {
    border-radius: 10px;
  }
}

.consulting-services-section {
  max-width: 100vw;
  margin: 60px auto 0 auto;
  background: transparent;
  border-radius: 18px;
  padding: 0 0 40px 0;
  text-align: center;
}
.consulting-services-intro {
  font-family: Georgia, serif;
  font-size: 1.5rem;
  color: #525252;
  margin: auto 60px 24px 60px;
}
.consulting-services-title {
  color: #0072DA;
  font-family: "Maven Pro", sans-serif;
  font-size: 2.25rem;
  font-weight: 600;
  margin-bottom: 32px;
}
.consulting-services-cards {
  display: flex;
  flex-direction: column;
  gap: 32px;
  align-items: center;
  margin: 0 auto;
  max-width: 95vw;
}
.consulting-service-card {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 8px 40px rgba(0,0,0,0.07);
  padding: 36px 48px;
  font-size: 1.35rem;
  color: #525252;
  font-family: Georgia, serif;
  width: 95%;
  max-width: 1600px;
  text-align: left;
  margin: 0 auto;
  transition: box-shadow 0.2s;
}

.consulting-service-card {
  opacity: 0;
  transform: translateY(60px); /* Start 60px lower */
  transition: opacity 0.7s cubic-bezier(0.23, 1, 0.32, 1), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}

.consulting-service-card.in-view {
  opacity: 1;
  transform: translateY(0);
}
.consulting-service-icon {
  width: 18px;
  height: 18px;
  margin-right: 18px;
  flex-shrink: 0;
  display: inline-block;
  vertical-align: middle;
}
@media (max-width: 900px) {
  .consulting-services-title {
    font-size: 2rem;
  }
  .consulting-service-card {
    font-size: 1.2rem;
    padding: 18px 10px;
  }
}
@media (max-width: 600px) {
  .consulting-services-title {
    font-size: 1.2rem;
  }
  .consulting-service-card {
    font-size: 1rem;
    padding: 12px 4px;
  }
}

@media (max-width: 900px) {
  .consulting-mobile-stack {
    display: flex;
    flex-direction: column;
  }
  .consulting-card-label {
    order: 1;
    align-self: flex-start;
    margin: 16px 0 0 16px;
    font-size: 1.5rem;
    color: #FF6A18;
    font-family: "Maven Pro", sans-serif;
    font-weight: 600;
  }
  .consulting-top-section {
    order: 2;
  }
  .consulting-main-img {
    order: 3;
    margin: 24px auto 0 auto;
    display: block;
    max-width: 95vw;
    border-radius: 10px;
  }
  .consulting-main-title {
    order: 4;
    font-size: 2rem;
    margin: 24px 20px 12px 20px;
    text-align: left;
  }
  .consulting-main-text {
    order: 5;
    font-size: 1rem;
    margin: 24px 20px 12px 20px;
    text-align: left;
  }
  .consulting-main-content {
    display: flex;
    flex-direction: column;
  }
  .consulting-main-left, .consulting-main-right {
    width: 100%;
    margin: 0;
    padding: 0;
  }

  .consulting-services-intro {
    font-family: Georgia, serif;
    font-size: 1rem;
    color: #525252;
    margin: auto 40px 40px 40px;
}
}

@media (max-width: 900px) {
  .consulting-top-section {
    width: 100vw;
    max-width: 100%;
    margin: 0 auto 0 auto;
    padding: 0 0 24px 0;
    background: #fff;
    border-radius: 0;
    box-shadow: none;
  }
  .consulting-top-heading {
    color: #FF6A18;
    font-family: "Maven Pro", sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
    padding: 18px 0 18px 0;
    text-align: center;
  }
  .consulting-cards-row {
    flex-direction: column;
    align-items: center;
    background: #F1F1F1;
    border-radius: 0;
    padding: 32px 0 32px 0;
    gap: 0;
  }
  .consulting-card {
    width: 100%;
    background: transparent;
    border-radius: 0;
    box-shadow: none;
    display: flex;
    flex-wrap:wrap;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    align-content: center;
    padding: 32px 0;
    margin: 0;
    border-bottom: none;
  }
  .consulting-card-icon {
    margin-bottom: 18px;
    margin-top: 0;
    width: 56px !important;
    height: 56px !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .consulting-card-label {
    color: #FF6A18;
    font-family: "Maven Pro", sans-serif;
    font-size: 2rem;
    font-weight: 600;
    letter-spacing: 1px;
    margin-top: 0;
    margin-bottom: 0;
    text-align: center;
  }
}
@media (max-width: 600px) {
  .consulting-card-label {
    font-size: 1.3rem;
  }
  .consulting-card-icon {
    width: 40px !important;
    height: 40px !important;
  }
}

</style>


<?php get_template_part('template-parts/cta-section'); ?>

<!-- Responsive and section styles are inherited from the main theme -->
<script>
document.addEventListener('DOMContentLoaded', function () {
  function reorderConsultingSections() {
    const topSection = document.querySelector('.consulting-top-section');
    const mainSection = document.querySelector('.consulting-main-section');
    const mainImg = document.querySelector('.consulting-main-img');
    const mainTitle = document.querySelector('.consulting-main-title');
    const mainTexts = document.querySelectorAll('.consulting-main-text');

    if (window.innerWidth <= 900 && topSection && mainSection) {
      // Clear main section
      while (mainSection.firstChild) {
        mainSection.removeChild(mainSection.firstChild);
      }

      // Append reordered content
      if (topSection) mainSection.appendChild(topSection);
      if (mainTitle) mainSection.appendChild(mainTitle);
      if (mainImg) mainSection.appendChild(mainImg);
      mainTexts.forEach((el) => mainSection.appendChild(el));
    }
  }

  reorderConsultingSections();
  window.addEventListener('resize', reorderConsultingSections);
});

document.addEventListener('DOMContentLoaded', function() {
  var cards = document.querySelectorAll('.consulting-service-card');

  function revealCardsOnScroll() {
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;
    cards.forEach(function(card, i) {
      var rect = card.getBoundingClientRect();
      if (rect.top < windowHeight - 80) { // 80px before fully in view
        setTimeout(function() {
          card.classList.add('in-view');
        }, i * 120); // stagger each card by 120ms
      }
    });
  }

  // Optimized scroll handler
  let isScrolling = false;

  function handleScrollAnimations() {
    if (!isScrolling) {
      requestAnimationFrame(() => {
        revealCardsOnScroll();
        isScrolling = false;
      });
    }
    isScrolling = true;
  }

  window.addEventListener('scroll', handleScrollAnimations, { passive: true });
  // Also check on load in case already in view
  revealCardsOnScroll();
});

</script>

<?php get_footer(); ?> 