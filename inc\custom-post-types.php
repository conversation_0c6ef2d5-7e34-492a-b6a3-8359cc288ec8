<?php
/**
 * Custom Post Types for Krystelis Theme
 *
 * @package Krystelis_Custom
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register Custom Post Types
 */
function krystelis_register_cpts() {
    
    // Services CPT
    register_post_type('service', array(
        'labels' => array(
            'name'               => __('Services', 'krystelis-custom'),
            'singular_name'      => __('Service', 'krystelis-custom'),
            'menu_name'          => __('Services', 'krystelis-custom'),
            'add_new'            => __('Add New Service', 'krystelis-custom'),
            'add_new_item'       => __('Add New Service', 'krystelis-custom'),
            'edit_item'          => __('Edit Service', 'krystelis-custom'),
            'new_item'           => __('New Service', 'krystelis-custom'),
            'view_item'          => __('View Service', 'krystelis-custom'),
            'search_items'       => __('Search Services', 'krystelis-custom'),
            'not_found'          => __('No services found', 'krystelis-custom'),
            'not_found_in_trash' => __('No services found in trash', 'krystelis-custom'),
        ),
        'public'              => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'query_var'           => true,
        'rewrite'             => array('slug' => 'services'),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => 20,
        'menu_icon'           => 'dashicons-admin-tools',
        'supports'            => array('title', 'editor', 'thumbnail', 'excerpt'),
        'show_in_rest'        => true,
    ));

    // Testimonials CPT
    register_post_type('testimonial', array(
        'labels' => array(
            'name'               => __('Testimonials', 'krystelis-custom'),
            'singular_name'      => __('Testimonial', 'krystelis-custom'),
            'menu_name'          => __('Testimonials', 'krystelis-custom'),
            'add_new'            => __('Add New Testimonial', 'krystelis-custom'),
            'add_new_item'       => __('Add New Testimonial', 'krystelis-custom'),
            'edit_item'          => __('Edit Testimonial', 'krystelis-custom'),
            'new_item'           => __('New Testimonial', 'krystelis-custom'),
            'view_item'          => __('View Testimonial', 'krystelis-custom'),
            'search_items'       => __('Search Testimonials', 'krystelis-custom'),
            'not_found'          => __('No testimonials found', 'krystelis-custom'),
            'not_found_in_trash' => __('No testimonials found in trash', 'krystelis-custom'),
        ),
        'public'              => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'query_var'           => true,
        'rewrite'             => array('slug' => 'testimonials'),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => 21,
        'menu_icon'           => 'dashicons-format-quote',
        'supports'            => array('title', 'editor', 'thumbnail'),
        'show_in_rest'        => true,
    ));

    // Webinars CPT
    register_post_type('webinar', array(
        'labels' => array(
            'name'               => __('Webinars', 'krystelis-custom'),
            'singular_name'      => __('Webinar', 'krystelis-custom'),
            'menu_name'          => __('Webinars', 'krystelis-custom'),
            'add_new'            => __('Add New Webinar', 'krystelis-custom'),
            'add_new_item'       => __('Add New Webinar', 'krystelis-custom'),
            'edit_item'          => __('Edit Webinar', 'krystelis-custom'),
            'new_item'           => __('New Webinar', 'krystelis-custom'),
            'view_item'          => __('View Webinar', 'krystelis-custom'),
            'all_items'          => __('All Webinars', 'krystelis-custom'),
            'search_items'       => __('Search Webinars', 'krystelis-custom'),
            'not_found'          => __('No webinars found.', 'krystelis-custom'),
        ),
        'public'              => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'query_var'           => true,
        'rewrite'             => array('slug' => 'webinars'),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => 23,
        'menu_icon'           => 'dashicons-video-alt3',
        'supports'            => array('title', 'editor', 'thumbnail', 'excerpt'),
        'show_in_rest'        => true,
    ));    
}
add_action('init', 'krystelis_register_cpts');

/**
 * Flush rewrite rules on theme activation
 */
function krystelis_flush_rewrite_rules() {
    krystelis_register_cpts();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'krystelis_flush_rewrite_rules');

// Add meta box
function krystelis_webinar_meta_box() {
    add_meta_box(
        'webinar_details',
        __('Webinar Details', 'krystelis-custom'),
        'krystelis_webinar_meta_box_callback',
        'webinar',
        'normal',
        'default'
    );
}
add_action('add_meta_boxes', 'krystelis_webinar_meta_box');

// Meta box display callback
function krystelis_webinar_meta_box_callback($post) {
    // Get existing values
    $speaker = get_post_meta($post->ID, '_webinar_speaker', true);
    $date = get_post_meta($post->ID, '_webinar_date', true);
    $time_from = get_post_meta($post->ID, '_webinar_time_from', true);
    $time_to = get_post_meta($post->ID, '_webinar_time_to', true);
    $time_zone = get_post_meta($post->ID, '_webinar_time_zone', true);
    $link = get_post_meta($post->ID, '_webinar_link', true);

    // Output fields
    ?>
    <p>
        <label for="webinar_speaker"><?php _e('Speaker:', 'krystelis-custom'); ?></label><br>
        <input type="text" name="webinar_speaker" id="webinar_speaker" value="<?php echo esc_attr($speaker); ?>" style="width:100%;">
    </p>
    <p>
        <label for="webinar_date"><?php _e('Date:', 'krystelis-custom'); ?></label><br>
        <input type="date" name="webinar_date" id="webinar_date" value="<?php echo esc_attr($date); ?>">
    </p>
    <p>
        <label for="webinar_time_from"><?php _e('Time From:', 'krystelis-custom'); ?></label><br>
        <input type="time" name="webinar_time_from" id="webinar_time_from" value="<?php echo esc_attr($time_from); ?>">
    </p>
    <p>
        <label for="webinar_time_to"><?php _e('Time To:', 'krystelis-custom'); ?></label><br>
        <input type="time" name="webinar_time_to" id="webinar_time_to" value="<?php echo esc_attr($time_to); ?>">
    </p>
    <p>
        <label for="webinar_time_zone"><?php _e('Time Zone (e.g. AM EDT / PM BST):', 'krystelis-custom'); ?></label><br>
        <input type="text" name="webinar_time_zone" id="webinar_time_zone" value="<?php echo esc_attr($time_zone); ?>" style="width:100%;">
    </p>
    <p>
        <label for="webinar_link"><?php _e('Webinar Link:', 'krystelis-custom'); ?></label><br>
        <input type="url" name="webinar_link" id="webinar_link" value="<?php echo esc_attr($link); ?>" style="width:100%;">
    </p>
    <?php
}

// Save meta box data
function krystelis_save_webinar_meta_box($post_id) {
    if (array_key_exists('webinar_speaker', $_POST)) {
        update_post_meta($post_id, '_webinar_speaker', sanitize_text_field($_POST['webinar_speaker']));
    }
    if (array_key_exists('webinar_date', $_POST)) {
        update_post_meta($post_id, '_webinar_date', sanitize_text_field($_POST['webinar_date']));
    }
    if (array_key_exists('webinar_time_from', $_POST)) {
        update_post_meta($post_id, '_webinar_time_from', sanitize_text_field($_POST['webinar_time_from']));
    }
    if (array_key_exists('webinar_time_to', $_POST)) {
        update_post_meta($post_id, '_webinar_time_to', sanitize_text_field($_POST['webinar_time_to']));
    }
    if (array_key_exists('webinar_time_zone', $_POST)) {
        update_post_meta($post_id, '_webinar_time_zone', sanitize_text_field($_POST['webinar_time_zone']));
    }
    if (array_key_exists('webinar_link', $_POST)) {
        update_post_meta($post_id, '_webinar_link', esc_url_raw($_POST['webinar_link']));
    }
}
add_action('save_post', 'krystelis_save_webinar_meta_box');

    // Company News CPT
    register_post_type('company_news', array(
        'labels' => array(
            'name'               => __('Company News', 'krystelis-custom'),
            'singular_name'      => __('Company News', 'krystelis-custom'),
            'menu_name'          => __('Company News', 'krystelis-custom'),
            'add_new'            => __('Add News', 'krystelis-custom'),
            'add_new_item'       => __('Add New News', 'krystelis-custom'),
            'edit_item'          => __('Edit News', 'krystelis-custom'),
            'new_item'           => __('New News', 'krystelis-custom'),
            'view_item'          => __('View News', 'krystelis-custom'),
            'search_items'       => __('Search News', 'krystelis-custom'),
            'not_found'          => __('No news found', 'krystelis-custom'),
            'not_found_in_trash' => __('No news found in trash', 'krystelis-custom'),
        ),
        'public'              => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'query_var'           => true,
        'rewrite'             => array('slug' => 'company-news'),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => 24,
        'menu_icon'           => 'dashicons-megaphone',
        'supports'            => array('title', 'editor', 'thumbnail', 'excerpt', 'comments'),
        'show_in_rest'        => true,
    ));