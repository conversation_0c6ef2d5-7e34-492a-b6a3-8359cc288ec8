<?php
/**
 * Template for Services Page
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <div class="content-area">
            <div class="main-content">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                        <header class="entry-header">
                            <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                        </header>

                        <div class="entry-content">
                            <?php the_content(); ?>

                            <!-- Services Overview -->
                            <div class="services-overview">
                                <p class="services-intro">Krystelis provides comprehensive clinical research solutions tailored to your study needs. Our experienced team delivers excellence across all phases of clinical development.</p>
                            </div>

                            <!-- Services Grid -->
                            <?php
                            // Query all services
                            $services_query = new WP_Query(array(
                                'post_type' => 'service',
                                'posts_per_page' => -1,
                                'post_status' => 'publish',
                                'orderby' => 'menu_order',
                                'order' => 'ASC'
                            ));
                            ?>

                            <?php if ($services_query->have_posts()) : ?>
                                <div class="detailed-services-grid">
                                    <?php while ($services_query->have_posts()) : $services_query->the_post(); ?>
                                        <?php
                                        // Get ACF fields with fallbacks
                                        $service_icon = krystelis_get_field('icon');
                                        $service_description = krystelis_get_field('description', get_the_ID(), get_the_excerpt());
                                        ?>
                                        <div class="detailed-service-card">
                                            <div class="service-header">
                                                <div class="service-icon">
                                                    <?php if ($service_icon) : ?>
                                                        <img src="<?php echo esc_url($service_icon['url']); ?>" alt="<?php echo esc_attr($service_icon['alt']); ?>">
                                                    <?php else : ?>
                                                        <div class="default-icon">
                                                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                                                                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                                                                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                                                            </svg>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <h3 class="service-title"><?php the_title(); ?></h3>
                                            </div>
                                            <div class="service-content">
                                                <p class="service-description"><?php echo esc_html($service_description); ?></p>
                                                <?php if (get_the_content()) : ?>
                                                    <div class="service-details">
                                                        <?php the_content(); ?>
                                                    </div>
                                                <?php endif; ?>
                                                <a href="<?php the_permalink(); ?>" class="service-link">Learn More →</a>
                                            </div>
                                        </div>
                                    <?php endwhile; ?>
                                </div>
                            <?php else : ?>
                                <!-- Default Services if none are created -->
                                <div class="default-services">
                                    <h2>Our Core Services</h2>
                                    <div class="detailed-services-grid">
                                        <div class="detailed-service-card">
                                            <div class="service-header">
                                                <div class="service-icon">
                                                    <div class="default-icon">
                                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <h3 class="service-title">Clinical Trial Management</h3>
                                            </div>
                                            <div class="service-content">
                                                <p class="service-description">Comprehensive oversight and management of clinical trials from protocol development through study completion.</p>
                                                <div class="service-details">
                                                    <ul>
                                                        <li>Protocol development and review</li>
                                                        <li>Site selection and management</li>
                                                        <li>Patient recruitment strategies</li>
                                                        <li>Study monitoring and oversight</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="detailed-service-card">
                                            <div class="service-header">
                                                <div class="service-icon">
                                                    <div class="default-icon">
                                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" stroke="currentColor" stroke-width="2"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <h3 class="service-title">Data Management & Analysis</h3>
                                            </div>
                                            <div class="service-content">
                                                <p class="service-description">Expert data management and statistical analysis services to ensure data integrity and meaningful insights.</p>
                                                <div class="service-details">
                                                    <ul>
                                                        <li>Database design and validation</li>
                                                        <li>Data collection and cleaning</li>
                                                        <li>Statistical analysis planning</li>
                                                        <li>Biostatistical reporting</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="detailed-service-card">
                                            <div class="service-header">
                                                <div class="service-icon">
                                                    <div class="default-icon">
                                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" stroke="currentColor" stroke-width="2"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <h3 class="service-title">Regulatory Affairs</h3>
                                            </div>
                                            <div class="service-content">
                                                <p class="service-description">Navigate complex regulatory requirements with our expert guidance and submission support.</p>
                                                <div class="service-details">
                                                    <ul>
                                                        <li>Regulatory strategy development</li>
                                                        <li>IND/CTA submissions</li>
                                                        <li>Ethics committee interactions</li>
                                                        <li>Regulatory compliance monitoring</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="detailed-service-card">
                                            <div class="service-header">
                                                <div class="service-icon">
                                                    <div class="default-icon">
                                                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" stroke="currentColor" stroke-width="2"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <h3 class="service-title">Medical Writing</h3>
                                            </div>
                                            <div class="service-content">
                                                <p class="service-description">Professional medical writing services for all phases of clinical development and regulatory submissions.</p>
                                                <div class="service-details">
                                                    <ul>
                                                        <li>Clinical study reports</li>
                                                        <li>Protocol development</li>
                                                        <li>Regulatory documents</li>
                                                        <li>Publication support</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php wp_reset_postdata(); ?>

                            <!-- Call to Action -->
                            <div class="services-cta">
                                <h2>Ready to Get Started?</h2>
                                <p>Contact us today to discuss how our clinical research services can support your next study.</p>
                                <a href="<?php echo esc_url(home_url('/contact')); ?>" class="btn btn-orange">Contact Us</a>
                            </div>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>
        </div>
    </div>
</main>

<style>
.services-overview {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
    border-left: 4px solid #ff6b35;
}

.services-intro {
    font-size: 1.1rem;
    color: #333;
    margin: 0;
    line-height: 1.6;
}

.detailed-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.detailed-service-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.detailed-service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.service-header {
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    color: white;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.service-icon img {
    width: 48px;
    height: 48px;
    object-fit: contain;
    filter: brightness(0) invert(1);
}

.default-icon {
    color: white;
}

.service-title {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.service-content {
    padding: 2rem;
}

.service-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.service-details ul {
    margin-left: 1.5rem;
    color: #666;
}

.service-details li {
    margin-bottom: 0.5rem;
}

.service-link {
    color: #0078d4;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
    display: inline-block;
    margin-top: 1rem;
}

.service-link:hover {
    color: #106ebe;
}

.services-cta {
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    color: white;
    padding: 3rem;
    border-radius: 10px;
    text-align: center;
    margin: 3rem 0;
}

.services-cta h2 {
    margin-bottom: 1rem;
    color: white;
}

.services-cta p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-orange {
    background-color: #ff6b35;
    color: white;
}

.btn-orange:hover {
    background-color: #e55a2b;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

@media (max-width: 768px) {
    .detailed-services-grid {
        grid-template-columns: 1fr;
    }
    
    .service-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .services-cta {
        padding: 2rem;
    }
}

@media (max-width: 1024px) {
    .container,
    .detailed-services-grid {
        padding-left: 20px !important;
        padding-right: 20px !important;
        width: 100% !important;
        box-sizing: border-box;
    }
    .detailed-service-card {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}
</style>

<?php get_footer(); ?>
