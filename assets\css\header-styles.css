/* Sidebar styles */
.mobile-sidebar-menu {
    position: fixed;
    top: 0;
    right: -320px;
    width: 280px;
    max-width: 90vw;
    height:  fit-content;
    background: #fff;
    z-index: 3000;
    box-shadow: -2px 0 16px rgba(0,0,0,0.12);
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    padding-top: 32px; 
    transition: right 0.3s cubic-bezier(.4,0,.2,1);
}
.mobile-sidebar-menu.active {
    right: 0;
}
.sidebar-close {
    background: none;
    border: none;
    font-size: 2.5rem;
    color: #171c24;
    margin: 0 24px 0 0;
    cursor: pointer;
    align-self: flex-end;
}
.sidebar-menu-list {
    margin: 48px 0 0 0;
    padding: 0 32px;
    width: 100%;
    font-size: 16px;
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
}
.sidebar-menu-list li a {
    color: #171c24;
    font-family: "Maven Pro", sans-serif;
    font-size: 16px;
    font-weight: 400;
    text-decoration: none;
    position: relative;
    transition: color 0.2s;
}
.sidebar-menu-list li a:hover,
.sidebar-menu-list li a.active {
    color: #0072DA;
    font-weight: 500;
}
.sidebar-menu-list li a.active::after,
.sidebar-menu-list li a:hover::after {
    content: '';
    display: block;
    margin: 8px auto 0 auto;
    width: 8px;
    height: 8px;
    background: #0072DA;
    border-radius: 50%;
}
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    width: 100vw; 
    height: 100vh;
    background: rgba(0,0,0,0.18);
    z-index: 2999;
    transition: opacity 0.3s;
}
.sidebar-overlay.active {
    display: block;
    opacity: 1;
}

/* Sidebar menu: hide all sub-menus by default */
#mobile-sidebar-menu .sub-menu {
    display: none;
    padding-left: 20px;
}

/* Show sub-menu when parent has .open */
#mobile-sidebar-menu li.menu-item-has-children.open > .sub-menu {
    display: block;
}
@media (min-width: 1024px) {
    .mobile-sidebar-menu, .sidebar-overlay { display: none !important; }
}
@media (max-width: 1023px) {
    .mobile-sidebar-menu { display: flex; }
}

.site-header {
    background: transparent;
    width: 100%;
    position: relative;
    margin: 0;
    padding: 30px;
    z-index: 5;
    align-items: center;
}

.header-box {
    position: relative;
    height: 112px;
    width: 100%;
    background: #fff;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    margin: 0 auto;
}

.header-top-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 10px;
    background: red;
    z-index: 21;
}

.header-main {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
}

.header-main::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: 10px;
    width: 100%;
    background: linear-gradient(90deg, #00A8E8 45%, #0072DA 50%, #0072DA 75%);
}

/* Enhanced header container centering */
.header-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    margin: 0 auto;
}

/* Logo section centering */
.site-branding {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
    flex: 0 0 auto !important;
}

.logo-link {
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
}

.site-logo {
    height: 40px !important;
    width: auto !important;
    margin: 0 !important;
}

/* Navigation menu container centering */
.main-navigation {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex: 1 !important;
    margin: 0 !important;
    padding: 0 !important;
}

.nav-menu,
#primary-menu,
.menu {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 !important;
    padding: 0 !important;
    list-style: none !important;
    gap: 40px !important;
}

.nav-menu li,
#primary-menu li,
.menu li {
    position: relative;
}

/* Enhanced Header Menu Hover Effects */
.nav-menu a,
#primary-menu a,
.menu a,
.main-navigation ul li a,
.site-header .main-navigation a,
.header-main .main-navigation a,
.header-container .main-navigation a {
    color: #171c24 !important;
    text-decoration: none !important;
    font-family: "Maven Pro", sans-serif !important;
    font-size: 18.0018px !important;
    font-weight: normal !important;
    position: relative !important;
    padding: 0 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
}

/* Hover Effects */
.nav-menu a:hover,
#primary-menu a:hover,
.menu a:hover,
.main-navigation ul li a:hover,
.site-header .main-navigation a:hover,
.header-main .main-navigation a:hover,
.header-container .main-navigation a:hover,
.nav-menu .current-menu-item a,
#primary-menu .current-menu-item a,
.menu .current-menu-item a,
.main-navigation ul li.current-menu-item a {
    color: #0072DA !important;
    font-weight: 400 !important;
}

/* Active Clicked Menu Items */
.nav-menu a.active-clicked,
#primary-menu a.active-clicked,
.menu a.active-clicked,
.main-navigation ul li a.active-clicked,
.site-header .main-navigation a.active-clicked,
.header-main .main-navigation a.active-clicked,
.header-container .main-navigation a.active-clicked {
    color: #0072DA !important;
    font-weight: 500 !important;
}

/* Blue Point at Bottom on Hover */
.nav-menu a::after,
#primary-menu a::after,
.menu a::after,
.main-navigation ul li a::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background-color: #0072DA;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.nav-menu a:hover::after,
#primary-menu a:hover::after,
.menu a:hover::after,
.main-navigation ul li a:hover::after,
.nav-menu .current-menu-item a::after,
#primary-menu .current-menu-item a::after,
.menu .current-menu-item a::after,
.main-navigation ul li.current-menu-item a::after,
.nav-menu a.active-clicked::after,
#primary-menu a.active-clicked::after,
.menu a.active-clicked::after,
.main-navigation ul li a.active-clicked::after {
    opacity: 1;
}

/* Header Social Section */
.header-social {
    flex: 0 0 auto !important;
    display: flex !important;
    align-items: center !important;
    gap: 5px !important;
    margin-left: 20px !important;
}

/* Responsive adjustments */
@media (min-width: 1024px) {
    .header-box {
        width: 100%;
    }
    
    .header-container {
        width: 100%;
        padding: 0 30px;
    }
}

@media (max-width: 1023px) {
    .header-box {
        width: 100%;
    }
    
    .header-container {
        padding: 0 15px;
    }
    
    .nav-menu,
    #primary-menu,
    .menu {
        gap: 20px !important;
    }
}

@media (max-width: 768px) {
    .site-header {
        padding: 15px;
    }
    
    .header-box {
        height: auto;
        min-height: 80px;
    }
    
    .header-container {
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 0 10px !important;
    }
    
    .site-branding {
        order: 1 !important;
        margin-left: 0 !important;
        margin-top: 0 !important;
    }
    .menu-toggle {
        display: block !important;
        order: 3;
        margin-left: 160px; /* Pushes to the right in a flex container */
        margin-right: 0;   /* Optional: Ensures it's flush against the right if needed */
        flex: 0 0 auto;
        align-items: end;
    }
    

    }
    .header-social {
        display: none !important;
    }


/* Mobile Menu Toggle Button - Hidden on Desktop */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px;
    z-index: 1000;
}

.menu-icon {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.menu-icon span {
    width: 25px;
    height: 3px;
    background: #0072DA;
    transition: all 0.3s ease;
    border-radius: 2px;
}

/* Animated menu icon */
.menu-icon.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.menu-icon.active span:nth-child(2) {
    opacity: 0;
}

.menu-icon.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile menu adjustments */
@media (max-width: 768px) {
    .nav-menu a::after,
    #primary-menu a::after,
    .menu a::after,
    .main-navigation ul li a::after {
        display: none;
    }

    /* Hide regular navigation on mobile */
    .nav-menu,
    #primary-menu,
    .menu {
        display: none !important;
    }

    /* Show mobile menu toggle */
    .menu-toggle {
        display: block !important;
        order: 3;
        margin-right: 10px;
        flex: 0 0 auto;
        margin-left: 160px; /* Pushes it to the right */
        align-items: end; /* Center it vertically, if needed */
    }
    

    /* Mobile Header Responsive */
    .site-header {
        padding-top: 20px;
        padding-bottom: 2px;
        padding-left: 5px;
        padding-right: 5px;
    }

    .header-box {
        width: 95%;
        max-width: calc(100vw - 10px);
        height: 90px;
        margin: 0 auto;
    }

    .header-container {
        padding: 0 5px;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: auto;
        min-height: 40px;
    }

    /* Mobile Logo */
    .site-branding {
        order: 1;
        margin-left: 20px;
        margin-top: 20px;
        flex: 0 0 auto;
        justify-content: flex-start !important;
    }

    .site-logo {
        height: 35px !important;
        margin-top: 20px;
    }

    /* Mobile Navigation - Hidden by default */
    .main-navigation {
        order: 3;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        margin-left: 0;
        display: none;
        z-index: 999;
    }

    .main-navigation.toggled {
        display: block;
    }

    .nav-menu,
    #primary-menu,
    .menu {
        flex-direction: column;
        gap: 0 !important;
        height: auto !important;
        padding: 20px 0;
        margin: 0;
    }

    .nav-menu li,
    #primary-menu li,
    .menu li {
        height: auto !important;
        width: 100%;
        text-align: center;
        border-bottom: 1px solid #f0f0f0;
    }

    .nav-menu li:last-child,
    #primary-menu li:last-child,
    .menu li:last-child {
        border-bottom: none;
    }

    .nav-menu a,
    #primary-menu a,
    .menu a,
    .main-navigation ul li a {
        font-size: 16px !important;
        padding: 15px 20px !important;
        height: auto !important;
        justify-content: center !important;
        display: block !important;
    }

    /* Mobile Social */
    .header-social {
        order: 2;
        justify-content: flex-end !important;
        gap: 8px !important;
    }

    .connect-text {
        font-size: 14px;
    }

    .social-icons {
        gap: 12px;
    }
}

@media (max-width: 480px) {
    .site-header {
        padding: 8px 10px;
    }

    .header-box {
        width: calc(100% - 20px);
        margin: 0 10px;
        min-height: 70px;
    }

    .header-container {
        padding: 0 10px;
        min-height: 70px;
        justify-content: space-between;
    }

    /* Ensure menu toggle is visible on very small screens */
    .menu-toggle {
        display: block !important;
        order: 3;
        margin-left: 148px;
        margin-right: -23px;
    }

    .site-logo {
        height: 30px !important;
    }

    .nav-menu a,
    #primary-menu a,
    .menu a,
    .main-navigation ul li a {
        font-size: 14px !important;
        padding: 6px 0 !important;
    }

    .connect-text {
        font-size: 12px;
    }

    .social-icon {
        width: 20px;
        height: 20px;
    }
}

@media (max-width: 480px) {
    .menu-toggle {
        display: block !important;
        order: 3;
        margin-right: -20px;
        margin-left: 60px;
    }
}



/* Enhanced Social Icons Styling */
.social-icons {
    display: flex;
    gap: 8px;
    align-items: center;
    margin: 0;
   
}

/* Header social section centering */
.header-social {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    gap: 5px !important;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 6px;
    border-radius: 6px;
    transition: all 0.3s ease;
    background: transparent;
}

@media (max-width: 766px) {
  .social-link {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .nav-menu,
  #primary-menu,
  .menu {
    display: none !important;
  }
  .menu-toggle {
    display: block !important;
    order: 3;
    margin-right: 10px;
    flex: 0 0 auto;
  }
  .header-social,
  .social-link {
    display: flex !important;
  }
}

.social-link:hover {
    background: rgba(0, 119, 181, 0.1);
    transform: translateY(-2px);
}

.social-icon {
    width: 24px;
    height: 24px;
    transition: all 0.3s ease;
}

.social-link:hover .social-icon {
    transform: scale(1.1);
}

/* Ensure both icons use LinkedIn blue color */
.linkedin-icon path,
.twitter-icon path {
    fill: #0072DA !important;
}

.social-link:hover .linkedin-icon path,
.social-link:hover .twitter-icon path {
    fill: #0072DA !important;
}

@media (min-width: 1024px) and (max-width: 1290px) {
  .nav-menu,
  #primary-menu,
  .menu {
    display: flex !important;
    flex-direction: row !important;
    gap: 10px !important;
    font-size: 8px;
    align-items: center !important;
    justify-content: center !important;
    position: static !important;
    background: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }
}


@media (min-width: 1290px) and (max-width: 2560px) {
  .nav-menu,
  #primary-menu,
  .menu {
    display: flex !important;
    flex-direction: row !important;
    gap: 40px !important;
    align-items: center !important;
    justify-content: center !important;
    position: static !important;
    background: none !important;
    box-shadow: none !important;
    padding: 0 !important;
  }
  .menu-toggle {
    display: none !important;
  }
  .header-social {
    display: flex !important;
  }
}

@media (min-width: 1023px) and (max-width: 1260px) {
    .nav-menu,
    #primary-menu,
    .menu {
        width: 55vw !important;
        gap: 10px !important;
        font-size: 0.8rem !important;
    }
}

/* Add this to your existing header styles */
@media (min-width: 1024px) and (max-width: 1260px) {
    body.page-template-page-contact-us .nav-menu,
    body.page-template-page-contact-us #primary-menu,
    body.page-template-page-contact-us .menu {
        gap: 15px !important;
    }
    body.page-template-page-contact-us .nav-menu a,
    body.page-template-page-contact-us #primary-menu a,
    body.page-template-page-contact-us .menu a {
        font-size: 15px !important;
    }

    body.page-template-page-contact-us .header-container {
        padding: 0 15px !important;
    }

    /* Reduce gap between connect text and social icons */
    body.page-template-page-contact-us .header-social {
        gap: 2px !important;
    }

    body.page-template-page-contact-us .connect-text {
        margin-right: 2px !important;
    }

    body.page-template-page-contact-us .social-divider {
        margin: 0 2px !important;
    }

    body.page-template-page-contact-us .social-icons {
        gap: 2px !important;
    }
}

.nav-menu li.menu-item-has-children > a::after,
#primary-menu li.menu-item-has-children > a::after,
.menu li.menu-item-has-children > a::after {
    content: '▼';
    font-size: 0.7em;
    margin-left: 8px;
    color: #0072DA;
    transition: transform 0.2s;
}

.nav-menu li.open > a::after,
#primary-menu li.open > a::after,
.menu li.open > a::after {
    transform: rotate(180deg);
}

/* Submenu dropdown styles */
.nav-menu .sub-menu,
#primary-menu .sub-menu,
.menu .sub-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 220px;
    background: #fff;
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    border-radius: 0 0 8px 8px;
    z-index: 3000;
    padding: 12px 0;
    margin-top: 8px;
}

.nav-menu li,
#primary-menu li,
.menu li {
    position: relative;
}

.nav-menu li:hover > .sub-menu,
#primary-menu li:hover > .sub-menu,
.menu li:hover > .sub-menu,
.nav-menu li.open > .sub-menu,
#primary-menu li.open > .sub-menu,
.menu li.open > .sub-menu {
    display: block;
}

.nav-menu .sub-menu li,
#primary-menu .sub-menu li,
.menu .sub-menu li {
    width: 100%;
    padding: 0;
    margin: 0;
}

/* Add spacing between submenu items */
.nav-menu .sub-menu li,
#primary-menu .sub-menu li,
.menu .sub-menu li {
    margin: 5px 0 !important; /* Vertical spacing between items */
}

/* Add equal padding inside each submenu item */
.nav-menu .sub-menu a,
#primary-menu .sub-menu a,
.menu .sub-menu a {
    padding: 10px 16px !important; /* Top/bottom, left/right padding */
    display: block !important;
    font-size: 15px !important;
    border-radius: 4px;
    color: #171c24 !important;
}

/* Optional: Add hover background for better UX */
.nav-menu .sub-menu a:hover,
#primary-menu .sub-menu a:hover,
.menu .sub-menu a:hover {
    background-color: #0072DA !important;
    color: #f5f7fa !important;
}


/* Compact submenu styling fix */
/* .nav-menu .sub-menu,
#primary-menu .sub-menu,
.menu .sub-menu {
    z-index: 9999 !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    display: none;
    background: #fff;
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    border-radius: 6px;
    padding: 4px 0;
} */

/* Show submenu on hover */
.nav-menu li:hover > .sub-menu,
#primary-menu li:hover > .sub-menu,
.menu li:hover > .sub-menu {
    display: block !important;
}

/* Make sure submenu sticks while hovering over it
.nav-menu li.menu-item-has-children,
#primary-menu li.menu-item-has-children,
.menu li.menu-item-has-children {
    position: relative;
}

.nav-menu li.menu-item-has-children:hover > .sub-menu,
#primary-menu li.menu-item-has-children:hover > .sub-menu,
.menu li.menu-item-has-children:hover > .sub-menu {
    display: block !important;
}

/* Prevent submenu from being too far down */
.nav-menu .sub-menu,
#primary-menu .sub-menu,
.menu .sub-menu {
    margin-top: 0 !important;
    top: 100% !important;
}

/* Add slight delay to prevent flicker (optional) */
.nav-menu li.menu-item-has-children,
#primary-menu li.menu-item-has-children,
.menu li.menu-item-has-children {
    pointer-events: auto;
} */


/* Allow submenu to overflow outside header */
.header-box,
.header-main,
.header-container {
    overflow: visible !important;
    position: relative !important;
    z-index: 10 !important;
}

/* Optional: reduce spacing between submenu items */
.nav-menu .sub-menu li,
#primary-menu .sub-menu li,
.menu .sub-menu li {
    margin: 0 !important;
}


/* Mobile: show submenu on click (handled by JS) */
@media (max-width: 1023px) {
    .nav-menu .sub-menu,
    #primary-menu .sub-menu,
    .menu .sub-menu {
        position: static;
        box-shadow: none;
        background: #fff;
        display: none;
        padding-left: 20px;
    }
    .nav-menu li.open > .sub-menu,
    #primary-menu li.open > .sub-menu,
    .menu li.open > .sub-menu {
        display: block;
    }
}

/* Remove overflow:hidden to allow submenus to be visible */
/* .header-box, .header-main, .header-container {
    overflow: hidden;
} */

.site-header,
.header-box,
.header-main,
.header-container {
    z-index: 3000 !important;
    position: relative;
}

#content {
    box-shadow: none;
}

.site-content, #content {
    position: relative;
    z-index: 1;
}

@media (max-width: 1023px) {
    .main-navigation {
        z-index: 3100 !important;
        position: relative;
    }
    .nav-menu .sub-menu,
    #primary-menu .sub-menu,
    .menu .sub-menu {
        z-index: 3200 !important;
    }
}

.nav-menu .sub-menu,
#primary-menu .sub-menu,
.menu .sub-menu {
    z-index: 3200 !important;
    position: absolute;
    margin-top: 8px;
}

/* Ensure all header containers allow overflow for submenus */
.site-header,
.header-box,
.header-main,
.header-container {
    overflow: visible !important;
    position: relative !important;
    z-index: 4000 !important;
}

/* Make sure the submenu is above everything */
.nav-menu .sub-menu,
#primary-menu .sub-menu,
.menu .sub-menu {
    z-index: 4100 !important;
} 

/* Hide blue pointer for submenu items (force override) */
.nav-menu .sub-menu a::after,
#primary-menu .sub-menu a::after,
.menu .sub-menu a::after {
    display: none !important;
    content: none !important;
    opacity: 0 !important;
    background: none !important;
    width: 0 !important;
    height: 0 !important;
} 