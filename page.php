<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <div class="content-area">
            <div class="main-content">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                        <header class="entry-header">
                            <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                        </header>

                        <?php if (has_post_thumbnail()) : ?>
                            <div class="post-thumbnail">
                                <?php the_post_thumbnail('large'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="entry-content">
                            <?php
                            the_content();

                            wp_link_pages(array(
                                'before' => '<div class="page-links">' . esc_html__('Pages:', 'krystelis-custom'),
                                'after'  => '</div>',
                            ));

                            // Display ACF fields if available
                            if (function_exists('get_fields')) {
                                $custom_fields = get_fields();
                                if ($custom_fields) {
                                    echo '<div class="custom-fields">';
                                    foreach ($custom_fields as $name => $value) {
                                        if ($value && !is_array($value)) {
                                            echo '<div class="custom-field">';
                                            echo '<strong>' . esc_html(ucfirst(str_replace('_', ' ', $name))) . ':</strong> ';
                                            echo '<span>' . esc_html($value) . '</span>';
                                            echo '</div>';
                                        }
                                    }
                                    echo '</div>';
                                }
                            }
                            ?>
                        </div>

                        <?php if (get_edit_post_link()) : ?>
                            <footer class="entry-footer">
                                <?php
                                edit_post_link(
                                    sprintf(
                                        wp_kses(
                                            __('Edit <span class="screen-reader-text">"%s"</span>', 'krystelis-custom'),
                                            array(
                                                'span' => array(
                                                    'class' => array(),
                                                ),
                                            )
                                        ),
                                        get_the_title()
                                    ),
                                    '<span class="edit-link">',
                                    '</span>'
                                );
                                ?>
                            </footer>
                        <?php endif; ?>
                    </article>

                    <?php
                    // If comments are open or we have at least one comment, load up the comment template.
                    if (comments_open() || get_comments_number()) :
                        comments_template();
                    endif;
                    ?>

                <?php endwhile; ?>
            </div>

            <?php
            // Only show sidebar on pages if it has widgets
            if (is_active_sidebar('sidebar-1')) :
                get_sidebar();
            endif;
            ?>
        </div>
    </div>
</main>

<?php get_footer(); ?>
