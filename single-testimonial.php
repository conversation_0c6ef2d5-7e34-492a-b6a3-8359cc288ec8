<?php
/**
 * The template for displaying single testimonial posts
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <div class="content-area">
            <div class="main-content">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class('testimonial-single'); ?>>
                        <header class="entry-header">
                            <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                        </header>

                        <div class="entry-content">
                            <?php
                            // Get ACF fields
                            $author_name = krystelis_get_field('author_name');
                            $quote = krystelis_get_field('quote', get_the_ID(), get_the_content());
                            $organization = krystelis_get_field('organization');
                            ?>
                            
                            <div class="testimonial-content">
                                <div class="quote-icon">
                                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" fill="currentColor"/>
                                    </svg>
                                </div>
                                
                                <blockquote class="testimonial-quote">
                                    "<?php echo esc_html($quote); ?>"
                                </blockquote>
                                
                                <div class="testimonial-author">
                                    <?php if ($author_name) : ?>
                                        <h3 class="author-name"><?php echo esc_html($author_name); ?></h3>
                                    <?php endif; ?>
                                    
                                    <?php if ($organization) : ?>
                                        <p class="author-organization"><?php echo esc_html($organization); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <?php
                            // Display additional content if any
                            the_content();

                            wp_link_pages(array(
                                'before' => '<div class="page-links">' . esc_html__('Pages:', 'krystelis-custom'),
                                'after'  => '</div>',
                            ));
                            ?>
                        </div>

                        <footer class="entry-footer">
                            <div class="testimonial-navigation">
                                <a href="<?php echo esc_url(get_post_type_archive_link('testimonial')); ?>" class="back-to-testimonials">
                                    &larr; <?php esc_html_e('Back to Testimonials', 'krystelis-custom'); ?>
                                </a>
                            </div>
                        </footer>
                    </article>

                    <?php
                    // Related testimonials
                    $related_testimonials = new WP_Query(array(
                        'post_type' => 'testimonial',
                        'posts_per_page' => 2,
                        'post__not_in' => array(get_the_ID()),
                        'orderby' => 'rand'
                    ));
                    
                    if ($related_testimonials->have_posts()) :
                    ?>
                        <section class="related-testimonials">
                            <h3><?php esc_html_e('Other Testimonials', 'krystelis-custom'); ?></h3>
                            <div class="testimonials-grid">
                                <?php while ($related_testimonials->have_posts()) : $related_testimonials->the_post(); ?>
                                    <?php
                                    $rel_author = krystelis_get_field('author_name');
                                    $rel_quote = krystelis_get_field('quote', get_the_ID(), get_the_content());
                                    $rel_org = krystelis_get_field('organization');
                                    ?>
                                    <div class="testimonial-card">
                                        <blockquote>"<?php echo esc_html(wp_trim_words($rel_quote, 20)); ?>"</blockquote>
                                        <div class="testimonial-meta">
                                            <?php if ($rel_author) : ?>
                                                <strong><?php echo esc_html($rel_author); ?></strong>
                                            <?php endif; ?>
                                            <?php if ($rel_org) : ?>
                                                <span><?php echo esc_html($rel_org); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <a href="<?php the_permalink(); ?>" class="read-full">Read Full Testimonial</a>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        </section>
                    <?php 
                    endif;
                    wp_reset_postdata();
                    ?>

                <?php endwhile; ?>
            </div>

            <?php
            // Only show sidebar on testimonials if it has widgets
            if (is_active_sidebar('sidebar-1')) :
                get_sidebar();
            endif;
            ?>
        </div>
    </div>
</main>

<style>
.testimonial-single .testimonial-content {
    background: #f8f9fa;
    padding: 3rem 2rem;
    border-radius: 10px;
    text-align: center;
    margin: 2rem 0;
}

.testimonial-single .quote-icon {
    color: #0078d4;
    margin-bottom: 2rem;
}

.testimonial-single .testimonial-quote {
    font-size: 1.5rem;
    line-height: 1.6;
    color: #333;
    font-style: italic;
    margin-bottom: 2rem;
    border: none;
    padding: 0;
}

.testimonial-single .author-name {
    font-size: 1.25rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.testimonial-single .author-organization {
    color: #666;
    font-size: 1rem;
}

.back-to-testimonials {
    color: #0078d4;
    text-decoration: none;
    font-weight: 600;
}

.back-to-testimonials:hover {
    text-decoration: underline;
}

.related-testimonials {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.related-testimonials h3 {
    margin-bottom: 2rem;
    color: #333;
}

.related-testimonials .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.related-testimonials .testimonial-card {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.related-testimonials .testimonial-card blockquote {
    font-style: italic;
    margin-bottom: 1rem;
    border: none;
    padding: 0;
}

.related-testimonials .testimonial-meta {
    margin-bottom: 1rem;
}

.related-testimonials .testimonial-meta strong {
    display: block;
    margin-bottom: 0.25rem;
}

.related-testimonials .testimonial-meta span {
    color: #666;
    font-size: 0.9rem;
}

.related-testimonials .read-full {
    color: #0078d4;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.related-testimonials .read-full:hover {
    text-decoration: underline;
}
</style>

<?php get_footer(); ?>
