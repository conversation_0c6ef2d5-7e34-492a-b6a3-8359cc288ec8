<?php
/**
 * Template part for displaying contact form section
 *
 * @package Krystelis_Custom
 */

// Handle form submission
$form_submitted = false;
$form_errors = array();
$success_message = '';

if (isset($_POST['krystelis_contact_submit']) && wp_verify_nonce($_POST['krystelis_contact_nonce'], 'krystelis_contact_form')) {
    $name = sanitize_text_field($_POST['contact_name']);
    $email = sanitize_email($_POST['contact_email']);
    $message = sanitize_textarea_field($_POST['contact_message']);
    
    // Validation
    if (empty($name)) {
        $form_errors[] = 'Name is required.';
    }
    if (empty($email) || !is_email($email)) {
        $form_errors[] = 'Valid email is required.';
    }
    if (empty($message)) {
        $form_errors[] = 'Message is required.';
    }
    
    // If no errors, send email
    if (empty($form_errors)) {
        $to = get_option('admin_email');
        $subject = 'New Contact Form Submission from ' . get_bloginfo('name');
        $body = "Name: {$name}\n";
        $body .= "Email: {$email}\n\n";
        $body .= "Message:\n{$message}";
        
        $headers = array(
            'Content-Type: text/plain; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>',
            'Reply-To: ' . $name . ' <' . $email . '>'
        );
        
        if (wp_mail($to, $subject, $body, $headers)) {
            $success_message = 'Thank you for your message! We will get back to you soon.';
            $form_submitted = true;
        } else {
            $form_errors[] = 'Sorry, there was an error sending your message. Please try again.';
        }
    }
}
?>

<section class="contact-section" id="contact">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Get In Touch</h2>
            <p class="section-subtitle">Ready to start your project? Let's discuss how we can help you achieve your goals.</p>
        </div>
        
        <div class="contact-content">
            <div class="contact-info">
                <h3>Contact Information</h3>
                <div class="contact-item">
                    <strong>Email:</strong>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-item">
                    <strong>Phone:</strong>
                    <a href="tel:+1234567890">+1 (234) 567-890</a>
                </div>
                <div class="contact-item">
                    <strong>Address:</strong>
                    <p>123 Business Street<br>City, State 12345</p>
                </div>
            </div>
            
            <div class="contact-form-wrapper">
                <?php if ($form_submitted && $success_message) : ?>
                    <div class="form-success">
                        <p><?php echo esc_html($success_message); ?></p>
                    </div>
                <?php else : ?>
                    <?php if (!empty($form_errors)) : ?>
                        <div class="form-errors">
                            <ul>
                                <?php foreach ($form_errors as $error) : ?>
                                    <li><?php echo esc_html($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <form class="contact-form" method="post" action="">
                        <?php wp_nonce_field('krystelis_contact_form', 'krystelis_contact_nonce'); ?>
                        
                        <div class="form-group">
                            <label for="contact_name">Name *</label>
                            <input type="text" id="contact_name" name="contact_name" required 
                                   value="<?php echo isset($_POST['contact_name']) ? esc_attr($_POST['contact_name']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="contact_email">Email *</label>
                            <input type="email" id="contact_email" name="contact_email" required 
                                   value="<?php echo isset($_POST['contact_email']) ? esc_attr($_POST['contact_email']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="contact_message">Message *</label>
                            <textarea id="contact_message" name="contact_message" rows="5" required><?php echo isset($_POST['contact_message']) ? esc_textarea($_POST['contact_message']) : ''; ?></textarea>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" name="krystelis_contact_submit" class="btn btn-primary">
                                Send Message
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<style>
.contact-section {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    max-width: 1000px;
    margin: 0 auto;
}

.contact-info h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1.5rem;
}

.contact-item {
    margin-bottom: 1.5rem;
}

.contact-item strong {
    display: block;
    color: #333;
    margin-bottom: 0.5rem;
}

.contact-item a {
    color: #0078d4;
    text-decoration: none;
}

.contact-item a:hover {
    text-decoration: underline;
}

.contact-form-wrapper {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0078d4;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background-color: #0078d4;
    color: white;
}

.btn-primary:hover {
    background-color: #106ebe;
}

.form-success {
    background: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.form-errors {
    background: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.form-errors ul {
    margin: 0;
    padding-left: 1rem;
}

@media (max-width: 768px) {
    .contact-section {
        padding: 2rem 0;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .contact-form-wrapper {
        padding: 1.5rem;
    }
}
</style>
