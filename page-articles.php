<?php
/**
 * Template Name: Articles Page
 * Description: Custom Articles page with the same UI as the Articles Section in Insights.
 * @package Krystelis_Custom
 */

get_header();
?>

<!-- Articles Section -->
<div class="articles-section">
    <div class="articles-container">
        <h2 class="articles-title">Articles</h2>
        <p class="articles-subtitle">Find the latest articles from our experts on topics that matter</p>

        <div class="articles-grid articles-grid-five">
            <?php
            // Query latest 5 posts (customize as needed)
            $args = array(
                'post_type' => 'post',
                'posts_per_page' => 6,
            );
            $insights_query = new WP_Query($args);

            if ($insights_query->have_posts()) :
                while ($insights_query->have_posts()) : $insights_query->the_post();
                    $categories = get_the_category();
                    ?>
                    <div class="article-box">
                        <a href="<?php the_permalink(); ?>" style="text-decoration:none; color:inherit;">
                            <div class="article-image">
                                <?php if (has_post_thumbnail()) : ?>
                                    <?php the_post_thumbnail('large'); ?>
                                <?php else : ?>
                                    <?php
                                    // Fallback images array
                                    $fallback_images = array(
                                        get_template_directory_uri() . '/assets/images/Untitled-design-1-637x478.png',
                                        get_template_directory_uri() . '/assets/images/Untitled-design-3-637x478.png',
                                        get_template_directory_uri() . '/assets/images/Untitled-design-637x478.png',
                                        get_template_directory_uri() . '/assets/images/AI-in-SLR_Hero-image-637x478.png',
                                    );
                                    // Pick a random fallback image
                                    $random_fallback = $fallback_images[array_rand($fallback_images)];
                                    ?>
                                    <img src="<?php echo esc_url($random_fallback); ?>" alt="No Image Available" />
                                <?php endif; ?>
                            </div>
                            <div class="article-overlay">
                                <div class="article-date"><?php echo get_the_date('F j, Y'); ?></div>
                                <h3 class="article-title"><?php the_title(); ?></h3>
                                <?php if (!empty($categories)) : ?>
                                    <div class="article-categories">
                                        <?php foreach ($categories as $cat) : ?>
                                            <!-- <span class="article-category"><?php echo esc_html($cat->name); ?></span> -->
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </a>
                    </div>
                    <?php
                endwhile;
                wp_reset_postdata();
            else :
                echo '<p>No articles found.</p>';
            endif;
            ?>
        </div>
    </div>

    <style>

          /* Articles Section */
articles-grid.articles-section {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding: 80px 20px;
    background: #f8f9fa;
}

.articles-container {
    max-width: 100%;
    margin: 0 20px;
    text-align: center;
}

.articles-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #0072DA;
    margin: 0 0 20px 0;
    line-height: 1.2;
}

.articles-subtitle {
    font-family: Georgia, serif;
    font-size: 22px;
    color: #525252;
    margin: 0 0 60px 0;
    line-height: 1.6;
}

/* .articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 40px;
} */

.articles-grid-five {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 30px;
    margin-top: 40px;
}

/* .articles-grid-five .article-box:nth-child(4),
.articles-grid-five .article-box:nth-child(5) {
    grid-row: 2;
}

.articles-grid-five .article-box:nth-child(4) {
    grid-column: 1 / 2;
}

.articles-grid-five .article-box:nth-child(5) {
    grid-column: 3 / 4;
} */

.article-box {
    position: relative;
    border-radius: 15px;
    overflow: visible;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    background: white;
    display: flex;
    flex-direction: column;
    margin-bottom: 6rem;
}

.article-box:hover,
.article-box.active {
    box-shadow: 0 15px 40px rgba(0, 114, 218, 0.3);
}

.article-box:hover .article-overlay {
    background: rgba(0, 114, 218, 0.95);
    transform: translate(-50%, 50%);
}

.article-box.active .article-overlay {
    background: rgba(0, 114, 218, 0.95);
    transform: translate(-50%, 50%);
}

.article-image {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    flex: 1;
}

.article-image img {
    width: 100%;
    height: 100%;
    border-radius: 15px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-box:hover .article-image img,
.article-box.active .article-image img {
    transform: scale(1.05);
}

.article-overlay {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translate(-50%, 50%);
    width: 90%;
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    padding: 24px 20px 20px 20px;
    z-index: 2;
    text-align: left;
}

.article-overlay-orange {
    background: rgba(255, 255, 255, 0.95);
}

.article-date {
    font-family: "Georgiagit", sans-serif;
    font-size: 16px;
    color: #ff6a18;
    font-weight: 600;
    margin: 0 0 8px 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.article-box:hover .article-date {
    color: #FFFFFF;
}

.article-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 22px;
    font-weight: 600;
    color: #0072DA;
    margin: 0;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.article-box:hover .article-title {
    color: white;
}

.article-box.active .article-title {
    color: white;
}

.article-box.active .article-date {
    color: #FFFFFF;
}

/* Second row of articles */
.articles-grid-second {
    margin-top: 30px;
    grid-template-columns: repeat(2, 1fr);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

        </style>


<?php get_footer(); ?>