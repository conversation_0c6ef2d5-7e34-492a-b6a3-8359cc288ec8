document.addEventListener('DOMContentLoaded', function() {
    // Find the mobile input group
    const mobileInputGroup = document.querySelector('.mobile-input-group');
    const mobileInput = document.getElementById('applicant-mobile');
    
    if (mobileInput && mobileInputGroup) {
        // Remove the existing country code select
        const countrySelect = mobileInputGroup.querySelector('.country-code');
        if (countrySelect) {
            countrySelect.remove();
        }
        
        // Initialize intl-tel-input
        const iti = window.intlTelInput(mobileInput, {
            preferredCountries: ['in', 'us', 'gb', 'au', 'de'],
            separateDialCode: true,
            loadUtils: () => import("https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/25.3.1/build/js/utils.js"),
            geoIpLookup: function(callback) {
                fetch("https://ipinfo.io/json")
                    .then(res => res.json())
                    .then(data => callback(data.country))
                    .catch(() => callback("in")); // Default to India if lookup fails
            },
            initialCountry: "auto"
        });

        // Add custom styling to make it fit the existing design
        mobileInput.style.width = '100%';
        mobileInput.style.padding = '12px 8px';
        mobileInput.style.border = '1px solid #e1e5e9';
        mobileInput.style.borderRadius = '4px';
        mobileInput.style.fontSize = '14px';
        mobileInput.style.fontFamily = '"Maven Pro", sans-serif';
    }
}); 