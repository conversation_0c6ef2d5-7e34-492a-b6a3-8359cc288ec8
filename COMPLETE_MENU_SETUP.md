# 🎯 Complete Menu Setup Guide

## ✅ All Menu Pages Created!

I've created all the page templates you requested:

### 📄 Page Templates Ready:
- **`index.php`** - HOME (already exists)
- **`page-services.php`** - SERVICES 
- **`page-about.php`** - ABOUT US
- **`page-insights.php`** - INSIGHTS (blog/news)
- **`page-careers.php`** - CAREERS
- **`page-contact.php`** - CONTACT US

---

## 🚀 Quick Setup (15 minutes)

### Step 1: Create All Pages in WordPress (5 minutes)

**Go to WordPress Admin → Pages → Add New** and create these 5 pages:

#### 1. Services Page
- **Title:** Services
- **Slug:** services
- **Template:** Will auto-select Services template
- **Content:** Add intro text about your services

#### 2. About Us Page  
- **Title:** About Us
- **Slug:** about-us
- **Template:** Will auto-select About template
- **Content:** Add any additional company info

#### 3. Insights Page
- **Title:** Insights
- **Slug:** insights  
- **Template:** Will auto-select Insights template
- **Content:** Add intro text about your blog/news

#### 4. Careers Page
- **Title:** Careers
- **Slug:** careers
- **Template:** Will auto-select Careers template
- **Content:** Add intro text about working at your company

#### 5. Contact Us Page
- **Title:** Contact Us
- **Slug:** contact-us
- **Template:** Will auto-select Contact template
- **Content:** Add any additional contact info

### Step 2: Create Navigation Menu (5 minutes)

1. **Go to WordPress Admin → Appearance → Menus**
2. **Click "Create a new menu"**
3. **Name it "Primary Menu"**
4. **Add pages in this exact order:**
   - Home (your homepage)
   - Services
   - About Us
   - Insights
   - Careers
   - Contact Us
5. **Under "Menu Settings", check "Primary Menu"**
6. **Click "Save Menu"**

### Step 3: Set Homepage (2 minutes)

1. **Go to WordPress Admin → Settings → Reading**
2. **Set "Your homepage displays" to "A static page"**
3. **Choose your homepage from dropdown**
4. **Save Changes**

### Step 4: Test Everything (3 minutes)

- Visit your website
- Click each menu item
- Verify all pages load correctly
- Test on mobile device

---

## 📋 What Each Page Includes

### 🏠 HOME
- Hero section with background image
- Services grid (auto-populated)
- Testimonials section
- Contact form section

### 🔧 SERVICES
- Services overview
- Detailed services grid
- Auto-shows your Service posts
- Default services if none created
- Call-to-action section

### 👥 ABOUT US
- Company mission and story
- Team information
- Company values (4 value cards)
- Experience statistics
- Professional layout

### 📰 INSIGHTS
- Blog/news introduction
- Featured insights section
- Recent blog posts (auto-populated)
- Default articles if no posts
- Newsletter signup

### 💼 CAREERS
- Why work with us section
- Current job openings (auto-populated)
- Default job listings if none created
- Application process steps
- Contact information for careers

### 📞 CONTACT US
- Contact information display
- Contact form
- Business hours
- Why choose us section
- Professional contact layout

---

## 🎨 Customization Options

### Quick Text Changes:

**Hero Section (Home):**
```php
// Edit template-parts/hero.php lines 13-16
'Making clinical research crystal clear' → 'Your Company Tagline'
'Great people are at the heart...' → 'Your company description'
```

**Contact Information:**
```php
// Edit page-contact.php
+**************** → Your phone
<EMAIL> → Your email
123 Research Drive... → Your address
```

**Company Name:**
- Find and replace "Krystelis" with your company name in all files

### Adding Content:

**Services:**
- WordPress Admin → Services → Add New
- Add title, description, and icon for each service

**Blog Posts (for Insights):**
- WordPress Admin → Posts → Add New
- Regular WordPress blog posts will appear

**Job Openings (for Careers):**
- WordPress Admin → Jobs → Add New (if custom post type exists)
- Or edit default jobs in page-careers.php

**Testimonials:**
- WordPress Admin → Testimonials → Add New
- Add client name, quote, and photo

---

## 🖼️ Images to Add

### Required Images:
- **Logo:** Replace `/assets/images/krystelis-logo.png`
- **Hero Background:** Add as `/assets/images/krystelis-hero-bg.jpg`
- **Service Icons:** 64x64px for each service
- **Client Photos:** Square format for testimonials

### Optional Images:
- **Insight Featured Images:** For blog posts
- **Team Photos:** For About Us page
- **Office Photos:** For various pages

---

## ✅ Final Checklist

### Content Setup:
- [ ] Create all 5 pages in WordPress
- [ ] Set up navigation menu with all 6 items
- [ ] Set static homepage
- [ ] Add your logo
- [ ] Add hero background image

### Content Addition:
- [ ] Create 4-6 services
- [ ] Add 2-3 testimonials  
- [ ] Write 2-3 blog posts (for Insights)
- [ ] Update contact information
- [ ] Customize hero text

### Testing:
- [ ] Test all menu links
- [ ] Check mobile responsiveness
- [ ] Verify contact form works
- [ ] Test on different browsers
- [ ] Check loading speeds

---

## 🎯 Your Complete Menu Structure

```
🏠 HOME
🔧 SERVICES  
👥 ABOUT US
📰 INSIGHTS
💼 CAREERS
📞 CONTACT US
```

**Perfect! Your website will have a complete, professional menu structure with all the pages your visitors expect.** 🚀

---

## 🆘 Need Help?

If you need assistance with:
- Customizing any page content
- Adding specific features
- Changing colors or styling
- Setting up custom post types
- Adding more functionality

**Just let me know what you'd like to work on next!** 🤝
