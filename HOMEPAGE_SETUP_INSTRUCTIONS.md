# Complete Website Setup Guide

## 🎯 Quick Start: What You Need to Do

### ✅ Part 1: Add Content to Your Homepage
1. **Hero Section** - Add your background image and customize text
2. **Services Section** - Create your services with icons
3. **Testimonials Section** - Add customer reviews
4. **Contact Section** - Add your contact information

### ✅ Part 2: Create Menu Pages
1. **About Page** - Your company story
2. **Services Page** - Detailed services listing
3. **Contact Page** - Contact form and info
4. **Set up Navigation Menu**

---

## 📋 Part 1: Homepage Content Setup

### 1. Hero Section (Main Banner)

**Current Content**: "Making clinical research crystal clear"

**To Add Your Background Image**:
```
Option A: Direct Upload
1. Save your image as 'krystelis-hero-bg.jpg'
2. Upload to: /assets/images/krystelis-hero-bg.jpg
3. Image size: 1920x1080px or larger

Option B: WordPress Media Library
1. WordPress Admin → Media → Add New
2. Upload your background image
3. Install ACF plugin for easy management
```

**To Change Hero Text**:
- Edit `template-parts/hero.php` lines 13-16
- Or set up ACF fields for easy editing

### 2. Services Section

**How to Add Services**:
1. **WordPress Admin → Services → Add New**
2. **For each service, add**:
   - Title: Service name
   - Content: Full description
   - Excerpt: Short description for homepage
   - Featured Image: Service icon (64x64px)

**Example Services to Create**:
- Clinical Trial Management
- Data Analysis & Reporting
- Regulatory Compliance
- Patient Recruitment
- Medical Writing
- Quality Assurance

### 3. Testimonials Section

**How to Add Testimonials**:
1. **WordPress Admin → Testimonials → Add New**
2. **For each testimonial**:
   - Title: Client name
   - Content: Testimonial text
   - Featured Image: Client photo (square format)

### 4. Contact Section

**To Customize**:
- Edit `template-parts/contact-form.php`
- Add your contact information
- Customize contact form fields

---

## 📄 Part 2: Creating Menu Pages

### ✅ Page Templates Created
I've created custom page templates for you:
- `page-about.php` - About page with company info, mission, values
- `page-services.php` - Services page with detailed service listings
- `page-contact.php` - Contact page with form and contact info

### 🔧 How to Create the Pages in WordPress

**Step 1: Create the Pages**
1. Go to **WordPress Admin → Pages → Add New**
2. Create these pages:

**About Page:**
- Title: "About"
- Slug: "about"
- Template: About (will auto-select page-about.php)
- Content: Add any additional content you want

**Services Page:**
- Title: "Services"
- Slug: "services"
- Template: Services (will auto-select page-services.php)
- Content: Add intro text about your services

**Contact Page:**
- Title: "Contact"
- Slug: "contact"
- Template: Contact (will auto-select page-contact.php)
- Content: Add any additional contact info

**Step 2: Set Up Navigation Menu**
1. Go to **WordPress Admin → Appearance → Menus**
2. Click "Create a new menu"
3. Name it "Primary Menu"
4. Add pages to menu:
   - Home (your homepage)
   - About
   - Services
   - Contact
5. Drag to reorder as needed
6. Under "Menu Settings", check "Primary Menu"
7. Click "Save Menu"

### 📝 Customizing Your Pages

**About Page Content:**
- Edit company mission and values
- Update team information
- Change statistics (100+ studies, etc.)
- Add your company story

**Services Page:**
- Will automatically show your Services posts
- If no services created, shows default services
- Customize the intro text

**Contact Page:**
- Update contact information (phone, email, address)
- Modify business hours
- Customize contact form fields

---

## 🖼️ Part 3: Adding Images and Content

### Adding Your Logo
```
Option 1: WordPress Customizer
1. Go to Appearance → Customize → Site Identity
2. Upload your logo

Option 2: Replace File
1. Replace /assets/images/krystelis-logo.png with your logo
```

### Adding Hero Background Image
```
1. Save your image as 'krystelis-hero-bg.jpg'
2. Upload to /assets/images/ folder
3. Image should be 1920x1080px or larger
```

### Creating Services (for Services page)
```
1. WordPress Admin → Services → Add New
2. For each service:
   - Title: Service name
   - Content: Detailed description
   - Excerpt: Short description
   - Featured Image: Service icon (64x64px)
```

### Creating Testimonials
```
1. WordPress Admin → Testimonials → Add New
2. For each testimonial:
   - Title: Client name
   - Content: Testimonial text
   - Featured Image: Client photo
```

---

## 🎨 Part 4: Customization Tips

### Changing Colors
Edit these files to change colors:
- Hero section: `template-parts/hero.php`
- Services: `template-parts/services.php`
- Overall theme: `style.css`

### Changing Text Content
- Hero text: Edit `template-parts/hero.php` lines 13-16
- Section titles: Edit respective template files
- Page content: Edit through WordPress admin

### Adding More Pages
1. Create new page in WordPress admin
2. Optionally create custom template (page-[slug].php)
3. Add to navigation menu

---

## ✅ Quick Checklist

**Homepage Setup:**
- [ ] Add hero background image
- [ ] Create 4-6 services
- [ ] Add 2-3 testimonials
- [ ] Customize hero text if needed

**Menu Pages:**
- [ ] Create About page
- [ ] Create Services page
- [ ] Create Contact page
- [ ] Set up navigation menu

**Content:**
- [ ] Upload your logo
- [ ] Update contact information
- [ ] Add your company content
- [ ] Test on mobile devices

**Ready to go live!** 🚀
