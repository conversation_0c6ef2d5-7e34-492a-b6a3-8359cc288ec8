<?php
/**
 * Enqueue scripts and styles for Krystelis Theme
 *
 * @package Krystelis_Custom
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue scripts and styles
 */
function krystelis_enqueue_scripts() {
    // Enqueue Google Fonts - Maven Pro
    wp_enqueue_style(
        'krystelis-google-fonts',
        'https://fonts.googleapis.com/css2?family=Maven+Pro:wght@400;500;600;700&display=swap',
        array(),
        null
    );

    // Enqueue main stylesheet
    wp_enqueue_style(
        'krystelis-style',
        get_stylesheet_uri(),
        array('krystelis-google-fonts'),
        wp_get_theme()->get('Version')
    );

    // Enqueue custom CSS from assets directory
    wp_enqueue_style(
        'krystelis-custom-style',
        get_template_directory_uri() . '/assets/css/custom.css',
        array('krystelis-style'),
        wp_get_theme()->get('Version')
    );

    // Enqueue header styles
    wp_enqueue_style(
        'krystelis-header-styles',
        get_template_directory_uri() . '/assets/css/header-styles.css',
        array('krystelis-custom-style'),
        wp_get_theme()->get('Version')
    );

    // Enqueue careers page specific styles
    if (is_page_template('page-careers.php') || is_page('careers')) {
        wp_enqueue_style(
            'krystelis-careers-style',
            get_template_directory_uri() . '/assets/css/careers.css',
            array('krystelis-custom-style'),
            wp_get_theme()->get('Version')
        );

        // Add intl-tel-input CSS
        wp_enqueue_style(
            'intl-tel-input-css',
            'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/25.3.1/build/css/intlTelInput.min.css'
        );

        // Add custom CSS for intl-tel-input integration
        wp_add_inline_style('intl-tel-input-css', '
            .iti {
                width: 100%;
            }
            .iti__flag-container {
                border-radius: 4px 0 0 4px;
            }
            .iti__selected-flag {
                background-color: #f8f9fa;
                border: 1px solid #e1e5e9;
                border-right: none;
                border-radius: 4px 0 0 4px;
            }
            .iti__country-list {
                border: 1px solid #e1e5e9;
                border-radius: 4px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }
            .iti__country {
                padding: 8px 12px;
                font-family: "Maven Pro", sans-serif;
            }
            .iti__country:hover {
                background-color: #f8f9fa;
            }
            .iti__country.iti__active {
                background-color: #0072DA;
                color: white;
            }
        ');

        // Add intl-tel-input JS
        wp_enqueue_script(
            'intl-tel-input-js',
            'https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/25.3.1/build/js/intlTelInput.min.js',
            array('jquery'),
            null,
            true
        );

        wp_enqueue_script(
            'krystelis-careers-form',
            get_template_directory_uri() . '/assets/js/careers-form.js',
            array('jquery', 'intl-tel-input-js'),
            wp_get_theme()->get('Version'),
            true
        );
    }

    // Enqueue About Us page specific styles
    if (is_page_template('page-about-us.php')) {
        wp_enqueue_style(
            'krystelis-about-us-style',
            get_template_directory_uri() . '/assets/css/about-us.css',
            array('krystelis-custom-style'),
            wp_get_theme()->get('Version')
        );
    }

    // Enqueue mobile hero fix CSS
    wp_enqueue_style(
        'krystelis-mobile-hero-fix',
        get_template_directory_uri() . '/assets/css/mobile-hero-fix.css',
        array('krystelis-custom-style'),
        wp_get_theme()->get('Version')
    );

    // Enqueue mobile hero fix JavaScript
    wp_enqueue_script(
        'krystelis-mobile-hero-fix',
        get_template_directory_uri() . '/assets/js/mobile-hero-fix.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );

    // Enqueue navigation script
    wp_enqueue_script(
        'krystelis-navigation',
        get_template_directory_uri() . '/assets/js/navigation.js',
        array(),
        wp_get_theme()->get('Version'),
        true
    );

    // Enqueue custom JavaScript
    wp_enqueue_script(
        'krystelis-custom',
        get_template_directory_uri() . '/assets/js/custom.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );

    // Localize script for AJAX
    wp_localize_script('krystelis-custom', 'krystelis_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('krystelis_nonce'),
    ));

    // Enqueue comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }

    // Enqueue Swiper for testimonials slider (optional)
    if (is_front_page() || is_page_template('template-parts/testimonials.php')) {
        wp_enqueue_style(
            'swiper-css',
            'https://unpkg.com/swiper@8/swiper-bundle.min.css',
            array(),
            '8.0.0'
        );

        wp_enqueue_script(
            'swiper-js',
            'https://unpkg.com/swiper@8/swiper-bundle.min.js',
            array(),
            '8.0.0',
            true
        );
    }
}
add_action('wp_enqueue_scripts', 'krystelis_enqueue_scripts');

/**
 * Enqueue admin styles and scripts
 */
function krystelis_admin_enqueue_scripts($hook) {
    // Only load on post edit screens for our custom post types
    if ('post.php' !== $hook && 'post-new.php' !== $hook) {
        return;
    }

    global $post_type;
    if (in_array($post_type, array('service', 'testimonial', 'job'))) {
        wp_enqueue_style(
            'krystelis-admin-style',
            get_template_directory_uri() . '/assets/css/admin.css',
            array(),
            wp_get_theme()->get('Version')
        );
    }
}
add_action('admin_enqueue_scripts', 'krystelis_admin_enqueue_scripts');
