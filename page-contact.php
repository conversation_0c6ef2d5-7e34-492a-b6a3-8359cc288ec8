<?php
/**
 * Template for Contact Us Page
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <div class="content-area">
            <div class="main-content">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                        <header class="entry-header">
                            <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                        </header>

                        <div class="entry-content">
                            <?php the_content(); ?>

                            <!-- Contact Information and Form -->
                            <div class="contact-wrapper">
                                <!-- Contact Information -->
                                <div class="contact-info">
                                    <h2>Get in Touch</h2>
                                    <p>Ready to discuss your clinical research needs? Our team is here to help you navigate your next study with confidence.</p>

                                    <div class="contact-details">
                                        <div class="contact-item">
                                            <div class="contact-icon">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" stroke="currentColor" stroke-width="2"/>
                                                </svg>
                                            </div>
                                            <div class="contact-content">
                                                <h3>Phone</h3>
                                                <p>+****************</p>
                                            </div>
                                        </div>

                                        <div class="contact-item">
                                            <div class="contact-icon">
                                                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Email.svg" alt="Email Icon" width="24" height="24">
                                            </div>
                                            <div class="contact-content">
                                                <h3>Email</h3>
                                                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                                            </div>
                                        </div>

                                        <div class="contact-item">
                                            <div class="contact-icon">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" stroke="currentColor" stroke-width="2"/>
                                                    <path d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" stroke="currentColor" stroke-width="2"/>
                                                </svg>
                                            </div>
                                            <div class="contact-content">
                                                <h3>Address</h3>
                                                <p>123 Research Drive<br>Clinical City, CC 12345<br>United States</p>
                                            </div>
                                        </div>

                                        <div class="contact-item">
                                            <div class="contact-icon">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                                                    <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
                                                </svg>
                                            </div>
                                            <div class="contact-content">
                                                <h3>Business Hours</h3>
                                                <p>Monday - Friday: 8:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 2:00 PM<br>Sunday: Closed</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Form -->
                                <div class="contact-form">
                                    <h2>Send us a Message</h2>
                                    <form id="contact-form" method="post" action="">
                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="first-name">First Name *</label>
                                                <input type="text" id="first-name" name="first_name" required>
                                            </div>
                                            <div class="form-group">
                                                <label for="last-name">Last Name *</label>
                                                <input type="text" id="last-name" name="last_name" required>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="email">Email Address *</label>
                                                <input type="email" id="email" name="email" required>
                                            </div>
                                            <div class="form-group">
                                                <label for="phone">Phone Number</label>
                                                <input type="tel" id="phone" name="phone">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="company">Company/Organization</label>
                                            <input type="text" id="company" name="company">
                                        </div>

                                        <div class="form-group">
                                            <label for="subject">Subject *</label>
                                            <select id="subject" name="subject" required>
                                                <option value="">Please select a subject</option>
                                                <option value="general">General Inquiry</option>
                                                <option value="services">Services Information</option>
                                                <option value="partnership">Partnership Opportunity</option>
                                                <option value="support">Support Request</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="message">Message *</label>
                                            <textarea id="message" name="message" rows="6" required placeholder="Please describe your clinical research needs or inquiry..."></textarea>
                                        </div>

                                        <div class="form-group checkbox-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="newsletter" value="yes">
                                                <span class="checkmark"></span>
                                                I would like to receive updates about Krystelis services and clinical research insights.
                                            </label>
                                        </div>

                                        <div class="form-group">
                                            <button type="submit" class="btn btn-orange">Send Message</button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Additional Information -->
                            <div class="contact-additional">
                                <div class="contact-cta">
                                    <h2>Why Choose Krystelis?</h2>
                                    <div class="cta-grid">
                                        <div class="cta-item">
                                            <h3>Expert Team</h3>
                                            <p>Experienced professionals with deep clinical research expertise across multiple therapeutic areas.</p>
                                        </div>
                                        <div class="cta-item">
                                            <h3>Quality Focus</h3>
                                            <p>Commitment to the highest standards of quality, compliance, and regulatory excellence.</p>
                                        </div>
                                        <div class="cta-item">
                                            <h3>Partnership Approach</h3>
                                            <p>Collaborative partnerships that align with your goals and deliver measurable results.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>
        </div>
    </div>
</main>

<style>
.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin: 2rem 0;
}

.contact-info h2,
.contact-form h2 {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    border-bottom: 3px solid #ff6b35;
    padding-bottom: 0.5rem;
    display: inline-block;
}

.contact-details {
    margin-top: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.contact-icon {
    color: #0078d4;
    flex-shrink: 0;
}

.contact-icon img {
    width: 24px;
    height: 24px;
    display: block;
    object-fit: contain;
}

.contact-icon svg {
    width: 24px;
    height: 24px;
    display: block;
}

.contact-content h3 {
    color: #333;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.contact-content p {
    color: #666;
    margin: 0;
    line-height: 1.5;
}

.contact-content p a {
    color: #0078d4;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-content p a:hover {
    color: #106ebe;
    text-decoration: underline;
}

.contact-form {
    background: white;
    padding: 2rem;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0078d4;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.4;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-orange {
    background-color: #ff6b35;
    color: white;
    font-size: 1rem;
}

.btn-orange:hover {
    background-color: #e55a2b;
    transform: translateY(-2px);
}

.contact-additional {
    margin-top: 3rem;
}

.contact-cta {
    background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
    color: white;
    padding: 3rem;
    border-radius: 10px;
    text-align: center;
}

.contact-cta h2 {
    color: white;
    border-bottom: 3px solid #ff6b35;
    margin-bottom: 2rem;
}

.cta-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.cta-item h3 {
    color: white;
    margin-bottom: 1rem;
}

.cta-item p {
    opacity: 0.9;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .cta-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>
