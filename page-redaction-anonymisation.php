<?php
    /* Template Name: Redaction and Anonymisation */
    get_header();
?>
<main id="primary" class="site-main">
  <div class="ra-hero-wrapper">
    <div class="ra-hero-content">
      <div class="ra-hero-text">
        <h3 class="ra-hero-subtitle">Making clinical research crystal clear</h3>
        <h1 class="ra-hero-title">Redaction and anonymisation services</h1>
        <p class="ra-hero-description">
        Data privacy is important. Patients and the public are demanding more transparency in clinical research. In response, regulatory authorities, including the European Medicines Agency (EMA), the US Food and Drug Administration (FDA), and Health Canada (HC), require clinical trial sponsors to make clinical trial documents publicly available. These documents may contain protected personal data (PPD), which is information that could be used to identify individuals. Commercially confidential information (CCI) may also be present in these documents. Clinical study sponsors must anonymise PPD and CCI to reduce the risk of participant identification and protect CCI. They must achieve this without reducing the scientific utility or value of the documents.
        </p>
        <p class="ra-hero-description2">
        Krystelis Services in Support of <a href="https://eur-lex.europa.eu/eli/reg/2014/536/oj" target="_blank" rel="noopener">EU CTR 536/2014</a><br>
        Krystelis Services in support of <a href="https://www.ema.europa.eu/en/human-regulatory/marketing-authorisation/clinical-data-publication/policy-0070" target="_blank" rel="noopener">EMA Policy 0070 and Health Canada Policy</a> on the Public Release of Clinical Information (PRCI)
        </p>
      </div>
      <div class="ra-hero-image">
        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Redaction-and-Anonymisation-Services-Image.png" alt="Redaction and Anonymisation Services" />
      </div>
    </div>
  </div>

  <div class="ra-services-section">
    <p class="ra-services-intro">
      Redacting and/or anonymising documents to meet regulatory requirements can be time-consuming and technically challenging. Many sponsors outsource these activities to specialised vendors such as Krystelis.
    </p>
    <div class="ra-services-content">
      <div class="ra-services-images">
        <img class="ra-services-main-img" src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Redaction-and-Anonymisation-Services-Image.jpg" alt="Redaction and Anonymisation Services" />
      </div>
    </div>
  </div>

  <div class="ra-triangle-section">
    <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Section-Bottom-Background.png" alt="Decorative Triangle" class="ra-triangle-img" />
  </div>

  <!-- New blue services grid section -->
  <section class="ra-blue-services">
    <div class="ra-blue-services-inner">
      <h2 class="ra-blue-services-title">We offer-comprehensive end-to-end<br>document redaction and anonymisation services</h2>
      <div class="ra-blue-services-grid">
        <div class="ra-blue-service-card">Liaising with Health Authorities</div>
        <div class="ra-blue-service-card">Assessment of source documents for PPD</div>
        <div class="ra-blue-service-card">Re-identification risk-assessment</div>
        <div class="ra-blue-service-card">Preparation of an anonymisation plan</div>
        <div class="ra-blue-service-card">Identification of CCI</div>
        <div class="ra-blue-service-card">Literature search to confirm CCI</div>
        <div class="ra-blue-service-card">Anonymisation/redaction of PPD</div>
        <div class="ra-blue-service-card">Redaction of CCI and preparation of justification table/proposed redaction control sheet</div>
        <div class="ra-blue-service-card">Preparation of the anonymisation report</div>
      </div>
    </div>
  </section>

  <!-- Benefits of working with Krystelis section -->
  <section class="ra-benefits-section">
    <div class="ra-benefits-intro">
      Krystelis Subject Matter Experts (SMEs) have been working in this area since 2012. During this time, they have supported redaction and anonymisation activities for many clinical trial sponsors, from small biotechs to top-10 pharmaceutical companies.
    </div>
    <h2 class="ra-benefits-title">Benefits of working with Krystelis</h2>
    <div class="ra-benefits-grid">
      <div class="ra-benefit-card">
      <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
                  <div class="ra-benefit-text">Deep expertise in all policies and guidelines, anonymisation techniques, GDPR and HIPAA</div>
      </div>
      <div class="ra-benefit-card">
      <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
                  <div class="ra-benefit-text">Our experience of voluntary data-sharing requests, investigator requests, and regulatory obligations</div>
      </div>
      <div class="ra-benefit-card">
      <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
                  <div class="ra-benefit-text">Our experience in gap analysis and process development for EU CTR submissions</div>
      </div>
      <div class="ra-benefit-card">
      <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
                  <div class="ra-benefit-text">We have prepared redacted packages under EMA Policies 0043 and 0070, HC PRCI, PMDA and BfArM</div>
      </div>
      <div class="ra-benefit-card">
      <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
                  <div class="ra-benefit-text">Our experience liaising with regulators for consultation and comment resolution</div>
      </div>
      <div class="ra-benefit-card">
      <svg xmlns="http://www.w3.org/2000/svg" width="68" height="68" viewBox="0 0 68 68" fill="none">
            <circle cx="34" cy="34" r="34" fill="#41A4FF"></circle>
            <path
              d="M25.365 42.73L27.1266 38.5308L28.9455 39.3034L29.9697 36.8259L31.0513 37.2794L31.2889 36.6831H31.3053H29.1668V38.2704L25.7746 38.2452V40.9747L19.9327 40.9411V45.5182H12.3291L19.3509 48.4997L22.2514 41.4114L25.365 42.73Z"
              fill="white"></path>
            <path
              d="M20.8717 30.8045L22.1908 34.0799L26.3121 32.3246L27.0823 34.2311L29.4994 33.1897L29.9583 34.3234L30.5646 34.0631L29.0324 32.5346L27.9672 33.6348L25.542 31.1908L23.7066 33.0721L19.5443 28.8813L16.4635 32.0307L11.0312 26.5465L13.9645 33.8195L20.8717 30.8045Z"
              fill="white"></path>
            <path
              d="M25.9239 25.5974L30.0206 27.403L29.2668 29.2675L31.6839 30.3173L31.2414 31.4259L31.8232 31.6694V29.9729L31.8314 29.4942H30.2746V27.5122L30.291 27.5206L30.2992 26.0173H27.6363L27.6691 20.0292H23.2036V12.2355L20.2949 19.4329L27.2102 22.406L25.9239 25.5974Z"
              fill="white"></path>
            <path
              d="M47.1357 38.0945L45.8083 34.8191L41.687 36.5744L40.9168 34.6679L38.4997 35.7093L38.0409 34.5756L37.4346 34.8359L38.9668 36.3644L40.0319 35.2642L42.4572 37.7082L44.2925 35.8353L48.4548 40.0261L51.5356 36.8767L56.9679 42.3525L54.0346 35.0795L47.1357 38.0945Z"
              fill="white"></path>
            <path
              d="M42.6348 26.1683L40.8732 30.3675L39.0543 29.5948L38.0301 32.0724L36.9567 31.6188L36.7109 32.2151L38.8331 32.2235V30.6278L42.2252 30.653V27.9235L48.0671 27.9571V23.38H55.6707L48.6489 20.3986L45.7484 27.4868L42.6348 26.1683Z"
              fill="white"></path>
            <path
              d="M48.4221 28.8476L44.2843 33.072L42.4408 31.1739L40.0319 33.6347L38.9504 32.5261L37.4346 34.063L38.0409 34.3233L38.4915 33.2148L40.9168 34.231L41.6788 32.3581L45.8165 34.0798L47.1193 30.8632L54.0428 33.8278L57.0007 26.5128L51.5274 32.0306L48.4221 28.8476Z"
              fill="white"></path>
            <path
              d="M42.0761 43.3011L37.9793 41.4954L38.7331 39.631L36.316 38.5812L36.7585 37.481L36.1768 37.229V39.4042H37.7253L37.7008 42.8811H40.3636L40.3309 48.8692H44.7963V56.6629L47.705 49.4655L40.7897 46.4925L42.0761 43.3011Z"
              fill="white"></path>
            <path
              d="M27.6338 42.8817H30.2721V39.3964H31.8124L31.8206 37.2128V37.2296L31.2389 37.4815L31.6895 38.5985L29.2643 39.6315L30.0263 41.5296L25.9131 43.3016L27.2241 46.5434L20.2842 49.4745L23.2011 56.7139V48.8614H27.6256L27.6338 42.8817Z"
              fill="white"></path>
            <path
              d="M19.5796 40.0513L23.7173 35.8269L25.569 37.725L27.9697 35.2642L29.0513 36.3728L30.5671 34.8359L29.9608 34.5756L29.5101 35.6842L27.0848 34.6679L26.3228 36.5492L22.1851 34.8191L20.8823 38.0357L13.9588 35.0795L11.001 42.3861L16.4742 36.8683L19.5796 40.0513Z"
              fill="white"></path>
            <path
              d="M39.4672 49.2299L35.3458 44.9887L37.1976 43.0991L34.7969 40.63L35.8784 39.5214L34.379 37.9677L34.125 38.5891L35.2065 39.051L34.2151 41.537L36.0505 42.318L34.3626 46.5592L37.5007 47.8946L34.6166 54.9912L41.745 58.0231L36.3618 52.4129L39.4672 49.2299Z"
              fill="white"></path>
            <path
              d="M19.9393 27.9155L25.7812 27.9239L25.773 30.6282H29.1733V32.2071L31.3036 32.2155H31.2873L31.0415 31.6108L29.9517 32.0811L28.9439 29.5952L27.0922 30.3762L25.3634 26.1602L22.2007 27.504L19.3411 20.3905L12.2783 23.3803H19.9393V27.9155Z"
              fill="white"></path>
            <path
              d="M40.3636 26.0255L37.7253 26.0171V29.5025H36.185L36.1768 31.6861L36.7667 31.4173L36.3079 30.3003L38.7331 29.2673L37.9711 27.3693L42.0843 25.5972L40.7733 22.3554L47.7132 19.4244L44.7963 12.1849V20.0375H40.3718L40.3636 26.0255Z"
              fill="white"></path>
            <path
              d="M48.0597 40.9831L42.2259 40.9747V38.2704H38.8256V36.6915L36.6953 36.6831H36.7117L36.9575 37.2878L38.0472 36.8175L39.055 39.3034L40.9068 38.5224L42.6356 42.7384L45.7983 41.3946L48.6578 48.5081L55.7206 45.5182H48.0597V40.9831Z"
              fill="white"></path>
            <path
              d="M33.7837 27.3615L31.9566 26.5804L33.6363 22.3392L30.4981 21.0039L33.3905 13.9072L26.2539 10.8754L31.637 16.4855L28.5317 19.6685L32.653 23.9097L30.8013 25.8078L33.202 28.2685L32.1205 29.3771L33.6199 30.9308L33.8739 30.3093L32.7923 29.8474L33.7837 27.3615Z"
              fill="white"></path>
            <path
              d="M33.6223 37.9677L32.131 39.5382L33.2044 40.63L30.8201 43.1159L32.6554 44.9971L28.5669 49.2635L31.6394 52.4213L26.2891 57.9895L33.3846 54.9829L30.4432 47.9114L33.6386 46.5508L31.9262 42.3264L33.7861 41.537L32.7701 39.0594L33.8763 38.5891L33.6223 37.9677Z"
              fill="white"></path>
            <path
              d="M34.379 30.9309L35.8702 29.3604L34.7969 28.2686L37.1812 25.7827L35.354 23.9014L39.4426 19.635L36.37 16.4772L41.7122 10.9091L34.6166 13.9157L37.5581 20.9956L34.3626 22.3477L36.0751 26.5721L34.2151 27.3616L35.2311 29.8391L34.125 30.3094L34.379 30.9309Z"
              fill="white"></path>
          </svg>
                  <div class="ra-benefit-text"><a href="http://localhost/wordpress/insights/" style="color:#2196F3;text-decoration:underline;">Insights</a> gained from our contributions to industry forums (DIA, PHUSE, and Informa Connect)</div>
      </div>
    </div>
  </section>

  <?php get_template_part('template-parts/cta-section'); ?>

  <style>
  .site-main {
    min-height: 60vh;
    margin-top: -4.65551em;
    width: 100%;
  }
  .ra-hero-wrapper {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    background: #fff;
    padding: 0;
    margin-top: 0;
    margin-bottom: 0;
  }
  .ra-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 500px;
    max-width: 100%;
    margin: 0;
    padding: 0;
    gap: 0;
  }
  .ra-hero-text {
    padding: 40px 60px 60px 40px;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    position: relative;
    opacity: 0;
    transform: translateY(40px);
    animation: revealUp 1s cubic-bezier(0.23, 1, 0.32, 1) 0.3s both;
  }
  .ra-hero-subtitle {
    font-family: "Maven Pro", sans-serif;
    font-size: 1.75rem;
    font-weight: 500;
    color: #FF6A18;
    margin: 0 0 15px 0;
    line-height: 1.5;
    position: relative;
    overflow: hidden;
    display: inline-block;
    animation: revealDown 1s cubic-bezier(0.23, 1, 0.32, 1) both;
  }
  .ra-hero-title {
    font-family: "Maven Pro", sans-serif;
    font-size: 48px;
    font-weight: 700;
    color: #0072DA;
    margin: 30px 0 30px 0;
    line-height: 1.2;
  }
  .ra-hero-description, .ra-hero-description2 {
    font-family: Georgia, serif;
    font-size: 20px;
    color: #525252;
    line-height: 1.5;
    margin: 0 0 20px 0;
  }
  .ra-hero-description2 a {
    color: #0072DA;
    text-decoration: underline;
  }
  .ra-hero-image {
    position: relative;
    background: #ffffff;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    overflow: hidden;
    padding: 20px;
    margin-top: 4rem;
    opacity: 0;
    transform: scale(0.85);
    animation: zoomInImg 1s cubic-bezier(0.23, 1, 0.32, 1) 0.6s both;
  }
  .ra-hero-image img {
    width: 100%;
    border-radius: 20px;
    display: block;
    object-fit: cover;
    max-width: 600px;
  }
  @media (max-width: 1024px) {
    .ra-hero-content {
      grid-template-columns: 1fr;
    }
    .ra-hero-image {
      margin-top: 2rem;
      justify-content: center;
      padding: 10px;
    }
    .ra-hero-text {
      padding: 30px 20px 30px 20px;
    }
  }
  @media (max-width: 600px) {
    .ra-hero-title {
      font-size: 2rem;
      margin: 20px 0 20px 0;
    }
    .ra-hero-description, .ra-hero-description2 {
      font-size: 1rem;
    }
    .ra-hero-text {
      padding: 20px 5px 20px 5px;
    }
    .ra-hero-subtitle {
    font-family: "Maven Pro", sans-serif;
    font-size: 1.25rem;
    margin: 0 0 15px 0;
    line-height: 1.5;
    text-align: center;
    margin-top: 20px;
    }
    .ra-hero-image img {
      max-width: 100%;
      border-radius: 10px;
    }
  }
  @keyframes revealUp {
    0% {
      opacity: 0;
      transform: translateY(40px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  @keyframes revealDown {
    0% {
      opacity: 0;
      transform: translateY(-40px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  @keyframes zoomInImg {
    0% {
      opacity: 0;
      transform: scale(0.85);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
  .ra-services-section {
    width: 100%;
    margin: 0 auto;
    padding: 0 0 0 0;
    background: transparent;
    position: relative;
    z-index: 2;
  }
  .ra-services-intro {
    margin: 0 auto 0 auto;
    font-size: 20px;
    color: #525252;
    font-family: Georgia, serif;
    text-align: left;
    padding: 0 30px;
  }
  .ra-services-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 30px;
    margin-bottom: 0;
  }
  .ra-services-images {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
  .ra-services-main-img {
    max-width: 900px;
    width: 100%;
    height: auto;
    display: block;
    margin: 0 auto 0 auto;
    box-shadow: none;
    background: transparent;
  }
  .ra-triangle-section {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    position: relative;
    min-height: 80px;
    margin: 20px 0 0 0;
    z-index: 1;
  }
  .ra-triangle-img {
    width: 170px;
    height: auto;
    display: block;
  }
  /* Blue services grid section */
  .ra-blue-services {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 5rem auto 40px auto;
    position: relative;
    z-index: 3;
  }
  .ra-blue-services-inner {
    background: linear-gradient(135deg, #4AB0FF 0%, #2196F3 100%);
    border-radius: 20px;
    padding: 48px 32px 40px 32px;
    max-width: 1300px;
    width: 95%;
    margin: 0 auto;
    box-shadow: 0 8px 32px rgba(33, 150, 243, 0.10);
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .ra-blue-services-title {
    color: #fff;
    font-family: "Maven Pro", sans-serif;
    font-size: 2.7rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 36px;
    line-height: 1.15;
  }
  .ra-blue-services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px 32px;
    width: 100%;
    margin: 0 auto;
  }
  .ra-blue-service-card {
    background: #fff;
    color: #444;
    font-family: Georgia, serif;
    font-size: 1.25rem;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(33, 150, 243, 0.07);
    padding: 24px 18px;
    text-align: center;
    transition: box-shadow 0.2s;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
  }
  .ra-blue-service-card:hover {
    box-shadow: 0 4px 24px rgba(33, 150, 243, 0.18);
  }
  /* Benefits section */
  .ra-benefits-section {
    width: 100%;
    margin: 0 auto 60px auto;
    padding: 0;
    background: transparent;
    position: relative;
    z-index: 2;
    text-align: center;
  }
  .ra-benefits-intro {
    max-width: 1100px;
    margin: 40px auto 0 auto;
    font-size: 22px;
    color: #525252;
    font-family: Georgia, serif;
    text-align: center;
    padding: 0 20px;
  }
  .ra-benefits-title {
    color: #1686e0;
    font-family: "Maven Pro", sans-serif;
    font-size: 2.7rem;
    font-weight: 700;
    margin: 60px 0 40px 0;
    text-align: center;
    line-height: 1.15;
  }
  .ra-benefits-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 36px 32px;
    width: 100%;
    max-width: 1300px;
    margin: 0 auto;
    padding: 0 10px;
  }
  .ra-benefit-card {
    background: #fff;
  border-radius: 24px;
  border: 1px solid #eaeaea;
  box-shadow: 0 6px 32px rgba(65, 164, 255, 0.13);
  font-family: Georgia, serif;
  font-size: 1.6rem;
  color: #525252;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 36px 36px 36px;
  margin: 0;
  font-weight: 400;
  transition: box-shadow 0.2s;
  text-align: center;
  position: relative;
  height: 100%;
  box-sizing: border-box;
  }
  .ra-benefit-card:hover {
    box-shadow: 0px 0px 25px 0px rgba(65, 164, 255, 0.3137);
  }
  .ra-benefit-icon {
    position: absolute;
  top: -32px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 12px rgba(65, 164, 255, 0.10);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  }
  .ra-benefit-text {
    margin-top: 32px;
  font-size: 1.35rem;
  color: #525252;
  font-family: Georgia, serif;
  line-height: 1.5;
  }
  .ra-benefit-text a {
    color: #2196F3;
    text-decoration: underline;
    font-weight: 500;
  }
  @media (max-width: 1024px) {
    .ra-services-main-img {
      max-width: 100%;
    }
    .ra-services-title {
      font-size: 1.5rem;
    }
    .ra-triangle-img {
      width: 100px;
    }
    .ra-triangle-address {
      font-size: 1rem;
      margin-left: 10px;
    }
    .ra-blue-services-inner {
      padding: 32px 10px 28px 10px;
    }
    .ra-blue-services-title {
      font-size: 2rem;
    }
    .ra-blue-services-grid {
      grid-template-columns: 1fr 1fr;
      gap: 18px 16px;
    }
    .ra-blue-service-card {
      font-size: 1.05rem;
      padding: 18px 10px;
    }
    .ra-benefits-title {
      font-size: 2rem;
    }
    .ra-benefits-grid {
      grid-template-columns: 1fr 1fr;
      gap: 24px 16px;
    }
    .ra-benefit-card {
      font-size: 1.05rem;
      padding: 28px 10px 18px 10px;
      min-height: 140px;
    }
    .ra-benefit-icon {
      width: 40px;
      height: 40px;
      top: -20px;
    }
    .ra-benefit-icon img {
      width: 22px;
      height: 22px;
    }
    .ra-benefit-text {
      font-size: 1.05rem;
      margin-top: 24px;
    }
  }
  @media (max-width: 600px) {
    .ra-services-intro {
      font-size: 1rem;
      padding: 0 10px;
    }
    .ra-services-main-img {
      max-width: 100%;
    }
    .ra-triangle-img {
      width: 60px;
    }
    .ra-blue-services-inner {
      padding: 18px 2px 16px 2px;
    }
    .ra-blue-services-title {
      font-size: 1.1rem;
    }
    .ra-blue-services-grid {
      grid-template-columns: 1fr;
      gap: 12px 0;
    }
    .ra-blue-service-card {
      font-size: 0.95rem;
      padding: 12px 6px;
    }
    .ra-benefits-title {
      font-size: 1.1rem;
    }
    .ra-benefits-grid {
      grid-template-columns: 1fr;
      gap: 16px 0;
    }
    .ra-benefit-card {
      font-size: 0.95rem;
      padding: 18px 6px 10px 6px;
      min-height: 100px;
    }
    .ra-benefit-icon {
      width: 32px;
      height: 32px;
      top: -16px;
    }
    .ra-benefit-icon img {
      width: 16px;
      height: 16px;
    }
    .ra-benefit-text {
      font-size: 0.95rem;
      margin-top: 16px;
    }
  }
  </style>
</main>

<script>
    document.addEventListener('DOMContentLoaded', function () {
  function moveHeroImageMobile() {
    var heroText = document.querySelector('.ra-hero-text');
    var heroImage = document.querySelector('.ra-hero-image');
    var heroSubtitle = document.querySelector('.ra-hero-subtitle');
    var heroContent = document.querySelector('.ra-hero-content');
    
    if (window.innerWidth <= 999) {
      // Mobile: Move image after subtitle inside text
      if (heroText && heroImage && heroSubtitle) {
        var nextSibling = heroSubtitle.nextElementSibling;
        if (nextSibling !== heroImage) {
          if (heroImage.parentNode) {
            heroImage.parentNode.removeChild(heroImage);
          }
          heroSubtitle.parentNode.insertBefore(heroImage, heroSubtitle.nextSibling);
        }
      }
    } else {
      // Desktop: Move image back to end of hero-content
      if (heroContent && heroImage) {
        var lastChild = heroContent.lastElementChild;
        if (lastChild !== heroImage) {
          if (heroImage.parentNode) {
            heroImage.parentNode.removeChild(heroImage);
          }
          heroContent.appendChild(heroImage);
        }
      }
    }
  }

  moveHeroImageMobile();
  window.addEventListener('resize', moveHeroImageMobile);
});
</script>
<?php
get_footer();