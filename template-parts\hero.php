<?php
/**
 * Template part for displaying hero section
 *
 * @package Krystelis_Custom
 */

// Get hero background image from ACF or use default
$hero_bg_image = krystelis_get_field('hero_background_image', get_option('page_on_front'));
$hero_bg_url = $hero_bg_image ? $hero_bg_image['url'] : get_template_directory_uri() . '/assets/images/Krystelis-Home-Main-Banner.png';

// Get hero content from ACF or use defaults
$hero_title = krystelis_get_field('hero_title', get_option('page_on_front'), 'Making<br>clinical research<br>crystal clear');
$hero_subtitle = krystelis_get_field('hero_subtitle', get_option('page_on_front'), 'Great people are at the heart of the most valuable partnerships – our exceptional team stands ready to support you');
$hero_cta_text = krystelis_get_field('hero_cta_text', get_option('page_on_front'), 'READ MORE');
$hero_cta_url = krystelis_get_field('hero_cta_url', get_option('page_on_front'), '#services');

// Get orange shape background
$orange_shape_bg = get_template_directory_uri() . '/assets/images/Bg-Shape-Orange.png';
?>

<section class="hero-section">
    <!-- Full-screen background image -->
    <div class="hero-background">
        <img src="<?php echo esc_url($hero_bg_url); ?>" alt="Krystelis Hero Background" class="hero-bg-image">
        <div class="hero-overlay"></div>
    </div>

    <!-- Orange diagonal design element -->
    <div class="orange-diagonal-shape"></div>

    <!-- Hero content -->
    <div class="hero-container">
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title"><?php echo wp_kses_post($hero_title); ?></h1>
                <p class="hero-subtitle"><?php echo esc_html($hero_subtitle); ?></p>
                <div class="hero-cta">
                    <a href="http://localhost/wordpress/#services-section" class="btn btn-orange hero-btn">
                        <?php echo esc_html($hero_cta_text); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Get content for Why How Who section from ACF or use defaults
$why_title = krystelis_get_field('why_title', get_option('page_on_front'), 'Why we do it?');
$why_content = krystelis_get_field('why_content', get_option('page_on_front'), 'To help our clients improve the lives of patients...');

$how_title = krystelis_get_field('how_title', get_option('page_on_front'), 'How we do it?');
$how_content = krystelis_get_field('how_content', get_option('page_on_front'), '...by providing services recognised for quality, value, and collaboration');

$who_title = krystelis_get_field('who_title', get_option('page_on_front'), 'Who we are');
$who_content = krystelis_get_field('who_content', get_option('page_on_front'), 'A team of life sciences leaders with a passion for customer service. Krystelis is a UK headquartered company with operations in India.');
$who_cta_text = krystelis_get_field('who_cta_text', get_option('page_on_front'), 'READ MORE');
$who_cta_url = krystelis_get_field('who_cta_url', get_option('page_on_front'), 'http://localhost/wordpress/about-us/');

// Get team image
$team_image = get_template_directory_uri() . '/assets/images/why choose us.png';
?>



            <div class="why-how-container">
            <!-- Why/How We Do It Boxes -->
            <div class="why-how-boxes">
                <div class="why-box">
                    <div class="box-icon">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Why-we-do-it-icon.png" alt="Why we do it icon">
                    </div>
                    <div class="box-content">
                        <h3>Why we do it?</h3>
                        <p>To help our clients improve the lives of patients...</p>
                    </div>
                </div>

                <div class="how-box">
                    <div class="box-icon">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/How-we-do-it-icon.png" alt="How we do it icon">
                    </div>
                    <div class="box-content">
                        <h3>How we do it?</h3>
                        <p>...by providing services recognised for quality, value, and collaboration</p>
                    </div>
                </div>
            </div>

        <!-- Bottom Content Row -->
        <div class="content-row">
            <!-- Team Image -->
            <div class="team-image-container">
                <img src="<?php echo esc_url(get_template_directory_uri() . '/assets/images/about.png'); ?>" alt="Team Working" class="team-image">
                <div class="image-decorations">
                    <div class="decoration-shape shape-1"></div>
                    <div class="decoration-shape shape-2"></div>
                    <div class="decoration-shape shape-3"></div>
                </div>
            </div>

            <!-- Who We Are Content -->
            <div class="who-content">
                <h2 class="who-title"><?php echo esc_html($who_title); ?></h2>
                <p class="who-text"><?php echo esc_html($who_content); ?></p>
                <div class="who-cta">
                    <a href="<?php echo esc_url($who_cta_url); ?>" class="btn btn-orange who-btn">
                        <?php echo esc_html($who_cta_text); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php
// Get content for Why Choose Us section from ACF or use defaults
$why_choose_title = krystelis_get_field('why_choose_title', get_option('page_on_front'), 'Why choose us');
$why_choose_content = krystelis_get_field('why_choose_content', get_option('page_on_front'), 'Our clients continuously recognise the quality, value, and cost-effectiveness of the work we deliver to them. Our flexible, proactive and collaborative approach makes us easy to work with.');
$why_choose_cta_text = krystelis_get_field('why_choose_cta_text', get_option('page_on_front'), 'READ MORE');
$why_choose_cta_url = krystelis_get_field('why_choose_cta_url', get_option('page_on_front'), 'http://localhost/wordpress/about-us/');

// Get team image for why choose us section
$why_choose_image = get_template_directory_uri() . '/assets/images/why choose usss.png';
?>

<section class="why-choose-us-section">
    <div class="why-choose-container">
        <div class="why-choose-content">
            <div class="why-choose-text">
                <h2 class="why-choose-title"><?php echo esc_html($why_choose_title); ?></h2>
                <p class="why-choose-description"><?php echo esc_html($why_choose_content); ?></p>
                <div class="why-choose-cta">
                    <a href="<?php echo esc_url($why_choose_cta_url); ?>" class="btn btn-orange why-choose-btn">
                        <?php echo esc_html($why_choose_cta_text); ?>
                    </a>
                </div>
            </div>
            <div class="why-choose-image-container">
                    <img src="<?php echo esc_url($why_choose_image); ?>" alt="Our Team" class="why-choose-image">
                    <div class="image-decorative-elements">
                        <div class="decorative-element element-1"></div>
                        <div class="decorative-element element-2"></div>
                        <div class="decorative-element element-3"></div>
                        <div class="decorative-element element-4"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.hero-section {
    position: relative;
    min-height: 100vh;
    background: #ffffff;
    overflow: visible; /* Changed from hidden to visible */
    margin-top: -30px;
    width: 100%;
    max-width: 100vw;
}

/* Full-screen background image */
.hero-background {
    position: absolute;
    top: 170px;
    left: 0;
    width: 100%;
    height: 100vh;
    /* z-index: 1; */
    opacity: 0;
    transform: scale(0.96);
    transition: opacity 1.2s cubic-bezier(0.23, 1, 0.32, 1), transform 1.2s cubic-bezier(0.23, 1, 0.32, 1);
}
.hero-background.zoom-in {
    opacity: 1;
    transform: scale(1);
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    z-index: 2;
}


.hero-container {
    position: relative;
    width: 100%;
    max-width: 100vw;
    margin: 0;
    padding: 0;
    z-index: 4;
    overflow: hidden; /* Prevent content overflow */
}

.hero-content {
    display: flex;
    align-items: center;
    min-height: 100vh;
    width: 100%;
    padding-top: 120px; /* Space for header */
}

.hero-text {
    flex: 1;
    max-width: 50%;
    padding: 2rem 20px 2rem 20px; /* 20px horizontal margins as per user preference */
    z-index: 5;
    position: relative;
}

.hero-title {
    font-size: 3.2rem;
    font-weight: 600;
    margin-bottom: 1rem !important;
    text-align: left;
    margin-left: 20px;
    margin-top: 5rem;
    line-height: 1.1;
    color: #0072DA;
    font-family: "Maven Pro", sans-serif;
    opacity: 0;
    transform: translateY(80px);
    transition: opacity 1.4s cubic-bezier(0.23, 1, 0.32, 1), transform 1.4s cubic-bezier(0.23, 1, 0.32, 1);
}
.hero-title.slide-in {
    opacity: 1;
    transform: translateY(0);
}

.hero-subtitle {
    font-size: 24.0024px;
    max-width: 500px;
    color: #525252;
    margin-left: 20px;
    line-height: 1.6;
    font-family: 'Arial', sans-serif;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.hero-subtitle.slide-in {
    opacity: 1;
    transform: translateY(0);
}

.hero-cta {
    margin-top: 2rem;
    opacity: 0;
    transform: translateY(80px);
    transition: opacity 1.7s cubic-bezier(0.23, 1, 0.32, 1), transform 1.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.hero-cta.slide-in {
    opacity: 1;
    transform: translateY(0);
}

.btn {
    display: inline-block !important;
    padding: 1.25rem 2.50rem !important;
    margin-left: 1rem !important;
    text-decoration: none !important;
    border-radius: 15px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    cursor: pointer !important;
    font-size: 1.50rem !important;
    font-family: "Maven Pro", sans-serif !important;
}

.btn-orange,
.hero-btn {
    background: linear-gradient(90deg, #ff7f3f 0%, #ff6b2b 100%);
    box-shadow: 0px 0px 20px 0px #FFBA9D;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    color: white;
}

.btn-orange:hover {
    background-color: #e55a2b;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 100vh;
        margin-top: 0;
    }

    .hero-background {
        width: 100%;
        height: 100vh;
        position: absolute;
        top: 44px;
        left: 0;
        width: 100%;
        z-index: 1;
    }

    .hero-content {
        flex-direction: column;
        min-height: 100vh;
        padding-top: 80px;
    }

    .hero-text {
        max-width: 100%;
        text-align: center;
        padding: 2rem 5px; /* Minimal margins on mobile as per user preference */
        order: 2;
    }

    .hero-title {
        font-size: 2.2rem;
        margin-bottom: 1rem !important;
        margin-left: 3rem;
        margin-top: 20rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        max-width: 100%;
        margin-left: 2.50rem;
        padding: 0 10px;
        text-align: left;
    }

    .btn {
        padding: 1.2rem 2.5rem;
        font-size: 1.1rem;
        margin-left: 10px;

    }

    .hero-cta {
        margin-top: 1.5rem;
    }
}

@media (min-width: 320px) and (max-width: 767px) {
    .hero-title {
        margin-top: 20rem !important;
        margin-left: 15px;
        margin-bottom: 10px;
    }
    .hero-subtitle{
        margin-left:10px;
        margin-bottom: 5px;
    }
    .btn{
        margin-left: 1rem;
        margin-bottom: 10px;
    }
    .hero-background {
        height: 50vh;
        top: 150px;
    }
}

.hero-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start; /* Use center if you want everything centered */
    justify-content: flex-start;
    gap: 1rem; /* Space between title, subtitle, and button */
    max-width: 100%;
    padding: 2rem 20px;
    z-index: 5;
    position: relative;
    text-align: left;
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
        margin-left: 7px;
    }

    .hero-image {
        height: 35vh;
    }

    .orange-accent-line {
        width: 120px;
        height: 5px;
    }

    .btn {
        padding: 1.2rem 2.5rem;
        font-size: 1rem;
    }
}

/* Why Choose Us Section Styles */
.why-choose-us-section {
    padding: 1rem 0;
    background:rgb(240, 240, 240);
    position: relative;
    overflow: visible; /* Changed from hidden to visible */
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
}

/* Gentle flowing wave at the top - reversed direction */
.why-choose-us-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background: rgb(255, 255, 255);
    clip-path: polygon(0% 64%, -451% -53%, -66% 65%, -16% 70%, 65%, 40% 60%, 70% 65%, 60% 70%, 70% 65%, 80% 60%, 90% 65%, 100% 70%, 100% 100%);
    transform: translateY(-40px);
    z-index: 1;
}



.why-choose-container {
    max-width: 1200px;
    margin: 0 auto;
    margin-left: 60px;
    padding: 0 20px;
}

.why-choose-content {
    display: flex;
    align-items: center;
    gap: 4rem;
    flex-wrap: wrap;
}

.why-choose-text {
    flex: 1;
    min-width: 300px;
    max-width: 500px;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.why-choose-text.slide-in {
    opacity: 1;
    transform: translateY(0);
}

.why-choose-title {
    font-size: 3.5rem;
    font-weight: 600;
    color: #0072DA;
    margin-bottom: 1.5rem;
    font-family: "Maven Pro", sans-serif;
    line-height: 1.2;
}

.why-choose-description {
    font-size: 1.4rem;
    line-height: 1.3;
    color: #525252;
    margin-bottom: 2rem;
    font-family: "Arial", sans-serif;
}

.why-choose-cta {
    margin-top: 2rem;
}

.why-choose-image-container {
    flex: 1;
    min-width: 300px;
    display: flex;
    justify-content: right;
    margin-top: 20px;
    margin-right: 10px;
    align-items: center;
}

.why-choose-image {
  width: 110%;
  height: 80%;
  object-fit: cover;
  object-position: center;
  margin-left: 450px;
  opacity: 0;
  transform: scale(0.92);
  transition: opacity 1.2s cubic-bezier(0.23, 1, 0.32, 1), transform 1.2s cubic-bezier(0.23, 1, 0.32, 1);
}
.why-choose-image.zoom-in {
  opacity: 1;
  transform: scale(1);
}

.image-decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.decorative-element {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, #4A90E2, #357ABD);
    opacity: 0.8;
}

/* Responsive Design for Why Choose Us Section */
@media (max-width: 768px) {
    .why-choose-us-section {
        padding: 4rem 0;
    }

    .why-choose-container {
        padding: 0 2px; /* Minimal margins as per user preference */
    }

   .why-choose-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

    .why-choose-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .why-choose-text {
        max-width: 100%;
        margin-right: 35px;
    }

    .why-choose-image {
    width: 110%;
    height: 80%;
    object-fit: cover;
    object-position: center;
    margin-left: 15px;
}


    .why-choose-title {
        font-size: 2.50rem;
        align-items: center;
        margin-left: 4rem;
    }

    .circular-image-wrapper {
        width: 280px;
        height: 280px;
    }
}

@media (max-width: 480px) {
    .why-choose-title {
        font-size: 1.8rem;
    }

    .why-choose-description {
        font-size: 1.25rem;
    }


    .element-1 {
        width: 40px;
        height: 40px;
    }

    .element-2 {
        width: 30px;
        height: 30px;
    }

    .element-3 {
        width: 25px;
        height: 25px;
    }

    .element-4 {
        width: 20px;
        height: 20px;
    }
}



/* Why How Who Section Styles */
.why-how-who-section {
    padding: 4rem 0;
    background: #f8f9fa;
    position: relative;
    overflow: visible; /* Changed from hidden to visible */
    width: 100%;
    max-width: 100vw;
    box-sizing: border-box;
}

.why-how-who-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Cards Row */
.cards-row {
    display: flex;
    gap: 2rem;
    margin-bottom: 4rem;
    justify-content: center;
    flex-wrap: wrap;
}

.info-card {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    border-radius: 15px;
    padding: 2rem;
    color: white;
    box-shadow: 0 10px 30px rgba(74, 144, 226, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    flex: 1;
    min-width: 300px;
    max-width: 400px;
    position: relative;
    overflow: hidden;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(74, 144, 226, 0.4);
}

.info-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.info-card:hover::before {
    top: -30%;
    right: -30%;
}

.card-icon {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    font-family: "Maven Pro", sans-serif;
}

.card-text {
    font-size: 1rem;
    line-height: 1.5;
    opacity: 0.95;
    font-family: "Arial", sans-serif;
}

/* Content Row */
.content-row {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.team-image-container {
    flex: 1.5;
    position: relative;
    min-width: 500px;
}

.team-image {
    width: 90%;
    object-fit: cover;
    margin-left: 30px;
    opacity: 0;
    transform: scale(0.92);
    transition: opacity 1.2s cubic-bezier(0.23, 1, 0.32, 1), transform 1.2s cubic-bezier(0.23, 1, 0.32, 1);
}
.team-image.zoom-in {
    opacity: 1;
    transform: scale(1);
}

.image-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.decoration-shape {
    position: absolute;
    background: linear-gradient(135deg, #4A90E2, #357ABD);
    border-radius: 50%;
    opacity: 0.8;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.who-content {
    flex: 1;
    min-width: 300px;
    margin-right: 200px;
    margin-left: 30px;
    opacity: 0;
    transform: translateY(70px);
    transition: opacity 1.3s cubic-bezier(0.23, 1, 0.32, 1), transform 1.3s cubic-bezier(0.23, 1, 0.32, 1);
}
.who-content.slide-in {
    opacity: 1;
    transform: translateY(0);
}

.who-title {
    font-size: 3rem;
    font-weight: 600;
    color: #0072DA;
    margin-bottom: 0.5rem;
    font-family: "Maven Pro", sans-serif;
}

.who-text {
    font-size: 1.5rem;
    line-height: 1.3;
    font-weight: 300;
    color: #525252;
    margin-bottom: 2rem;
    font-family: "Arial", sans-serif;
}

.who-cta {
    margin-top: 2rem;
}

/* Responsive Design for Why How Who Section */
@media (max-width: 768px) {
    .why-how-who-section {
        padding: 3rem 0;
    }

    .why-how-who-container {
        padding: 0 2px; /* Minimal margins as per user preference */
    }

    .cards-row {
        flex-direction: column;
        gap: 1.5rem;
        margin-bottom: 3rem;
    }

    .info-card {
        min-width: auto;
        max-width: none;
    }

    .content-row {
        flex-direction: column;
        gap: 2rem;
    }

    .team-image {
        height: 400px;
        width: 100vh;
        margin-left: 20px;
    }

    .who-title {
        font-size: 2rem;
        text-align: center;
    }

    .who-text {
        text-align: center;
    }

    .who-cta {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .info-card {
        padding: 1.5rem;
    }

    .card-title {
        font-size: 1.3rem;
    }

    .who-title {
        font-size: 1.8rem;
    }

    .team-image {
        height: 350px;
        width: 98%;
        margin-left: 10px;
    }
}

/* Ultra-wide screens (2560px and above) */
@media (min-width: 2560px) {
    .hero-title {
        font-size: 4rem;
    }
    .hero-subtitle {
        font-size: 2rem;
        max-width: 900px;
    }
    .hero-text {
        max-width: 60%;
    }
}

/* Super ultra-wide screens (3425px and above) */
@media (min-width: 3425px) {
    .hero-title {
        font-size: 5rem;
    }
    .hero-subtitle {
        font-size: 2.5rem;
        max-width: 1200px;
    }
    .hero-text {
        max-width: 50%;
    }
}

/* Large desktop (1024px - 2559px) */
@media (min-width: 1024px) and (max-width: 2559px) {
    .hero-title {
        font-size: 3.2rem;
    }
    .hero-subtitle {
        font-size: 1.5rem;
        max-width: 700px;
    }
    .hero-text {
        max-width: 50%;
    }
}

/* Tablet landscape (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .hero-title {
        font-size: 2rem;
        margin-top: 20rem !important;
        margin-left: 20px !important;
    }
    .hero-subtitle {
        font-size: 1.1rem;
        margin-left: 20px !important;
        width: 40%; /* or max-width: 40%; based on your layout */
    }
    .hero-text {
        max-width: 100%;
        padding: 2rem 10px;
    }
    .hero-background {
        top: 170px;
    }
}

/* Tablet portrait and large mobile (600px - 767px) */
@media (min-width: 600px) and (max-width: 767px) {
    .hero-title {
        font-size: 1.7rem;
        margin-top: 18rem !important;
        margin-left: 20px !important;
    }
    .hero-subtitle {
        font-size: 1rem;
        margin-left: 20px !important;
        width: 50%; /* or max-width: 50%; based on your layout */
    }
    .hero-text {
        max-width: 100%;
        padding: 2rem 8px;
    }
}

/* Small mobile (375px - 599px) */
@media (min-width: 375px) and (max-width: 599px) {
    .hero-title {
        font-size: 1.7rem;
        margin-top: 15rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .btn {
        padding: 1rem 1.8rem;
        font-size: 1rem;
    }
}

/* Extra small mobile (320px - 374px) */
@media (min-width: 320px) and (max-width: 374px) {
    .hero-title {
        font-size: 1.1rem;
        margin-top: 20rem !important;
        margin-left: 10px !important;
    }
    .hero-subtitle {
        font-size: 0.85rem;
        margin-left: 10px !important;
    }
    .hero-text {
        max-width: 100%;
        padding: 2rem 2px;
    }
}

/* Prevent horizontal scroll and double scroll bars */
html, body {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    box-sizing: border-box;
    height: 100%;
}

.hero-section, .hero-container, .hero-content, .hero-text {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    box-sizing: border-box;
}

/* Ensure no double scroll bars */
.why-how-container,
.why-choose-us-section,
.why-how-boxes,
.content-row {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
    overflow-y: visible;
    box-sizing: border-box;
    position: relative;
}

/* Fix for specific containers that might cause scroll issues */
.why-how-container {
    padding: 0;
    margin: 0;
    display: block;
}

.why-how-boxes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    padding: 2rem 20px;
}

/* Additional responsive adjustments for content sections */
@media (max-width: 1023px) {
    .content-row {
        flex-direction: column;
    }
    
    .team-image-container {
        min-width: 100%;
    }
    
    .team-image {
        width: 100%;
        margin-left: 0;
    }
    
    .who-content {
        margin-right: 0;
        margin-left: 0;
        padding: 0 20px;
    }
}

@media (max-width: 767px) {
    .why-how-boxes {
        flex-direction: column;
    }
    
    .why-box, .how-box {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .box-content {
        padding: 15px;
    }
}

/* Ensure images are responsive */
img {
    max-width: 100%;
    height: auto;
}

/* Ensure containers are responsive */
.container, .why-choose-container, .why-how-who-container {
    width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    margin-left: auto;
    margin-right: auto;
}

/* Ensure text remains readable on all devices */
p, h1, h2, h3, h4, h5, h6 {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

@media (max-width: 1024px) {
    .hero-title {
        margin-bottom: 1rem !important;
        margin-top: 15rem;
        font-size: 2.5rem;
    }
    .why-choose-container {
        margin-left: auto !important;
        margin-right: auto !important;
        padding-left: 20px !important;
        padding-right: 20px !important;
        width: 100% !important;
        box-sizing: border-box;
    }
    .why-choose-title {
        margin-left: 20px !important;
    }

    .why-choose-description{
        margin-left: 20px !important;  
    }
    .service-box {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }
}

@media (min-width: 1025px) {
    .hero-title {
        text-align: left;
        margin-left: 20px;
        margin-top: 20rem;
    }
    .hero-subtitle {
        text-align: left;
        margin-left: 20px;
        margin-top: 0;
    }
}

@media (max-width: 768px) {
    .hero-title {
        margin-bottom: 1rem !important;
        margin-top: 15rem 
    }
    .hero-subtitle {
        margin-top: 0 !important;
        text-align: left !important;
        margin-left: 3px !important;
    }
}

@media (max-width: 599px) {
    .hero-title {
        margin-bottom: 1rem !important;
        font-size: 28px;
    }
}

@media (min-width: 769px) and (max-width: 1023px) {
    .why-choose-title {
        margin-top: 30px !important;
    }

    @media (min-width: 767px) and (max-width: 1024px) {
    .content-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        gap: 2rem;
        flex-wrap: nowrap;
    }
    .team-image-container {
        flex: 1 1 50%;
        max-width: 50%;
        min-width: 250px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
    .team-image {
        width: 90%;
        height: auto;
        border-radius: 50%;
        object-fit: cover;
        margin-left: 0;
    }
    .who-content {
        flex: 1 1 50%;
        max-width: 50%;
        min-width: 250px;
        padding: 0 2rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        text-align: left;
    }
}

@media (min-width: 1530px) and (max-width: 2060px) {
    .why-choose-us-section {
        margin-left: 60px !important;
        margin-right: 60px !important;
    }}
}

@media (min-width: 844px) and (max-width: 1750px) {
    .hero-subtitle {
        margin-top: 2rem;
        position: relative;
        z-index: 5;
        max-width: 40%;
        margin-left: 15px;
    }
}

/* Button Responsive Styles */
@media (min-width: 1440px) {
    .btn {
        padding: 1.5rem 3rem;
        font-size: 1.2rem;
        min-width: 200px;
    }
}

@media (min-width: 1025px) and (max-width: 1439px) {
    .btn {
        padding: 1.3rem 2.5rem;
        font-size: 1.1rem;
        min-width: 180px;
    }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .btn {
        padding: 1.2rem 2.2rem;
        font-size: 1rem;
        min-width: 160px;
    }
}

@media (min-width: 481px) and (max-width: 767px) {
    .btn {
        padding: 1rem 2rem;
        font-size: 0.95rem;
        min-width: 140px;
    }
}

@media (max-width: 480px) {
    .btn {
        padding: 0.9rem 1.8rem;
        font-size: 0.9rem;
        min-width: 120px;
    }
}
</style>

<script>
// Optimized scroll animation system to prevent stuttering
(function() {
    'use strict';

    let isScrolling = false;
    let animatedElements = new Set();

    // Optimized animation function that only animates once per element
    function animateOnScroll(selector, offset = 60, className = 'slide-in') {
        const el = document.querySelector(selector);
        if (!el || animatedElements.has(selector)) return;

        const rect = el.getBoundingClientRect();
        const windowHeight = window.innerHeight || document.documentElement.clientHeight;

        // Only animate once when element comes into view
        if (rect.top < windowHeight - offset && !el.classList.contains(className)) {
            el.classList.add(className);
            animatedElements.add(selector); // Mark as animated to prevent re-animation
        }
    }

    // Optimized animation handler using requestAnimationFrame
    function handleScrollAnimations() {
        if (!isScrolling) {
            requestAnimationFrame(() => {
                // Stop checking if all elements are animated
                if (animatedElements.size >= 9) {
                    window.removeEventListener('scroll', handleScrollAnimations);
                    return;
                }

                // Only animate elements that haven't been animated yet
                animateOnScroll('.hero-title', 60, 'slide-in');
                animateOnScroll('.hero-background', 60, 'zoom-in');
                animateOnScroll('.hero-subtitle', 60, 'slide-in');
                animateOnScroll('.hero-cta', 60, 'slide-in');
                animateOnScroll('.why-how-boxes', 60, 'slide-in');
                animateOnScroll('.team-image', 60, 'zoom-in');
                animateOnScroll('.who-content', 60, 'slide-in');
                animateOnScroll('.why-choose-image', 60, 'zoom-in');
                animateOnScroll('.why-choose-text', 60, 'slide-in');

                isScrolling = false;
            });
        }
        isScrolling = true;
    }

    // Passive scroll listener for better performance
    window.addEventListener('scroll', handleScrollAnimations, { passive: true });

    // Initial animation check on load
    document.addEventListener('DOMContentLoaded', handleScrollAnimations);
})();
</script>
