<?php
/**
 * Template for Contact Us Page (Alternative)
 * This template will work for pages with slug "contact-us"
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <!-- Contact Page Layout matching the design -->
    <div class="contact-page-wrapper">
        <div class="contact-page-container">
                                <!-- <PERSON>er Bar -->
                                <div class="orange-header">
                                    <span class="header-text">Making clinical research crystal clear</span>
                                </div>

                                <!-- Main Contact Content -->
                                <div class="contact-main-content">
                                    <!-- Left Side - Contact Info -->
                                    <div class="contact-left">
                                        <h1 class="contact-title">Contact us</h1>
                                        <p class="contact-description">
                                            Our experts would love to talk to you about how we can help you.
                                            Either send us an email or use the form and we will get back to you
                                            right away.
                                        </p>

                                        <div class="contact-methods">
                                            <!-- Connect with us box -->
                                            <div class="contact-box connect-box">
                                                <span class="contact-box-label">Connect with us:</span>
                                                <div class="contact-social-icons">
                                                    <a href="https://www.linkedin.com" target="_blank" class="social-link linkedin">
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                                        </svg>
                                                    </a>
                                                    <a href="https://www.twitter.com" target="_blank" class="social-link twitter">
                                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                                                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                                        </svg>
                                                    </a>
                                                </div>
                                            </div>

                                            <!-- Email address box -->
                                            <div class="contact-box email-box">
                                                <div class="email-icon">
                                                    <svg width="64" height="64" viewBox="0 0 64 64" fill="none" stroke="#0078d4" stroke-width="0">
                                           <path xmlns="http://www.w3.org/2000/svg" d="M63.9984 22.8247C63.9984 22.1809 63.8041 21.6935 63.348 21.2439C61.4791 19.4013 59.6398 17.5307 57.7677 15.6897C57.4449 15.3719 57.3346 15.0557 57.3363 14.6144C57.3511 10.6163 57.3461 6.61811 57.3445 2.62161C57.3445 1.40636 56.9394 1.00293 55.721 1.00293C50.7763 1.00293 45.8316 1.00293 40.8869 1.00293C40.7387 0.996341 40.5872 1.00293 40.439 1.00293C39.8726 1.00293 39.1646 1.00293 38.3479 1.00293C38.2458 1.00293 38.1453 0.999634 38.0432 1.00293C30.5117 1.00293 14.4016 1.00293 9.33996 1.00293C8.02433 1.00293 7.64397 1.37672 7.64397 2.67266C7.64397 6.69056 7.6522 10.7101 7.62751 14.728C7.62586 15.031 7.46614 15.4048 7.25373 15.6222C5.46225 17.4549 3.63783 19.2547 1.83317 21.0743C1.53349 21.3757 1.27663 21.7198 1 22.0442V58.8261C1 60.7165 2.82771 63.9572 5.6516 63.9572C5.66807 63.9572 6.27731 63.9852 6.29048 64H58.8906C62.3402 64 63.9901 60.8861 63.9901 57.3309C63.9901 45.8289 63.9868 34.3285 64 22.8264L63.9984 22.8247ZM57.4005 18.9583C58.558 20.1176 59.7238 21.2835 60.8468 22.4081C59.7222 23.5345 58.5564 24.7003 57.4005 25.8579V18.96V18.9583ZM10.1023 3.46472H10.8252C15.9296 3.46472 35.0366 3.46472 40.141 3.46472C40.3797 3.46472 40.6201 3.47624 40.8572 3.46472C45.2767 3.46472 49.6961 3.46472 54.1139 3.46472H54.8829V4.25677C54.8829 12.1081 54.8763 19.9612 54.896 27.8125C54.896 28.3543 54.7231 28.7182 54.3527 29.0887C47.1966 36.2271 40.0537 43.377 32.9059 50.5219C32.7544 50.6734 32.5914 50.815 32.4317 50.9632C32.3279 50.8694 32.267 50.815 32.2094 50.7574C24.9463 43.4939 17.6816 36.2337 10.4333 28.957C10.2357 28.7594 10.1155 28.3905 10.1155 28.1024C10.099 20.1061 10.1023 12.1114 10.1023 4.11515V3.46307V3.46472ZM7.6061 18.9468V25.9699C6.42715 24.7925 5.23172 23.5987 4.09393 22.4641C5.24819 21.3082 6.42879 20.1258 7.6061 18.9468ZM3.47152 57.8612C3.45671 47.222 3.46 36.5811 3.46 25.9419C3.46 25.8481 3.47152 25.7542 3.47482 25.7163C9.13084 31.3743 14.7885 37.034 20.3968 42.6442C14.9005 48.1375 9.3416 53.6967 3.71357 59.3218C3.6296 58.841 3.46988 58.3519 3.46988 57.8612H3.47152ZM58.1398 61.5185C57.901 61.5613 57.6491 61.5366 57.4038 61.5366C40.7963 61.5366 24.1872 61.5399 7.57976 61.5283C6.94417 61.5283 6.31024 61.3982 5.65325 61.3258C11.3109 55.6678 16.8681 50.1086 22.4764 44.5C24.2663 46.2916 26.1187 48.1474 27.9727 50.0032C29.0891 51.1197 30.2038 52.2378 31.3219 53.3526C32.1485 54.1759 32.7412 54.1743 33.576 53.3394C36.4197 50.4989 39.2601 47.6567 42.1021 44.8145C42.2453 44.6713 42.3952 44.5362 42.4857 44.4506C48.0808 50.046 53.6561 55.6217 59.2809 61.2467C58.94 61.3307 58.5432 61.446 58.1398 61.5185ZM61.5186 57.652C61.5186 58.207 61.3737 58.7619 61.2996 59.2971C55.6666 53.6638 50.0962 48.0931 44.5291 42.5256C50.1242 36.9302 55.8 31.2541 61.4939 25.5599C61.5038 25.6999 61.5268 25.8695 61.5285 26.0407C61.5301 36.5778 61.5334 47.1149 61.5202 57.6504L61.5186 57.652ZM31.9821 35.7347C32.845 35.7644 33.4081 35.3395 33.4641 34.6133C33.5201 33.902 33.0063 33.38 32.1567 33.2861C31.7088 33.2367 31.256 33.2202 30.8147 33.133C26.4529 32.28 23.4512 29.7704 22.1487 25.5286C20.8414 21.2769 21.8721 17.4434 25.1142 14.4053C28.335 11.3869 32.1863 10.6624 36.3588 12.1131C40.8671 13.6807 45.2289 19.0604 42.4758 25.4841C42.0099 26.5693 41.3463 27.4289 40.1426 27.7417C38.8879 28.0678 37.9823 27.4124 37.9164 26.1181C37.8967 25.7097 37.9115 25.2981 37.9115 24.888C37.9115 21.9355 37.9148 18.983 37.9082 16.0305C37.9066 15.2237 37.4192 14.6852 36.7144 14.6687C36.0393 14.6523 35.5487 15.1347 35.4515 15.9103C35.44 15.9976 35.4235 16.0849 35.4054 16.1952C31.8537 13.6988 28.0336 15.6238 26.5352 18.4676C25.0879 21.2143 25.4304 25.0576 27.4112 27.4503C28.656 28.9553 30.2401 29.7968 32.2407 29.6651C33.6337 29.5728 34.7781 28.929 35.7677 27.9492C35.8434 27.9871 35.8681 27.9904 35.8763 28.0036C35.9521 28.1254 36.0212 28.2506 36.0986 28.3708C36.9417 29.6815 38.1091 30.3896 39.698 30.2859C41.7744 30.1492 43.3304 29.1151 44.2904 27.3235C45.6225 24.837 46.1576 22.1611 45.5912 19.3568C44.4797 13.8586 39.5285 9.51956 33.8658 9.01568C24.1032 8.14788 16.7315 17.5652 19.939 26.8064C21.7223 31.9408 26.583 35.547 31.9789 35.7364L31.9821 35.7347ZM34.2017 25.9781C32.9158 27.5655 30.9761 27.6446 29.5584 26.179C27.8459 24.4088 27.6039 21.0414 29.0743 19.067C29.8844 17.9786 30.9646 17.3512 32.3543 17.5224C33.7423 17.6937 34.5805 18.6125 35.0217 19.8624C35.2836 20.6034 35.3741 21.4069 35.4597 21.8005C35.407 23.5872 35.0909 24.8814 34.2017 25.9798V25.9781Z" fill="#0072DA"/>                                                        <polyline points="22,6 12,13 2,6"/>
                                                    </svg>
                                                </div>
                                                <div class="email-info">
                                                    <span class="contact-box-label">Email address:</span>
                                                    <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right Side - Contact Form -->
                                    <div class="contact-right">
                                        <div class="form-container">
                                            <h2 class="form-title">Please fill your details</h2>

                                            <form class="contact-form" method="post" action="">
                                                <div class="form-group">
                                                    <input type="text" name="full_name" placeholder="Please enter your full name" required>
                                                </div>

                                                <div class="form-group">
                                                    <input type="email" name="email" placeholder="Write your work email" required>
                                                </div>

                                                <div class="form-group">
                                                    <input type="text" name="subject" placeholder="Please enter the subject" required>
                                                </div>

                                                <div class="form-group">
                                                    <textarea name="message" rows="5" placeholder="Write your message" required></textarea>
                                                    <div class="character-count">0 of 500 max characters.</div>
                                                </div>

                                                <div class="form-group">
                                                    <button type="submit" class="submit-btn">Send Message</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
        </div>
    </div>
</main>

<style>
/* Contact Page Styling to match the design */
.contact-page-wrapper {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 2px;
    width: 100%;
    box-sizing: border-box;
}

.contact-page-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0px 0px rgba(0, 0, 0, 0.1);
    width: 100%;
    box-sizing: border-box;
}

.orange-header {
    background: transparent;
    padding: 1rem 2rem;
    color: #ff6b35;
    text-align: left;
}

.header-text {
    font-family: "Maven Pro", sans-serif;
    font-size: 1.75rem;
    font-weight: 500;
    line-height: 1.5em;
    text-align: center;
    margin: 0;
    opacity: 0;
  transform: translateY(-120px); /* Only Y, no scale */
  transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.header-text.animated {
  opacity: 1;
  transform: translateY(0);
}


.contact-main-content {
display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 600px;
}

/* Left Side - Contact Info */
.contact-left {
    padding: 1rem 2rem;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.contact-title {
    color: #0078d4;
    font-family: "Maven Pro", sans-serif;
    font-size: 3.5rem;
    font-weight: 600;
    margin: 0px 0px 20px 0px;
    max-width: 766px;
    margin-top: 0;
    opacity: 0;
    transform: translateY(120px);
    transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.contact-title.animated {
    opacity: 1;
    transform: translateY(0);
}

.contact-description {
    color: #525252;
    font-family: 'Arial', sans-serif;
    font-size: 1.4rem;
    line-height: 1.5;
    margin-bottom: 3rem;
    margin-top: -1rem;
    opacity: 0;
    transform: translateY(120px);
    transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.contact-description.animated {
    opacity: 1;
    transform: translateY(0);
}

.contact-methods {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    margin-top: 1rem;
    opacity: 0;
    transform: translateX(-120px);
    transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.contact-methods.animated {
    opacity: 1;
    transform: translateX(0);
}

/* Contact boxes styling */
.contact-box {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    flex: 1;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.contact-box:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.contact-box-label {
    color: #ff6b35;
    font-size: 1.2rem;
    font-weight: 500;
    display: block;
    margin-bottom: 1rem;
}

/* Connect with us box */
.connect-box .contact-social-icons {
    display: flex;
    gap: 0.8rem;
    justify-content: flex-start;
}

.contact-social-icons .social-link {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.contact-social-icons .social-link.linkedin {
    background: #0077b5;
}

.contact-social-icons .social-link.twitter {
    background: #1da1f2;
}

.contact-social-icons .social-link:hover {
    transform: translateY(-2px);
}

/* Email box */
.email-box {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.email-icon {
    flex-shrink: 0;
}

.email-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 0;
}

.email-link {
    color: #9263D9;
    font-family: "Georgia", Sans-serif;
    font-size: 1.25rem;
    font-weight: 300;
    margin-top: -20px;
    text-decoration: none;
}

.email-link:hover {
    color: #ff6b35;
}

/* Right Side - Contact Form */
.contact-right {
    padding: 1rem 2rem;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-right: 20px;
    opacity: 0;
    transform: translateX(120px);
    transition: opacity 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.4s, transform 1.2s cubic-bezier(0.22, 1, 0.36, 1) 0.4s;
}
.contact-right.animated {
    opacity: 1;
    transform: translateX(0);
}

.form-container {
    width: 100%;
    max-width: none;
}

.form-title {
    color: #0072DA;
    font-family: "Maven Pro", Sans-serif;
    font-size: 2.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    margin-top: 0;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    font-family: "Arial", sans-serif;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: #f8f9fa;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0078d4;
    background: white;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #999;
    font-size: 0.9rem;
}

/* Orange placeholder text for specific fields 
.form-group input[name="email"]::placeholder,
.form-group textarea[name="message"]::placeholder {
    font-size: 1.1rem;
    color: rgb(255, 179, 128);
    font-family: "Lora", sans-serif;
}*/

.character-count {
    font-size: 0.8rem;
    color: #999;
    margin-top: 0.5rem;
    text-align: left;
}

.submit-btn {
    background: #ff6b35;
    color: white;
    padding: 0.75rem 1.75rem;
    border: none;
    border-radius: 3px;
    font-family: inherit;
    font-size: 1.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    align-self: flex-start;
}

.submit-btn:hover {
    background: #e55a2b;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-page-wrapper {
        padding: 0 2px;
    }

    .contact-main-content {
        grid-template-columns: 1fr;
    }

    .contact-left,
    .contact-right {
        padding: 2rem 1rem;
    }

    .contact-title {
        font-size: 2.25rem;
        text-align: center;
    }

    .contact-description {
        color: #525252;
        font-family: 'Arial', sans-serif;
        font-size: 1rem;
        margin-bottom: 3rem;
        text-align: center;
    }

    .contact-box-label {
        color: #ff6a18;
        font-size: 1rem;
        font-weight: 500;
        display: block;
        text-align: center;
        margin-bottom: 1rem;
    }

    .connect-box .contact-social-icons {
        display: flex;
        justify-content: center;
        gap: 0.8rem;
    }

    .form-container {
        width: 100%;
        max-width: none;
    }

    .orange-header {
        padding: 1rem 1rem;
        text-align: center;
    }

    .header-text {
        font-size: 1.25rem;
        text-align: center;
        margin: 0;
    }

    /* Stack contact boxes vertically on mobile */
    .contact-methods {
        flex-direction: column;
        gap: 1rem;
    }

    .contact-box {
        padding: 1.25rem;
    }

    .email-box {
        flex-direction: column;
        gap: 1rem;
    }

    /* Form styling for mobile */
    .form-title {
        text-align: center;
        font-size: 1.5rem;
    }

    .form-group input,
    .form-group textarea {
        border-color: #d7d7d7;
    }

    .submit-btn {
        margin: 0 auto;
        display: block;
    }
}

@media (max-width: 480px) {
    .contact-page-wrapper {
        padding: 0 10px;
        margin: 0 10px;
        width: calc(100% - 20px);
        box-sizing: border-box;
    }

    .contact-page-container {
        width: 100%;
        box-sizing: border-box;
    }

    .contact-left,
    .contact-right {
        padding: 1.5rem 10px;
        width: 100%;
        box-sizing: border-box;
    }

    .contact-main-content {
        padding: 0 10px;
        width: 100%;
        box-sizing: border-box;
    }

    .form-container {
        padding: 0 10px;
        width: 100%;
        box-sizing: border-box;
    }

    .orange-header {
        padding: 1rem 10px;
        width: 100%;
        box-sizing: border-box;
    }

    .contact-title {
        font-size: 1.8rem;
    }

    .form-title {
        font-size: 1.3rem;
    }

    .header-text {
        font-size: 1.1rem;
        text-align: center;
    }
}

/* Hide the default page title since we have our own */
.entry-header {
    display: none;
}

/* Add this new media query for tablets */
@media (min-width: 769px) and (max-width: 1024px) {
    .contact-main-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-left {
        padding: 2rem;
    }

    .contact-right {
        padding: 2rem;
        margin-top: 1rem;
    }

    .form-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .contact-methods {
        justify-content: center;
    }

    .contact-box {
        max-width: 400px;
        margin: 0 auto;
    }

    /* Left align header text for tablets */
    .orange-header {
        text-align: left;
        padding-left: 2rem;
    }

    .header-text {
        text-align: left;
        margin: 0;
    }

    /* Form styling for mobile */
    .form-title {
        text-align: center;
        font-size: 1.5rem;
    }

    .form-group input,
    .form-group textarea {
        border-color: #d7d7d7;
    }

    .submit-btn {
        margin: 0 auto;
        display: block;
    }
}

/* Your existing styles ...

@media (min-width: 1560px) and (max-width: 2060px) {
    .contact-page-wrapper {
        max-width: 95%;
        padding: 0 5rem;
    }

    .contact-main-content {
        gap: 4rem;
    }

    .contact-title {
        font-size: 4rem;
    }

    .contact-description {
        font-size: 1.5rem;
    }

    .form-title {
        font-size: 2.5rem;
    }

    .form-group input,
    .form-group textarea {
        font-size: 1.2rem;
        padding: 1.2rem;
    }

    .submit-btn {
        font-size: 1.4rem;
        padding: 1rem 2rem;
    }

    .contact-box {
        padding: 2rem;
    }

    .contact-box-label {
        font-size: 1.3rem;
    }

    .email-link {
        font-size: 1.4rem;
        letter-spacing: 2px;
    }
} */

@media (min-width: 200px) and (max-width: 360px) {
    .contact-page-wrapper {
        padding: 0 10px;
        margin: 0 10px;
        width: calc(100% - 20px);
        box-sizing: border-box;
    }

    .contact-page-container {
        width: 100%;
        box-sizing: border-box;
    }

    .orange-header {
        padding: 0.8rem 10px;
        width: 100%;
        box-sizing: border-box;
        text-align: center;
    }

    .header-text {
        font-size: 1rem;
        line-height: 1.4;
        margin: 0;
        padding: 0 5px;
        text-align: center;
        width: 100%;
        box-sizing: border-box;
    }

    .contact-left,
    .contact-right {
        padding: 1rem 10px;
        width: 100%;
        box-sizing: border-box;
    }

    .contact-main-content {
        padding: 0 10px;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }

    .contact-title {
        font-size: 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .contact-description {
        font-size: 0.9rem;
        text-align: center;
        margin-bottom: 2rem;
        line-height: 1.5;
    }

    .contact-methods {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        width: 100%;
    }

    .contact-box {
        padding: 1rem;
        width: 100%;
        box-sizing: border-box;
    }

    .contact-box-label {
        font-size: 0.9rem;
        margin-bottom: 0.8rem;
        display: block;
        text-align: center;
    }

    .connect-box .contact-social-icons {
        display: flex !important;
        justify-content: center;
        gap: 0.8rem;
    }

    .connect-box .social-link {
        display: flex !important;
        width: 40px;
        height: 40px;
        border-radius: 8px;
        align-items: center;
        justify-content: center;
    }

    .email-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
        align-content: space-between;
    }

    .email-icon {
        width: 40px;
        height: 40px;
    }

    .email-info {
        text-align: center;
    }

    .email-link {
        font-size: 1rem;
        letter-spacing: 2px;
        margin-top: 0.5rem;
    }

    .form-container {
        padding: 0 10px;
        width: 100%;
        box-sizing: border-box;
    }

    .form-title {
        font-size: 1.3rem;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group input,
    .form-group textarea {
        padding: 0.8rem;
        font-size: 0.9rem;
        width: 100%;
        box-sizing: border-box;
    }

    .form-group input::placeholder,
    .form-group textarea::placeholder {
        font-size: 0.85rem;
    }

    .character-count {
        font-size: 0.75rem;
        margin-top: 0.3rem;
    }

    .submit-btn {
        width: 100%;
        text-align: center;
        padding: 0.8rem;
        font-size: 1rem;
    }

    .contact-social-icons {
        display: flex !important;
        justify-content: center;
        gap: 0.8rem;
    }

    .contact-social-icons .social-link {
        display: flex !important;
        width: 40px;
        height: 40px;
        border-radius: 8px;
        align-items: center;
        justify-content: center;
    }
}

@media (min-width: 361px) and (max-width: 767px) {
    .contact-page-wrapper {
        padding: 0 10px;
        margin: 0 10px;
        width: calc(100% - 20px);
        box-sizing: border-box;
    }

    .contact-page-container {
        width: 100%;
        box-sizing: border-box;
    }

    .contact-left,
    .contact-right {
        padding: 1.5rem 10px;
        width: 100%;
        box-sizing: border-box;
    }

    .contact-main-content {
        padding: 0 10px;
        width: 100%;
        box-sizing: border-box;
    }

    .form-container {
        padding: 0 10px;
        width: 100%;
        box-sizing: border-box;
    }

    .orange-header {
        padding: 1rem 10px;
        width: 100%;
        box-sizing: border-box;
    }

    .connect-box .contact-social-icons {
        display: flex;
        justify-content: center;
        gap: 0.8rem;
    }

    .connect-box .social-link {
        display: flex;
        width: 45px;
        height: 45px;
        border-radius: 8px;
        align-items: center;
        justify-content: center;
    }

    .contact-social-icons {
        display: flex;
        justify-content: center;
        gap: 0.8rem;
    }

    .contact-social-icons .social-link {
        display: flex;
        width: 45px;
        height: 45px;
        border-radius: 8px;
        align-items: center;
        justify-content: center;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function () {
  var el = document.querySelector('.header-text');
  if (el) {
    setTimeout(function() {
      el.classList.add('animated');
    }, 400); // 400ms delay for effect
  }

  // Scroll-triggered animation for .contact-title
  function animateContactTitleOnScroll() {
    var title = document.querySelector('.contact-title');
    if (!title) return;
    var windowHeight = window.innerHeight;
    var position = title.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      title.classList.add('animated');
    }
  }
  window.addEventListener('scroll', animateContactTitleOnScroll);
  animateContactTitleOnScroll(); // Trigger on load

  // Scroll-triggered animation for .contact-description
  function animateContactDescriptionOnScroll() {
    var desc = document.querySelector('.contact-description');
    if (!desc) return;
    var windowHeight = window.innerHeight;
    var position = desc.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      desc.classList.add('animated');
    }
  }
  window.addEventListener('scroll', animateContactDescriptionOnScroll);
  animateContactDescriptionOnScroll(); // Trigger on load

  // Scroll-triggered animation for .contact-methods
  function animateContactMethodsOnScroll() {
    var methods = document.querySelector('.contact-methods');
    if (!methods) return;
    var windowHeight = window.innerHeight;
    var position = methods.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      methods.classList.add('animated');
    }
  }
  window.addEventListener('scroll', animateContactMethodsOnScroll);
  animateContactMethodsOnScroll(); // Trigger on load

  // Scroll-triggered animation for .contact-right
  function animateContactRightOnScroll() {
    var right = document.querySelector('.contact-right');
    if (!right) return;
    var windowHeight = window.innerHeight;
    var position = right.getBoundingClientRect().top;
    if (position < windowHeight - 100) {
      right.classList.add('animated');
    }
  }
  window.addEventListener('scroll', animateContactRightOnScroll);
  animateContactRightOnScroll(); // Trigger on load
});
</script>
<?php get_footer(); ?>
