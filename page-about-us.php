<?php
/**
 * Template Name: About Us Page
 * Description: Custom About Us page template for Krystelis_Custom Theme
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="aboutus-hero-container">
        <div class="aboutus-hero-left">
            <h4 class="aboutus-tagline">Making clinical research crystal clear</h4>
            <div class="mobile-only" style="margin-bottom: 20px;">
       <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-AboutUs-image.png" alt="Teamwork with gears" style="width:100%;border-radius:20px;box-shadow:0 4px 24px rgba(0,0,0,0.08);" />
   </div>
            <h2 class="aboutus-headline">
                You can be confident that <br />
                you will achieve <br />
                your business goals
            </h2>
            <p class="aboutus-description">
                The Krystelis team has decades of experience in successfully delivering services to global pharmaceutical companies. Working with Krystelis you can be confident that you will achieve high-quality outcomes that provide value for money. Our clients value our collaborative and proactive approach that helps them keep pace with rapidly changing regulatory requirements.
            </p>
            <div class="aboutus-icon-row">
                <div class="aboutus-icon-box">
                    <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M66.8947 66.4639C64.6153 64.6142 61.8286 63.8461 59.3722 63.1699C57.7488 62.7222 56.0994 62.2463 54.5043 61.7844C53.8106 61.5842 53.1168 61.3839 52.4207 61.1836C52.4207 61.0752 52.4254 60.9715 52.4278 60.8726C52.4372 60.604 52.4443 60.3519 52.4113 60.1021C52.3122 59.3858 52.5387 58.9429 53.2466 58.4645C54.1409 57.8614 55.0423 57.0814 55.9248 56.146C57.586 54.3859 58.7398 52.1499 59.3604 49.5039C59.3864 49.4992 59.41 49.4968 59.4336 49.4921C59.5185 49.478 59.5917 49.4662 59.6648 49.4638C62.1094 49.3248 63.8626 48.1868 64.8772 46.0756C65.847 44.054 65.6559 42.03 64.3038 40.0555C64.1363 39.8105 64.0325 39.4288 64.0301 39.04C64.0183 36.9241 64.0183 34.7705 64.0183 32.69C64.0183 31.0666 64.0183 29.4431 64.0136 27.8221C64.0018 24.3467 61.9961 21.6229 58.6454 20.532C58.3717 20.4424 58.0603 20.1479 57.9777 19.8982C56.8191 16.4133 54.1763 14.4883 50.533 14.4836C47.0974 14.4765 43.9945 14.4742 40.8396 14.4836C36.8236 14.4977 33.1024 16.0175 30.3605 18.7648C27.6186 21.5122 26.1014 25.2373 26.0919 29.2547C26.0896 30.4799 26.0896 31.7028 26.0919 32.928C26.0919 34.926 26.0919 36.9924 26.0778 39.0258C26.0754 39.417 25.9527 39.834 25.7616 40.1144C24.57 41.858 24.3317 44.0116 25.1245 45.8777C25.9268 47.7603 27.5903 49.0444 29.6927 49.4002C29.957 49.445 30.2237 49.4685 30.4832 49.4921C30.5752 49.5015 30.6673 49.5086 30.7593 49.518C31.6276 53.222 33.48 56.0801 36.2714 58.0145C37.5173 58.8769 37.9066 59.7699 37.6518 61.1789C37.6046 61.1954 37.5574 61.2119 37.5126 61.226C37.3757 61.2731 37.2436 61.3203 37.1091 61.358C36.1275 61.6431 35.1411 61.9187 34.1572 62.1968C32.0736 62.7835 29.9169 63.3914 27.8192 64.0535C21.6983 65.9903 17.5147 71.6027 17.4132 78.0163C17.3731 80.5987 17.3825 83.2259 17.3943 85.7659C17.3967 86.5599 17.4014 87.3563 17.4038 88.1504C17.4061 89.4086 17.9866 89.9953 19.2301 89.9953C28.1519 89.9976 37.069 90 45.9907 90C54.2825 90 62.5742 90 70.866 89.9976C72.1284 89.9976 72.7159 89.4204 72.7183 88.1786C72.7183 87.2456 72.7277 86.3102 72.7348 85.3771C72.7537 82.8843 72.7749 80.3042 72.6876 77.7713C72.5319 73.2497 70.5852 69.4445 66.9065 66.4615L66.8947 66.4639ZM26.1085 79.6775V86.8073H20.5256C20.5256 86.633 20.5256 86.4563 20.5256 86.2819C20.5256 85.8154 20.5232 85.3488 20.5279 84.88C20.5397 84.0459 20.5279 83.1953 20.5161 82.373C20.4902 80.5233 20.4642 78.6125 20.7002 76.7675C21.309 72.0057 24.1452 68.6669 29.1335 66.8409C31.4058 66.0091 32.798 66.3508 34.0935 68.059C34.8816 69.0981 35.8655 70.0099 36.8165 70.8912C37.1586 71.2093 37.5126 71.5368 37.8524 71.8666C38.1803 72.1847 38.5933 72.4109 38.9189 72.5735C39.0581 72.6418 39.2233 72.6725 39.3838 72.7007C39.5631 72.7314 39.7495 72.7644 39.8014 72.8445L39.8274 72.8845C40.1412 73.3746 40.5353 73.9825 40.4928 74.482C40.2285 77.6299 39.8958 80.8344 39.5749 83.9351C39.4758 84.8941 39.3767 85.8554 39.2776 86.8191H29.2538V79.6775H26.1085ZM44.3531 63.184C43.4848 62.7034 42.5575 62.2015 41.5924 61.8103C40.8845 61.5229 40.6816 61.1483 40.8727 60.4508C43.768 61.2425 46.5759 61.2354 49.2211 60.4273C49.4405 61.2425 49.1031 61.57 48.4896 61.8268C47.6 62.2015 46.6703 62.6633 45.7288 63.2029C45.2545 63.4738 44.8675 63.4691 44.3508 63.184H44.3531ZM35.7216 65.0242L36.3327 64.8475C37.1374 64.6119 37.9703 64.3692 38.808 64.143C38.985 64.0959 39.2021 64.1077 39.3366 64.1736C40.1247 64.553 40.9104 64.9535 41.6726 65.34L42.2861 65.651L39.3224 68.6151L35.724 65.0289L35.7216 65.0242ZM43.565 75.5682H46.5311L47.6614 86.8144H42.4418L43.565 75.5682ZM46.5075 72.352H46.5004C46.1701 72.352 45.8138 72.3874 45.4692 72.4203C43.9638 72.5664 42.5409 72.7007 42.2484 70.3987L44.9336 67.7032L47.8124 70.5872C47.6826 70.7686 47.6142 71.0137 47.5387 71.2682C47.3782 71.8266 47.2248 72.3544 46.5051 72.3544L46.5075 72.352ZM54.4477 65.0761L50.9412 68.535L47.9964 65.5968C49.8133 64.0794 51.4202 63.9474 54.45 65.0737L54.4477 65.0761ZM60.9036 79.7057V86.8191H50.828L50.6581 85.2169C50.5236 83.9469 50.3891 82.6746 50.2617 81.3998C50.2145 80.938 50.1673 80.4786 50.1201 80.0191C49.9455 78.3203 49.7638 76.5649 49.6434 74.8355C49.6198 74.5032 49.7449 74.1592 49.8747 73.794C49.936 73.622 49.9997 73.4453 50.0469 73.2686C50.1909 72.7267 50.5307 72.6041 51.1489 72.5546C51.5312 72.524 51.9063 72.2036 52.2556 71.8596C52.9965 71.1339 53.7303 70.3987 54.4642 69.6659C55.5048 68.6269 56.5808 67.5524 57.6591 66.5133C57.8715 66.3083 58.3458 66.1788 58.6148 66.2542C58.8484 66.3178 59.082 66.3814 59.3179 66.4426C60.4647 66.7466 61.6516 67.0623 62.7087 67.5807C67.041 69.6966 69.3417 73.2073 69.5493 78.0163C69.6366 80.0403 69.6154 82.0266 69.5918 84.1283C69.5823 85.0237 69.5729 85.9238 69.5729 86.8333H64.0537V79.7034H60.906L60.9036 79.7057ZM29.655 37.7723C29.5134 37.8195 29.3695 37.8666 29.2255 37.9208C29.2255 37.221 29.2185 36.5353 29.2137 35.8615C29.1996 34.198 29.1854 32.6287 29.2491 31.0477C29.2586 30.8286 29.2656 30.6071 29.2727 30.3856C29.3294 28.7905 29.3883 27.1411 29.8131 25.6308C31.1298 20.9608 35.5352 17.6668 40.5235 17.6221C42.1209 17.6079 43.7255 17.6032 45.2852 17.6032C47.2012 17.6032 49.0559 17.6103 50.7548 17.6197C53.1074 17.6315 54.8842 19.2785 55.0777 21.6253C55.1815 22.874 55.6157 23.3099 56.8686 23.4113C59.0891 23.5927 60.7998 25.2562 60.847 27.2802C60.906 29.7518 60.8942 32.2777 60.88 34.7234C60.8753 35.7389 60.8706 36.7592 60.8682 37.7841L59.8323 37.6475C59.7238 37.4943 59.6247 37.3459 59.5279 37.2045C59.3109 36.8817 59.1056 36.5754 58.8507 36.3162C56.7861 34.2215 54.6647 32.1127 52.6166 30.0723L52.3287 29.7848C51.6892 29.1486 51.1277 28.8824 50.6109 28.9696C50.0847 29.0567 49.6316 29.5068 49.2211 30.3432C47.357 34.1485 44.2658 36.1183 40.0327 36.1937C37.5173 36.2384 34.9547 36.2172 32.4795 36.1937H32.4299C31.4695 36.1843 30.8749 36.533 30.4478 37.36C30.3511 37.5485 30.016 37.6569 29.6597 37.7723H29.655ZM33.6876 42.1431C33.7136 41.2265 33.7395 40.2793 33.7419 39.3321C34.2917 39.3321 34.8485 39.3298 35.4078 39.3274C36.7244 39.3227 38.0836 39.3157 39.4309 39.3369C44.4522 39.417 48.3976 37.3977 51.1512 33.3474C51.1843 33.3003 51.2173 33.2531 51.2621 33.1919L51.2692 33.1824L52.1564 34.066C53.6123 35.5151 54.9904 36.884 56.3826 38.2883C56.4628 38.3708 56.5265 38.5452 56.5241 38.6865C56.51 39.6526 56.5194 40.6398 56.5241 41.5918C56.5407 43.8137 56.5595 46.111 56.3094 48.3352C55.7053 53.705 50.7643 57.9132 45.061 57.9179H45.0563C39.3484 57.9179 34.4073 53.7168 33.8032 48.3517C33.5743 46.3207 33.631 44.292 33.69 42.1455L33.6876 42.1431ZM59.7049 46.2641V40.8354C61.2576 40.9556 62.3524 42.1054 62.3265 43.611C62.3005 45.0766 61.182 46.1981 59.7049 46.2665V46.2641ZM30.3818 40.8283V46.2853C28.8409 46.1157 27.7767 45.0082 27.7767 43.5568C27.7767 42.0795 28.8173 40.9956 30.3818 40.8283ZM8.22715 13.3503C8.67312 13.7885 9.13561 14.2409 9.56506 14.7074C9.78451 14.9454 9.92609 15.3954 9.87654 15.6947C9.5981 17.3299 9.26067 19.0452 8.81234 21.0904C8.6566 21.802 8.61177 22.5371 9.33618 23.0861C9.64057 23.317 9.93553 23.4042 10.221 23.4042C10.6717 23.4042 11.0941 23.1804 11.4764 22.9494C12.2008 22.5088 12.9299 22.0729 13.659 21.6394C14.7232 21.0056 15.8252 20.3482 16.8941 19.6814C17.3589 19.3892 17.6775 19.3916 18.1565 19.6885C19.2868 20.3906 20.4453 21.0833 21.5662 21.7525C22.2882 22.1837 23.0103 22.6149 23.7276 23.0508C24.1877 23.3288 24.8862 23.6422 25.6318 23.1238C26.2359 22.7044 26.4294 22.1059 26.2477 21.2388C25.8701 19.4528 25.4784 17.6079 25.1646 15.7536C25.108 15.4261 25.2543 14.9383 25.4832 14.6886C26.0329 14.0854 26.6252 13.5011 27.1986 12.9356C27.6375 12.502 28.0929 12.052 28.5271 11.5972C28.763 11.3498 28.9377 11.0459 29.0769 10.8032C29.1311 10.709 29.1807 10.6218 29.2303 10.5487L29.2515 10.5157V10.4757C29.1642 9.43896 28.7064 8.87112 27.8027 8.68969C27.2387 8.57659 26.6771 8.46349 26.1155 8.34804C24.898 8.09828 23.6379 7.84145 22.3944 7.61997C21.7573 7.50687 21.4364 7.24062 21.2169 6.64215C20.7946 5.48996 20.325 4.33542 19.8696 3.21858C19.6053 2.57062 19.3434 1.92266 19.0862 1.27235C18.8785 0.742205 18.4916 0.0353431 17.5477 0.0188496C16.559 0.00235621 16.1532 0.742205 15.9337 1.29827C15.627 2.08524 15.3108 2.8675 14.9969 3.65212C14.5793 4.69356 14.1451 5.77035 13.7393 6.83771C13.5646 7.29717 13.3074 7.50452 12.7907 7.60112C11.8964 7.76841 10.9879 7.94984 10.1125 8.12891C9.2182 8.31034 8.29322 8.49648 7.3824 8.66613C6.44091 8.84048 5.94775 9.2434 5.91471 9.86543C5.8864 10.4286 6.1436 11.1425 6.54237 11.602C7.07565 12.2169 7.66556 12.7942 8.23659 13.355L8.22715 13.3503ZM21.847 15.3224C22.0357 16.0764 22.1797 16.861 22.3307 17.6904C22.3873 17.9967 22.4439 18.3124 22.5077 18.6423L19.8295 17.0471C19.6785 16.9576 19.5274 16.8681 19.3741 16.7809C19.0885 16.616 18.7936 16.444 18.5104 16.2649C17.8285 15.8337 17.1725 15.8455 16.441 16.3026C15.5349 16.8681 14.6052 17.4218 13.6213 18.0061C13.2626 18.2182 12.8969 18.4373 12.517 18.6635C12.576 18.3478 12.6279 18.0415 12.6798 17.7446C12.819 16.9364 12.9511 16.1754 13.147 15.4426C13.4042 14.4812 13.1682 13.7296 12.4084 13.0722C11.9389 12.667 11.4693 12.2452 11.0162 11.8376C10.8369 11.675 10.6552 11.5124 10.4759 11.3522L10.5561 11.2203C10.8959 11.1519 11.2357 11.0812 11.5755 11.0106C12.3754 10.8409 13.2036 10.6665 14.0224 10.5511C15.3155 10.3673 16.047 9.77119 16.3891 8.61665C16.5826 7.96633 16.8516 7.34429 17.1607 6.6233C17.274 6.36176 17.3896 6.09315 17.5076 5.80569C17.6327 6.112 17.753 6.40652 17.871 6.68927C18.206 7.50216 18.4963 8.20431 18.7606 8.93238C19.0838 9.82067 19.6289 10.2825 20.5279 10.4286C21.8871 10.6477 23.1424 10.9611 24.4567 11.4111L23.9706 11.8564C23.4822 12.3018 22.9961 12.7471 22.5029 13.1853C21.8375 13.7744 21.6299 14.4553 21.847 15.3224ZM63.2774 13.3126C63.7399 13.7697 64.2189 14.2409 64.6672 14.7239C64.8772 14.9501 65.0094 15.4049 64.9551 15.7159C64.6767 17.344 64.3298 19.1088 63.8909 21.1116C63.7446 21.7855 63.6927 22.5465 64.4407 23.0979C64.7357 23.3147 65.0235 23.3971 65.3043 23.3971C65.7338 23.3971 66.1396 23.1992 66.503 22.9777C67.1826 22.5654 67.8669 22.1578 68.5512 21.7501C69.6602 21.088 70.807 20.4047 71.9207 19.7073C72.4304 19.3869 72.7702 19.3845 73.261 19.6932C74.3889 20.4 75.5522 21.0951 76.6778 21.7666C77.3715 22.1813 78.0629 22.5937 78.7542 23.0131C79.2852 23.3359 79.9907 23.6375 80.7363 23.1002C81.5056 22.5442 81.4395 21.7525 81.3215 21.2059C80.9251 19.3468 80.5428 17.5255 80.2384 15.7206C80.1865 15.4072 80.3423 14.9242 80.5806 14.6627C81.0218 14.1796 81.4985 13.7131 81.961 13.2607C82.5155 12.7141 83.0912 12.151 83.6127 11.5548C83.995 11.1166 84.2428 10.4309 84.2121 9.88428C84.1767 9.2434 83.6906 8.83106 82.7633 8.6567C81.8831 8.48941 80.9912 8.31034 80.1275 8.13598C79.2191 7.9522 78.2799 7.7637 77.355 7.58934C76.8123 7.48802 76.5315 7.25947 76.3474 6.77645C75.9156 5.62898 75.4484 4.47208 75.0001 3.35288C74.7169 2.64838 74.4337 1.94387 74.1553 1.23701C73.8415 0.433542 73.3483 0.0400555 72.605 0H72.5932C71.8476 0.0376993 71.3521 0.431186 71.0359 1.23465C70.748 1.96508 70.4554 2.69314 70.1628 3.42121C69.7216 4.51685 69.2662 5.65018 68.8391 6.77174C68.6503 7.2689 68.3695 7.50216 67.8409 7.59876C66.8876 7.77312 65.9249 7.96869 64.9905 8.15718C64.1528 8.32683 63.2845 8.50119 62.4303 8.66141C61.5053 8.83342 61.0145 9.2434 60.9744 9.87957C60.9414 10.4168 61.1915 11.1189 61.569 11.5478C62.1094 12.1627 62.7016 12.7447 63.2727 13.3079L63.2774 13.3126ZM76.9491 15.4025C77.1402 16.1306 77.2724 16.8563 77.421 17.6998C77.4753 18.0038 77.5319 18.3171 77.5933 18.6447C77.3149 18.4821 77.0435 18.3313 76.7792 18.1852C76.0902 17.8035 75.4389 17.4406 74.8726 16.9977C73.3011 15.7701 71.8665 15.7677 70.3539 16.993C69.8018 17.4406 69.1788 17.7846 68.4568 18.1828C68.1784 18.336 67.8928 18.4939 67.5955 18.6659C67.6569 18.3407 67.7135 18.025 67.7678 17.721C67.9164 16.8964 68.0557 16.1164 68.2468 15.3648C68.4757 14.4671 68.2609 13.7626 67.5649 13.1476C67.0811 12.7188 66.6021 12.2829 66.1231 11.847L65.5686 11.3428L65.6323 11.225C65.9721 11.1566 66.3095 11.0859 66.6493 11.0129C67.4492 10.8433 68.2775 10.6689 69.0963 10.5511C70.3988 10.365 71.1303 9.77119 71.4701 8.62607C71.6635 7.97105 71.9325 7.34901 72.2417 6.63036C72.3526 6.37354 72.4682 6.10493 72.5838 5.82454C72.6971 6.09079 72.808 6.34526 72.9165 6.59266C73.2162 7.27832 73.4993 7.92628 73.6999 8.58366C74.0562 9.76412 74.7806 10.3673 76.0454 10.537C76.8665 10.6477 77.6924 10.8244 78.4923 10.9964C78.8368 11.0695 79.179 11.1425 79.5235 11.2132L79.6438 11.3805L79.0987 11.8611C78.6221 12.2805 78.1455 12.7 77.6617 13.1146C76.9279 13.7437 76.7014 14.4718 76.9491 15.4049V15.4025ZM18.9871 49.4615C19.2962 49.4615 19.5888 49.3578 19.8696 49.1528C20.4312 48.7405 20.6223 48.1467 20.4524 47.3409C20.3038 46.6246 20.1598 45.906 20.0182 45.1897C19.8012 44.0964 19.577 42.9654 19.3292 41.858C19.2183 41.3608 19.3127 41.0475 19.6785 40.6987C20.83 39.596 21.8588 38.5711 22.8191 37.565C23.3359 37.0254 23.5341 36.4034 23.3666 35.8591C23.199 35.3125 22.6823 34.9095 21.9484 34.7517C20.4784 34.436 18.534 34.0283 16.5378 33.6773C15.9432 33.5736 15.6435 33.3191 15.4264 32.7418C14.9851 31.5661 14.5061 30.3833 14.0436 29.2382C13.7817 28.5902 13.5198 27.9399 13.2626 27.2896C12.9511 26.5002 12.4367 26.0997 11.6958 26.0691H11.684C10.9478 26.1185 10.457 26.5167 10.1432 27.3226C9.84822 28.0789 9.54383 28.8329 9.24179 29.5845C8.81234 30.6566 8.36637 31.764 7.94872 32.862C7.76466 33.3497 7.49095 33.5689 6.93643 33.6702C6.05157 33.8328 5.15491 34.0165 4.28892 34.1933C3.4135 34.37 2.50976 34.5561 1.61782 34.7211C1.04443 34.8271 0.350695 35.0509 0.0840569 35.8544C-0.187301 36.6673 0.242152 37.2351 0.747114 37.7299L1.14353 38.1163C2.00952 38.9622 2.90618 39.8364 3.73441 40.7435C3.96565 40.998 4.11667 41.4834 4.06476 41.8062C3.8052 43.373 3.47485 45.0601 3.02416 47.1123C2.85899 47.864 2.81179 48.6321 3.57867 49.1787C4.35263 49.7301 5.06052 49.3861 5.7165 48.9855C6.42203 48.5543 7.13464 48.1326 7.84489 47.7085C8.90201 47.0794 9.99688 46.429 11.0564 45.7622C11.5472 45.4536 11.8869 45.4559 12.3966 45.774C13.5764 46.5092 14.7893 47.2349 15.9644 47.9347C16.6345 48.3352 17.3023 48.7334 17.9701 49.1387C18.3335 49.3578 18.6709 49.4685 18.9918 49.4685L18.9871 49.4615ZM6.88688 43.7854C7.0379 42.9466 7.17948 42.1549 7.37061 41.3891C7.58297 40.5338 7.37768 39.8529 6.72407 39.2473C6.25922 38.8161 5.81797 38.3802 5.30357 37.8737C5.11951 37.6899 4.92603 37.5014 4.72546 37.3035C5.05581 37.2328 5.37436 37.1574 5.68347 37.0843C6.4763 36.8982 7.22667 36.7238 7.97231 36.6508C9.43293 36.5094 10.3013 35.7978 10.7071 34.4053C10.8794 33.8163 11.1177 33.2555 11.3961 32.6052C11.4952 32.3743 11.5967 32.1316 11.7029 31.8771C11.8185 32.1504 11.9318 32.412 12.0403 32.6664C12.34 33.3615 12.6255 34.0165 12.8284 34.6786C13.18 35.8261 13.8832 36.4152 15.1078 36.5872C15.9479 36.705 16.7973 36.8864 17.6185 37.0608C17.9677 37.1338 18.3146 37.2092 18.6638 37.2775L18.737 37.4048L18.1093 37.975C17.6138 38.4274 17.1159 38.8774 16.6204 39.3274C16.0352 39.8576 15.8417 40.5079 16.0328 41.3161C16.2169 42.0983 16.3679 42.9018 16.5283 43.7547C16.585 44.0611 16.644 44.3768 16.7077 44.7019C16.3915 44.5158 16.0824 44.3414 15.7827 44.1694C14.9922 43.7194 14.2466 43.2953 13.5623 42.8028C12.2668 41.8674 11.1389 41.8604 9.90485 42.7863C9.22764 43.2929 8.47255 43.7194 7.67264 44.1718C7.36353 44.3462 7.04262 44.5276 6.71463 44.7208C6.77598 44.398 6.83025 44.0846 6.88688 43.7807V43.7854ZM88.5278 34.7163C87.7633 34.5703 86.987 34.4124 86.2343 34.2592C85.2739 34.0613 84.2781 33.8587 83.2965 33.682C82.6264 33.5618 82.3149 33.312 82.1002 32.7277C81.6873 31.5991 81.2271 30.4657 80.7812 29.3701C80.5098 28.7009 80.2361 28.0294 79.9718 27.3556C79.7524 26.7971 79.3441 26.0573 78.3555 26.0785C77.414 26.0973 77.0293 26.8042 76.8193 27.3343C76.5385 28.0483 76.253 28.7575 75.9651 29.4691C75.5263 30.5529 75.0732 31.6745 74.6556 32.789C74.4503 33.3332 74.1647 33.5712 73.6032 33.6725C72.6522 33.8422 71.6919 34.0401 70.7622 34.2286C69.9504 34.3935 69.1104 34.5655 68.2822 34.7187C67.7112 34.8247 67.0221 35.0509 66.7579 35.8638C66.4959 36.6696 66.949 37.254 67.3714 37.671L67.7819 38.0739C68.6833 38.9599 69.6154 39.8764 70.4884 40.8142C70.6843 41.0239 70.8141 41.4504 70.7645 41.7284C70.4743 43.3872 70.118 45.1803 69.6767 47.2113C69.5399 47.8404 69.4832 48.6321 70.2666 49.1834C70.5522 49.3837 70.8353 49.4615 71.1043 49.4615C71.529 49.4615 71.9231 49.2683 72.2511 49.0704C72.8528 48.7051 73.4592 48.347 74.0657 47.9865C75.2054 47.3126 76.3828 46.6175 77.5084 45.88C78.1478 45.4606 78.6221 45.4701 79.3205 45.913C80.8755 46.8979 82.5981 47.937 84.7453 49.1787C85.2975 49.4992 85.9204 49.5227 86.4112 49.2471C86.9021 48.9714 87.1899 48.446 87.2017 47.8121V47.8027L87.1852 47.6943C87.1711 47.593 87.1522 47.4611 87.1262 47.3315C87.0295 46.8437 86.9351 46.3584 86.8407 45.8706C86.5906 44.5771 86.331 43.2387 86.029 41.9334C85.9016 41.3797 86.0125 41.0263 86.4348 40.6257L86.5127 40.5527C87.4849 39.6361 88.4877 38.6889 89.3702 37.6616C89.7666 37.1998 90.0262 36.4835 89.9979 35.9204C89.9672 35.2936 89.4859 34.9001 88.5302 34.7187L88.5278 34.7163ZM82.7326 41.3868C82.9213 42.1407 83.0653 42.9254 83.2163 43.7547C83.2729 44.0634 83.3296 44.3815 83.3933 44.7114C83.07 44.5205 82.7562 44.3391 82.4494 44.1647C81.6471 43.7029 80.8897 43.2694 80.1889 42.7699C78.9312 41.8721 77.8269 41.8745 76.6093 42.7769C75.9298 43.2812 75.1723 43.7123 74.37 44.1694C74.0586 44.3485 73.7353 44.5323 73.4026 44.7279C73.4616 44.4098 73.5135 44.1035 73.5654 43.8066C73.7046 42.9984 73.8368 42.235 74.0326 41.5046C74.2851 40.5574 74.0515 39.8057 73.2964 39.1342C72.8434 38.7313 72.4139 38.3237 71.9184 37.8477C71.7367 37.6757 71.5503 37.4967 71.3521 37.3082C71.7603 37.2257 72.1496 37.1432 72.5272 37.0631C73.5111 36.8558 74.4385 36.6626 75.354 36.5047C76.2601 36.3492 76.8241 35.8756 77.1332 35.0156C77.3951 34.2828 77.6877 33.5806 78.0251 32.7654C78.1431 32.4803 78.2658 32.1858 78.3909 31.8771C78.5065 32.1622 78.6197 32.4332 78.7306 32.6971C79.0398 33.4346 79.33 34.132 79.5707 34.8318C79.9223 35.8567 80.5476 36.3845 81.5952 36.5377C82.4659 36.6673 83.3437 36.8558 84.2711 37.0537C84.6321 37.1315 85.0025 37.2116 85.3919 37.2917C85.1842 37.4943 84.9908 37.6875 84.802 37.876C84.2899 38.3826 83.8511 38.8185 83.3862 39.2473C82.7302 39.8552 82.5226 40.5338 82.7373 41.3868H82.7326Z" fill="#0072DA"></path></svg>
                    <div class="aboutus-icon-label">Experience</div>
                </div>
                <div class="aboutus-icon-box">
                    <svg xmlns="http://www.w3.org/2000/svg" width="92" height="90" viewBox="0 0 92 90" fill="none"><path d="M69.8349 24.1655C69.1748 23.506 69.2411 22.6125 70.0053 21.8348C71.704 20.1068 73.4547 18.3694 75.1463 16.6888L75.4965 16.3413C75.9176 15.9229 76.3245 15.7172 76.7456 15.7172C76.9562 15.7172 77.1715 15.7692 77.3915 15.8709C77.9735 16.1427 78.2456 16.6202 78.2905 17.4617C78.2527 17.5279 78.2148 17.6059 78.1722 17.6887C78.0421 17.9439 77.8812 18.2631 77.6541 18.4947C76.0075 20.1872 74.2118 21.9837 72.1677 23.9905C71.3869 24.7564 70.4926 24.825 69.8349 24.1655ZM46.0485 17.5303H46.0532C46.9759 17.5303 47.5532 16.814 47.5626 15.6605C47.5721 14.4006 47.5697 13.1194 47.5674 11.8808C47.5674 11.3489 47.5674 10.8147 47.5674 10.2829C47.5674 9.75101 47.5674 9.1837 47.5674 8.63294C47.5674 7.38249 47.5721 6.08712 47.5626 4.8154C47.5532 3.6855 47.0161 3.02364 46.0887 3C46.0745 3 46.0603 3 46.0438 3C45.6416 3 45.2914 3.13474 45.0264 3.39003C44.6929 3.71387 44.513 4.20554 44.5107 4.81303C44.4918 8.18381 44.4918 11.7319 44.5107 15.6582C44.5154 16.7928 45.1187 17.5279 46.0485 17.5303ZM14.5351 18.5822C16.5816 20.6363 18.2472 22.3004 19.7803 23.8204C19.9766 24.0165 20.2156 24.2364 20.4829 24.3829C20.9395 24.6311 21.4482 24.6264 21.8764 24.3688C22.3733 24.0686 22.669 23.48 22.6784 22.7803C22.643 22.7235 22.6051 22.655 22.5625 22.5794C22.4158 22.3217 22.2171 21.9695 21.9545 21.7C20.5847 20.2912 19.1675 18.8824 17.7953 17.5208L16.7212 16.4524C16.5745 16.3058 16.3758 16.1167 16.1534 15.9678C15.8884 15.7905 15.5903 15.7031 15.2969 15.7031C14.9184 15.7031 14.5493 15.8473 14.2631 16.1262C13.7568 16.6249 13.6763 17.3979 14.0643 18.0101C14.1803 18.1898 14.334 18.3765 14.5351 18.5798V18.5822ZM80.5831 45.1631C81.8914 47.1865 81.7755 48.8081 80.1951 50.5809C68.3563 63.8702 58.5143 74.9068 49.2211 85.317C48.2369 86.4185 47.1226 87 45.9964 87C44.8703 87 43.7749 86.4256 42.8049 85.3382C31.4298 72.5974 20.2771 60.081 11.8333 50.5998C10.2387 48.8081 10.1133 47.1889 11.4146 45.1844C12.5052 43.5061 13.5982 41.8278 14.6913 40.1495C16.681 37.0978 18.7369 33.9445 20.7384 30.8267C21.7155 29.3044 23.0215 28.5929 24.8432 28.5929H24.8787C30.0126 28.6189 35.2317 28.6141 40.2805 28.6094C44.1416 28.607 48.0051 28.607 51.8662 28.6094C56.8652 28.6141 62.0323 28.6189 67.1165 28.5905C67.1307 28.5905 67.1426 28.5905 67.1568 28.5905C69.0116 28.5905 70.3365 29.3138 71.3278 30.8621C73.3411 34.0083 75.4089 37.1924 77.4081 40.27C78.4656 41.8987 79.5255 43.5297 80.5807 45.1607L80.5831 45.1631ZM48.7953 31.7367L57.4638 45.1229L66.13 31.7367H48.7953ZM46.0035 33.0628L37.3752 46.3923H54.639L46.0035 33.0628ZM25.9173 31.7391L34.5693 45.0922L43.2094 31.7391H25.9173ZM14.2796 46.397H31.7657L23.0404 32.9257L14.2796 46.397ZM42.8002 79.9228L33.429 49.5338H14.9775L42.3033 80.1852L42.8002 79.9228ZM55.3747 49.5338H36.6347L45.7433 79.0624L46.2969 78.9725L55.3724 49.5338H55.3747ZM77.0414 49.5196H58.5805L49.214 79.9015L49.7109 80.1781L77.0414 49.5172V49.5196ZM77.7109 46.3616L69.0092 32.9304L60.3147 46.3616H77.7109ZM90.9976 47.872C90.9527 47.0801 90.3257 46.4939 89.4764 46.4419C89.0813 46.4183 88.672 46.4254 88.2769 46.4301C88.09 46.4325 87.9031 46.4396 87.7162 46.4348C87.5269 46.4348 87.3376 46.4325 87.1484 46.4301C86.9638 46.4277 86.7769 46.4254 86.59 46.4254C86.3581 46.4254 86.1263 46.4301 85.8968 46.4419C85.0427 46.4916 84.4725 47.0092 84.4039 47.7893C84.3637 48.2455 84.4773 48.6544 84.721 48.9428C84.941 49.2028 85.2604 49.3565 85.646 49.3872C86.9425 49.4936 88.3171 49.4936 89.7295 49.3872C90.5765 49.3258 91.0402 48.567 91 47.8697L90.9976 47.872ZM6.0322 46.4372C5.62054 46.423 5.19942 46.4277 4.79249 46.4325C4.27436 46.4396 3.73968 46.4467 3.21209 46.4136C2.11906 46.345 1.43769 46.683 1 47.508V48.2833C1.35488 49.0563 1.81859 49.4274 2.45501 49.4487C3.73021 49.4912 4.97702 49.5243 6.20964 49.4369C7.10631 49.373 7.65992 48.7561 7.6197 47.8649C7.58184 47.0187 6.97382 46.4703 6.03457 46.4372H6.0322ZM46.0509 20.4827C45.2512 20.4827 44.5414 21.1705 44.5296 21.9648C44.5154 22.7543 45.2299 23.4871 46.0272 23.5012C46.4081 23.5131 46.7795 23.357 47.0729 23.0758C47.3686 22.7897 47.5414 22.4021 47.5461 22.0097C47.5579 21.18 46.8955 20.4945 46.0698 20.4827C46.0627 20.4827 46.0556 20.4827 46.0485 20.4827H46.0509Z" fill="#0072DA"></path></svg>
                    <div class="aboutus-icon-label">Value</div>
                </div>
                <div class="aboutus-icon-box">
                    <svg xmlns="http://www.w3.org/2000/svg" width="92" height="90" viewBox="0 0 92 90" fill="none"><path d="M75.1349 4.66197V1.52347C75.1349 0.683099 75.8203 0 76.6635 0C77.5067 0 78.192 0.683099 78.192 1.52347V4.66197C78.192 5.50235 77.5067 6.18545 76.6635 6.18545C75.8203 6.18545 75.1349 5.50235 75.1349 4.66197ZM68.5589 7.56338C68.7497 7.92958 69.0724 8.19953 69.4681 8.31925C69.6165 8.3662 69.7696 8.38732 69.9203 8.38732C70.1699 8.38732 70.4196 8.32629 70.6457 8.20188L70.7187 8.16197C71.4465 7.76995 71.7291 6.85446 71.3499 6.12207L69.8661 3.26291C69.4799 2.51878 68.5589 2.22535 67.8123 2.60563L67.7369 2.64319C67.3719 2.82864 67.1034 3.14554 66.9785 3.53286C66.8537 3.92254 66.889 4.33568 67.0751 4.69718L68.5589 7.55869V7.56338ZM87.4766 50.3169C87.4836 50.3474 87.493 50.3732 87.5025 50.3897C88.4399 52.2512 88.494 54.1972 87.6791 56.6972C87.2528 58.007 86.9019 59.3732 86.5627 60.6948C86.2895 61.7606 86.0045 62.8639 85.6818 63.9413C85.3945 64.8944 85.02 65.7911 84.6549 66.6573C84.5277 66.9601 84.4029 67.2559 84.2875 67.5423C84.337 67.7512 84.3935 67.9601 84.4477 68.162C84.6007 68.73 84.7562 69.3169 84.7868 69.9178C84.8433 71.0892 84.8316 72.2629 84.8198 73.3991C84.8174 73.7324 84.8127 74.061 84.8104 74.3873C86.8288 74.5 87.1185 74.8662 87.1492 76.9812C87.1774 79.0282 87.2081 80.9906 87.2363 82.9624C87.2599 84.5634 87.2858 86.1714 87.3093 87.8333V87.9883C87.314 88.385 87.1609 88.7582 86.8807 89.0399C86.6004 89.3216 86.2283 89.4742 85.8326 89.4742H85.5382C84.7232 89.4742 84.059 88.8122 84.059 88V77.6854H57.2866V87.9953C57.2866 88.8075 56.6247 89.4695 55.8074 89.4695H55.6873C54.8936 89.4695 54.2388 88.8263 54.2271 88.0376L54.1799 85.0258C54.1375 82.3286 54.0975 79.6995 54.0575 76.9225C54.0316 75.0798 54.3519 74.7066 56.1749 74.6056C56.1749 74.2629 56.1772 73.9225 56.1796 73.5798C56.1843 72.4319 56.1914 71.2465 56.1631 70.0892C56.1442 69.2653 56.2243 68.2723 56.9521 67.608L55.3929 63.6901L53.9185 64.6268C53.8007 64.7019 53.6853 64.7817 53.5676 64.8615C53.2896 65.054 52.9999 65.2535 52.6679 65.4014C50.8284 66.23 48.9889 67.0516 47.1471 67.8756C45.1686 68.7582 43.1902 69.6432 41.2165 70.5329C40.4934 70.8592 39.5513 71.1268 38.6869 70.284C37.8272 69.4484 38.0604 68.5023 38.3595 67.7723C39.1697 65.7981 38.8659 64.0117 37.4551 62.4648C36.1032 60.9789 34.3626 60.4883 32.4219 61.047C30.759 61.5258 29.4424 62.8498 28.9855 64.5047C28.6722 65.6409 28.5568 67.277 29.7816 68.6127C30.5753 69.4765 31.5386 70.2793 32.3112 70.9249C33.0248 71.5211 33.3781 72.284 33.2768 73.0188C33.1803 73.7254 32.6904 74.3122 31.899 74.669C28.677 76.1268 23.0997 78.5704 20.0613 79.8991C18.4126 80.6197 16.4672 79.8732 15.73 78.2371C13.4642 73.2136 5.76007 56.1174 1.70192 47.0751C0.189832 43.7066 1.08955 41.3146 4.45054 39.7653C4.45996 39.7606 4.47173 39.7559 4.48115 39.7512C3.71333 37.9319 4.1467 36.1432 4.62247 34.5915C5.01345 33.3192 5.35261 32.0023 5.68235 30.7277C5.98382 29.5587 6.29708 28.3474 6.65037 27.1667C6.93536 26.2113 7.31456 25.3146 7.68198 24.4484C7.81152 24.1432 7.93635 23.8474 8.05176 23.5587C7.99994 23.3286 7.94342 23.1009 7.88925 22.8779C7.73851 22.2723 7.58306 21.6455 7.54773 21.0094C7.48885 19.8803 7.50062 18.7512 7.5124 17.6596C7.51711 17.331 7.51947 17.007 7.52182 16.6854C5.4374 16.5305 5.21365 16.2113 5.18774 13.9577C5.1689 12.3075 5.14063 10.0563 5.11237 7.85916C5.08175 5.42723 5.05113 3.06338 5.037 1.66197C5.03229 1.23709 5.1948 0.835681 5.49628 0.532864C5.80482 0.225352 6.20758 0.0586854 6.63624 0.0586854H6.67157C7.54773 0.0586854 8.26138 0.769953 8.26138 1.64319V13.3756H35.0598V1.62207C35.0598 0.748826 35.7734 0.0375587 36.6496 0.0375587C37.5258 0.0375587 38.237 0.746479 38.2394 1.61737C38.2441 4.19484 38.2582 10.6174 38.3077 14.6197C38.3218 15.8146 37.7731 16.3732 36.5224 16.4343C36.4023 16.4413 36.2822 16.4437 36.162 16.4413C36.162 16.7676 36.1597 17.0916 36.1573 17.4155C36.1503 18.5939 36.1456 19.7066 36.1738 20.838C36.1997 21.8192 36.1008 22.8052 35.3047 23.4953L36.3693 25.5704L49.752 20.0117C46.3157 16.7371 45.4984 13.4178 47.2484 9.88498C48.8688 6.61502 52.6019 4.78639 56.123 5.53286C58.3771 6.01174 60.1506 7.22535 61.3942 9.1385C62.5224 10.8732 62.6024 12.7911 62.5506 14.6854L73.0222 10.3333C74.6968 9.6385 76.6305 10.4272 77.33 12.0939C80.0386 18.5282 86.7252 34.4225 90.3264 43.0235C91.7844 46.507 90.9082 48.7559 87.4813 50.3122L87.4766 50.3169ZM10.6803 16.7934C10.6449 18.0751 10.6473 19.3662 10.6496 20.615C10.6496 20.7864 10.6496 20.9554 10.6496 21.1244H32.9824C32.9824 20.8075 32.9777 20.4953 32.9753 20.1854C32.9659 19.0563 32.9589 17.9883 33.0013 16.9155C33.013 16.608 32.9683 16.5329 32.9659 16.5305C32.9683 16.5352 32.8976 16.4953 32.6032 16.4953C32.5867 16.4953 32.5679 16.4953 32.549 16.4953C29.2681 16.5469 25.9354 16.5681 22.7087 16.5916C21.5051 16.5986 20.2992 16.608 19.0957 16.6174L17.6048 16.6291C15.4144 16.6455 13.1509 16.662 10.9252 16.7042C10.8734 16.7066 10.7603 16.7488 10.6755 16.7934H10.6803ZM81.2421 66.1737C81.7132 65.4202 82.1583 64.7113 82.3868 63.9484C83.2135 61.1995 83.9483 58.3709 84.6573 55.6315C84.7986 55.0869 84.9399 54.5423 85.0812 53.9977C85.159 53.6972 85.1519 53.3826 85.0577 53.0869C83.8306 49.2136 82.5611 45.284 81.334 41.4836L80.8653 40.0329C80.7546 39.6925 80.6203 39.4695 80.4837 39.4085C80.3424 39.3451 80.1069 39.4178 79.9373 39.4906C79.4474 39.6995 78.9622 40.1103 78.7031 40.5329C78.2156 41.3286 78.0649 42.3216 78.2533 43.4836L78.2933 43.73C78.7361 46.4531 79.193 49.2676 79.6806 52.0282C79.8219 52.8239 79.5699 53.2934 78.8397 53.5939C77.3959 54.1901 76.1594 55.3521 75.3633 56.8685C75.1867 57.2042 75.0077 57.5399 74.824 57.8803C74.4283 58.615 73.5286 58.9085 72.7749 58.5516L72.756 58.5423C72.0919 58.2277 71.7527 57.507 71.894 56.8193L71.1592 57.1244C67.061 58.8216 62.8262 60.5751 58.6738 62.3333C58.6126 62.3991 58.5301 62.5798 58.5301 62.6573C58.728 64.2183 59.3451 65.5704 60.3626 66.6643C60.4733 66.784 60.7182 66.8967 60.8689 66.8967C66.7218 66.9178 72.9822 66.9178 80.5709 66.9038C80.6604 66.9038 80.7687 66.8756 80.8229 66.8498C80.9571 66.6244 81.0985 66.3991 81.2398 66.1714L81.2421 66.1737ZM41.8712 66.7981C42.0832 66.7042 42.2858 66.608 42.5613 66.4671C45.1145 65.1643 47.6864 64.0117 50.2089 63.0376C51.0333 62.7207 51.8482 62.3826 51.9872 62.0446C52.0696 61.8451 51.9825 61.4742 51.7116 60.8709L50.7601 58.7512C49.5447 56.0423 48.2894 53.2418 47.0434 50.4906C46.6901 49.7113 46.4193 48.7911 47.0835 47.8944C47.8136 46.9061 48.9489 47.0258 49.5589 47.0916C50.2796 47.169 50.9108 47.1901 51.4855 47.1526C52.5383 47.0869 53.6359 46.9202 54.5191 46.1596C56.0736 44.8193 56.6459 42.7371 55.977 40.8545C55.2963 38.9366 53.4734 37.6315 51.4408 37.608C49.3869 37.5775 47.4391 38.8779 46.709 40.7559C46.4546 41.4108 46.0283 42.5047 44.7117 42.561C43.3598 42.615 42.877 41.5211 42.5613 40.7958C41.0705 37.3709 39.4288 33.6174 37.5422 29.3263C37.3114 28.7981 37.2078 28.7042 37.1748 28.6854C37.1418 28.6761 37.0076 28.6808 36.5083 28.9038C31.1335 31.3005 25.3678 33.8732 19.6044 36.4624C19.3807 36.5634 19.1616 36.7559 18.919 36.9765L18.9002 36.993C18.0028 37.8146 17.0748 38.662 16.201 39.5305C16.0856 39.6455 15.9961 39.8474 15.9985 39.9202C16.307 42.061 16.5991 43.838 16.9147 45.507C17.3127 47.608 17.5388 50.0751 16.1987 52.4225C15.2707 54.0493 13.6879 54.8451 11.6271 54.7254C10.0396 54.6338 8.95383 53.6502 8.39799 51.8052C7.84685 49.9765 7.24861 48.1244 6.66685 46.3333C6.45723 45.6854 6.24761 45.0399 6.03799 44.392C5.88255 43.9061 5.71768 43.4225 5.55516 42.939L5.50099 42.777C4.84858 43.0423 4.42934 43.3756 4.25034 43.7676C4.04307 44.2254 4.11373 44.831 4.46938 45.6221C8.43096 54.446 16.1068 71.439 18.3655 76.4366C18.5422 76.8286 19.0085 77.007 19.4042 76.8286L29.1763 72.4577C25.4361 68.6972 24.6848 65.5211 26.6514 61.939C28.4085 58.7371 32.1251 57.0986 35.6863 57.9531C37.4716 58.3803 38.946 59.3028 40.0718 60.6925C41.5085 62.4648 42.0997 64.4718 41.8712 66.8005V66.7981ZM9.95954 27.0916C9.11164 29.9577 8.34617 32.9014 7.60661 35.7512L7.26981 37.0423C7.19679 37.3239 7.2015 37.669 7.28158 37.9225C8.50397 41.784 9.76876 45.7042 10.9935 49.4953L11.481 51.0047C11.693 51.6596 11.8838 51.7535 12.5551 51.5376C13.3747 51.2746 13.7091 50.6315 13.9871 49.6737C14.3121 48.5563 14.1025 47.446 13.8834 46.27C13.8246 45.9577 13.7633 45.6315 13.7139 45.3099C13.4618 43.6784 13.1863 42.0141 12.9201 40.4061C12.8047 39.7066 12.687 39.0047 12.5715 38.3005L12.5032 37.8803L12.9013 37.723C14.4793 37.1033 15.6876 36.1714 16.592 34.8756C16.9029 34.4296 17.3669 34.0376 17.8332 33.8263C20.6313 32.5587 23.4836 31.2911 26.2416 30.0657C27.3109 29.5892 28.3825 29.115 29.4518 28.6385C30.4387 28.1995 31.4209 27.7559 32.4737 27.2817L33.5147 26.8122C33.4511 26.7066 33.3875 26.6033 33.3239 26.4977C32.8717 25.7512 32.4454 25.0423 31.9861 24.3615C31.9272 24.3286 31.7718 24.2911 31.6493 24.2864C25.25 24.3239 18.5045 24.3803 11.6035 24.4507C11.514 24.4507 11.4057 24.4789 11.3515 24.5047C11.2385 24.6948 11.1183 24.8826 11.0006 25.0728C10.5696 25.7582 10.1644 26.4038 9.95954 27.0939V27.0916ZM50.5858 50.662L54.9242 60.5023C54.9525 60.4906 54.9831 60.4812 55.0114 60.4695L57.1688 59.5704C63.0193 57.1338 69.0677 54.615 75.0124 52.1268C75.4811 51.9296 75.8674 51.7066 76.1571 51.4648C76.2419 51.3944 76.3314 51.1925 76.329 51.115C76.077 49.338 75.7755 47.4836 75.4364 45.608C75.0878 43.6831 74.8004 41.4484 75.792 39.2817C76.5056 37.723 77.7469 36.7465 79.478 36.3803C81.5059 35.9531 83.1711 36.9343 83.8212 38.946C84.6903 41.6385 85.5617 44.331 86.4355 47.0211C86.4638 47.108 86.4944 47.1948 86.5297 47.284C87.1633 47.0329 87.5613 46.7254 87.7403 46.3474C87.9453 45.9131 87.8864 45.3239 87.559 44.5446C85.3615 39.3005 83.1593 34.0563 80.9595 28.8146L77.6009 20.8122C76.866 19.0587 76.1312 17.3052 75.3869 15.5282L74.6968 13.8803C74.6167 13.6878 74.4636 13.5352 74.2681 13.4554C74.0726 13.3756 73.8583 13.3756 73.6628 13.4554L71.2628 14.4577C68.2104 15.7324 65.3275 16.9343 62.3763 18.1549C61.5355 18.5023 60.7064 18.4437 60.0988 17.9953C59.4793 17.5352 59.1755 16.7347 59.2697 15.8005C59.3639 14.8779 59.3992 14.1197 59.3875 13.4131C59.3592 11.8662 58.6314 10.4413 57.3902 9.50704C56.1984 8.60798 54.6957 8.30751 53.2708 8.68075C51.177 9.23005 49.6743 11.0305 49.5353 13.1573C49.3893 15.3685 50.647 17.3239 52.7338 18.1385C53.3226 18.3685 54.4202 18.7981 54.4461 20.1103C54.4744 21.5282 53.2967 21.9859 52.7314 22.2066C52.039 22.4765 51.3371 22.7653 50.6612 23.0446L50.3832 23.1596C47.8607 24.1972 45.3406 25.2347 42.8251 26.2723L40.1307 27.3826L44.7635 37.6948C47.4603 34.4085 50.8519 33.3568 54.3896 34.7394C57.866 36.0962 59.9739 39.5986 59.5147 43.2512C59.265 45.2418 58.4713 46.9437 57.157 48.3122C55.2539 50.2934 52.8327 50.6714 50.5905 50.6596L50.5858 50.662ZM59.3663 74.2793C59.3639 74.4178 59.3757 74.4883 59.3828 74.5211C59.4157 74.5305 59.4887 74.5423 59.6371 74.5423C67.0304 74.4718 74.3977 74.4155 81.5342 74.3756C81.6307 74.3756 81.6896 74.3662 81.7202 74.3592C81.7273 74.3239 81.7344 74.2559 81.7297 74.1315C81.692 73.0751 81.6449 71.7629 81.6967 70.4977C81.7108 70.1596 81.6567 70.0845 81.6567 70.0845C81.6567 70.0845 81.586 70.0258 81.1974 70.0258C75.6789 70.054 70.0687 70.0563 64.6445 70.0563H59.3757C59.3757 70.3568 59.3757 70.6502 59.378 70.9437C59.3828 72.0939 59.3875 73.1784 59.3616 74.2793H59.3663ZM82.7731 8.24413L82.8461 8.28404C83.0675 8.40141 83.3101 8.46009 83.5527 8.46009C83.7081 8.46009 83.8636 8.43662 84.0143 8.38967C84.4053 8.26526 84.7256 7.99765 84.914 7.6338L86.3978 4.76291C86.5839 4.40141 86.6192 3.98592 86.4944 3.59859C86.3696 3.21127 86.0987 2.89437 85.736 2.70892L85.663 2.67136C84.9164 2.29108 83.9954 2.58685 83.6092 3.33099L82.1254 6.19953C81.7414 6.94131 82.0335 7.85916 82.7731 8.24648V8.24413ZM17.56 83.6761C16.7192 83.6761 16.0362 84.3568 16.0362 85.1948V88.4812C16.0362 88.8897 16.1963 89.2723 16.4884 89.561C16.7757 89.8451 17.1549 90 17.56 90H17.5765C18.4079 89.993 19.0863 89.3099 19.0863 88.4812V85.1948C19.0863 84.3568 18.4032 83.6761 17.5624 83.6761H17.56ZM25.7776 81.7113C25.5892 81.3498 25.2712 81.0822 24.8826 80.9601C24.494 80.838 24.0794 80.8756 23.7167 81.0634L23.639 81.1033C22.8995 81.4883 22.6098 82.4038 22.9937 83.1432L24.4798 86.0188C24.6683 86.3826 24.9862 86.6502 25.3772 86.7723C25.5279 86.8192 25.6834 86.8427 25.8365 86.8427C26.0814 86.8427 26.324 86.784 26.5478 86.6643L26.6279 86.6221C27.3651 86.2347 27.6524 85.3193 27.2685 84.5822L25.7776 81.7089V81.7113ZM11.5776 81.1807L11.4975 81.1385C11.1372 80.9507 10.7226 80.9155 10.334 81.0376C9.94541 81.1596 9.62744 81.4272 9.43902 81.7887L7.94813 84.6667C7.76206 85.0258 7.72909 85.439 7.85392 85.8239C7.97875 86.2113 8.2496 86.5282 8.61232 86.7113L8.69475 86.7535C8.91379 86.8639 9.14932 86.9178 9.38249 86.9178C9.93599 86.9178 10.4706 86.6174 10.7415 86.0962L12.23 83.2207C12.6139 82.4789 12.3219 81.5634 11.58 81.1784L11.5776 81.1807Z" fill="#0072DA"></path></svg>
                    <div class="aboutus-icon-label">Collaboration</div>
                </div>
            </div>
        </div>
        <div class="aboutus-hero-right desktop-only">
       <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-AboutUs-image.png" alt="Teamwork with gears" />
   </div>
    </div>
</main>

<!-- Mission Statement Section -->
<section class="mission-section">
    <div class="mission-container">
        <h2 class="mission-title">Mission statement</h2>
        <p class="mission-desc">
            To help our customers improve the lives of patients by providing services that are<br />
            recognised for quality, value, and collaboration
        </p>
        <div class="mission-values-title">OUR COMPANY VALUES</div>
        <div class="mission-values-row">
            <div class="mission-value-box">
                <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M84.2393 45.604C78.2873 43.7563 72.3489 41.8677 66.3878 40.0495C65.736 39.852 64.9213 39.8543 64.2695 40.0518C58.9534 41.6679 53.66 43.3522 48.3598 45.0138C48.1199 45.0887 47.8755 45.1432 47.6039 45.2158C47.3912 44.41 47.536 43.8607 48.2466 43.3273C50.8899 41.3411 52.7593 38.7488 53.8411 35.6141C54.0515 35.0034 54.3231 34.8128 54.9704 34.8105C58.6208 34.7923 61.4338 32.3567 61.9724 28.8179C62.5767 24.8524 59.5622 21.1251 55.5656 20.8959C55.346 20.8823 55.1288 20.8618 54.821 20.8369C54.821 19.8426 54.8255 18.887 54.821 17.9314C54.7667 9.95258 49.5683 3.34256 41.8533 1.44944C41.1472 1.27693 40.4298 1.14981 39.7169 1H36.3268C35.3966 1.20656 34.4529 1.37 33.5341 1.62423C26.3011 3.63538 21.3064 10.1614 21.225 17.6862C21.2136 18.7258 21.225 19.7655 21.225 20.7688C20.4012 20.9118 19.6657 20.9708 18.9709 21.1728C15.7459 22.1103 13.5869 25.3722 14.026 28.5818C14.5239 32.2183 17.3075 34.7628 20.9262 34.8037C21.6572 34.8128 22.0012 34.9853 22.2434 35.7139C23.0128 38.0338 24.3254 40.0381 26.0318 41.7837C26.8217 42.5918 27.9487 43.266 28.3221 44.233C28.7204 45.268 28.4126 46.5801 28.4126 47.7695C28.4126 47.7967 28.4081 47.8262 28.4126 47.8535C28.4805 48.3483 28.2565 48.5345 27.7699 48.6298C23.1418 49.5241 20.8267 50.2891 18.7106 51.3446C13.5733 53.9051 4.01167 60.0611 4.00261 68.9933C4.00035 72.3936 3.99809 75.7962 4.00261 79.1966C4.00261 80.6493 4.61818 81.2667 6.06432 81.269C9.08785 81.2736 12.1114 81.269 15.1349 81.269C16.8866 81.269 27.1045 81.2758 32.828 81.269C32.8913 81.269 32.9547 81.269 33.0181 81.269C33.9595 81.269 34.7697 81.269 35.3898 81.2667C40.7399 81.2667 46.0921 81.2622 51.4422 81.2872C51.8382 81.2872 52.3225 81.4824 52.6167 81.7502C55.9163 84.7829 59.7365 86.819 64.0885 87.8382C64.7018 87.9812 65.3965 88.0697 65.9963 87.929C75.6711 85.659 82.0146 79.7368 84.9906 70.244C85.4931 68.6414 85.6719 66.9367 86 65.282V46.9137C85.6198 46.1942 84.9974 45.8355 84.237 45.5994L84.2393 45.604ZM54.8617 24.3008C56.3531 24.2463 57.4507 24.8319 58.1659 26.0509C58.9037 27.3084 58.8584 28.5977 58.055 29.8189C57.3149 30.9448 56.2354 31.4215 54.8617 31.426V24.3008ZM21.1797 31.4034C19.8331 31.4601 18.7853 30.9698 18.0385 29.9075C17.2011 28.718 17.1106 27.4242 17.8167 26.1439C18.5183 24.8728 19.6227 24.2531 21.1797 24.3008V31.4011V31.4034ZM25.0768 14.304C26.8013 8.1775 32.355 4.14384 38.7415 4.41623C44.7705 4.67501 50.0073 9.25798 51.2 15.3051C51.3064 15.843 51.2272 16.2562 50.8334 16.6874C47.5903 20.2467 43.5665 22.108 38.7777 22.1829C34.2289 22.2533 29.68 22.2011 25.1311 22.2011C24.9953 22.2011 24.8573 22.1784 24.597 22.158C24.7034 19.4772 24.3549 16.869 25.0768 14.304ZM24.9116 32.6654C24.4771 30.4159 24.6174 28.0552 24.4861 25.606C25.0813 25.606 25.4434 25.606 25.8055 25.606C30.4924 25.5833 35.1816 25.6491 39.864 25.5129C43.9014 25.3949 47.4975 23.8944 50.7021 21.4384C50.8809 21.3022 51.0642 21.1705 51.2475 21.0389C51.2634 21.0275 51.2973 21.0457 51.3878 21.0639C51.3992 21.2886 51.424 21.5292 51.424 21.7721C51.4263 24.4914 51.4376 27.2108 51.424 29.9279C51.3833 36.8239 46.2415 42.6939 39.6875 43.3454C32.5066 44.0582 26.2627 39.6409 24.9116 32.6632V32.6654ZM44.1051 50.3413C44.1006 50.5343 43.863 50.7658 43.6774 50.8997C40.4321 53.2673 35.3921 53.2332 32.2396 50.8112C32.0268 50.6478 31.8232 50.3254 31.8141 50.0689C31.7734 48.6548 31.7937 47.2383 31.7937 45.6789C35.9534 47.2202 39.9862 47.2315 44.121 45.7175C44.121 47.3132 44.1368 48.8273 44.1006 50.3413H44.1051ZM48.138 77.8732C43.3017 77.8732 38.4654 77.8732 33.6314 77.8732C33.5182 77.8732 33.4051 77.8732 33.2874 77.8732C33.095 77.8732 32.9027 77.8664 32.7103 77.8732C25.509 77.8709 12.0752 77.8732 8.36591 77.8732H7.39503C7.39503 74.6681 7.3724 71.556 7.39956 68.444C7.45614 61.9679 14.5555 56.9264 18.421 54.9107C20.5936 53.778 23.0671 53.0039 27.8061 52.0937C28.1162 52.0347 28.5733 52.214 28.8223 52.4387C32.2622 55.5077 36.2317 56.6858 40.7466 55.7574C42.0525 55.4895 43.304 54.947 44.6279 54.5134C44.6279 57.4167 44.6211 60.3562 44.6279 63.2958C44.6415 68.3305 46.0333 72.9543 48.7739 77.1695C48.9029 77.3693 49.0116 77.5827 49.1768 77.8709H48.138V77.8732ZM82.6053 63.3049C82.5601 73.3379 75.9404 81.8932 66.2543 84.3992C65.6613 84.5536 64.9598 84.5513 64.3668 84.3992C54.6829 81.8932 48.0543 73.3243 48.0203 63.3026C48.0045 58.4608 48.0181 53.6168 48.0181 48.6797C52.4583 47.2883 56.842 45.915 61.2279 44.5417C62.3051 44.2034 63.3688 43.813 64.4619 43.5497C65.0028 43.4203 65.6432 43.384 66.166 43.5452C71.4527 45.1614 76.7235 46.8252 81.9965 48.4777C82.1821 48.5367 82.3632 48.6139 82.6076 48.7047C82.6076 49.4583 82.6076 50.221 82.6076 50.9814C82.6076 55.09 82.6234 59.1963 82.6053 63.3049ZM77.7939 50.734C73.8651 49.4946 69.9341 48.2598 65.994 47.0635C65.5663 46.9342 65.0299 46.9432 64.5999 47.0726C60.6847 48.2621 56.7831 49.4878 52.8792 50.7136C51.768 51.0632 51.4127 51.5762 51.4105 52.7883C51.4059 54.6882 51.4105 56.5859 51.4105 58.4858C51.4105 60.102 51.4037 61.7159 51.4105 63.3321C51.4444 71.388 56.5998 78.4362 64.2673 80.8332C64.8941 81.0284 65.6817 81.0398 66.3109 80.8514C73.8154 78.5973 79.1269 71.497 79.2084 63.6408C79.2468 60.0135 79.2197 56.3861 79.2174 52.7565C79.2174 51.592 78.8712 51.0722 77.7962 50.734H77.7939ZM75.825 59.3189C75.825 59.5141 75.911 64.1175 75.4335 66.4192C74.3155 71.808 71.0114 75.4671 65.865 77.4101C65.5346 77.535 65.082 77.51 64.7402 77.3943C59.13 75.5238 54.9681 69.924 54.8278 63.9722C54.7508 60.7444 54.8165 57.5143 54.7893 54.2842C54.7848 53.7825 54.9771 53.6191 55.423 53.4806C58.5506 52.5227 61.6624 51.5194 64.7923 50.566C65.1408 50.4594 65.5799 50.4821 65.9352 50.5887C68.9836 51.5149 72.0184 52.4909 75.0669 53.4125C75.6553 53.5896 75.8612 53.8484 75.8431 54.4726C75.7956 56.0865 75.8273 57.7027 75.8273 59.3166L75.825 59.3189ZM72.0003 61.1984C69.6557 63.6068 67.2772 65.9834 64.8806 68.3373C64.2559 68.9501 63.2896 68.9161 62.6536 68.3009C61.2935 66.9867 59.9515 65.6497 58.6479 64.2786C57.9713 63.5659 58.0527 62.5649 58.7339 61.9225C59.3812 61.3119 60.3498 61.2687 61.0287 61.9225C61.9385 62.8032 62.7849 63.7498 63.7377 64.7553C65.7157 62.7601 67.5397 60.9101 69.3774 59.0737C70.1468 58.3042 71.0838 58.2089 71.7989 58.7922C72.5503 59.4029 72.7019 60.4833 72.0026 61.2006L72.0003 61.1984Z" fill="#0072DA"></path></svg>
                <div class="mission-value-label">Integrity</div>
            </div>
            <div class="mission-value-box">
                <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M55.1754 20.0269C52.2406 18.3692 49.2258 18.4091 46.218 20.1468C45.3872 20.6265 44.6717 20.6195 43.8221 20.1163C42.41 19.2839 40.8402 18.8606 39.261 18.8606C38.136 18.8606 37.0087 19.0746 35.9284 19.5072C33.3631 20.5348 31.4638 22.6464 30.7178 25.2988C29.9058 28.1839 30.4212 31.0103 32.2899 33.9378C34.8388 37.9282 38.7221 41.0367 44.5117 43.722C44.7635 43.8396 45.4343 43.7996 45.9238 43.5739C49.8259 41.7798 52.9749 39.5718 55.5567 36.823C58.3033 33.9002 59.6236 30.9868 59.7083 27.6572C59.6024 24.2336 58.0773 21.6658 55.1731 20.0246L55.1754 20.0269ZM54.2858 32.969C52.3418 35.5509 49.6635 37.7071 45.8602 39.7599C45.6273 39.8845 45.3425 39.948 45.053 39.948C44.7635 39.948 44.4646 39.8822 44.2246 39.7528C40.4448 37.7283 37.783 35.5956 35.8484 33.0419C34.8011 31.6593 33.4643 29.5101 33.9397 26.8201C34.3186 24.6756 35.6884 23.1566 37.7971 22.5429C39.8541 21.9457 41.7675 22.4888 43.3279 24.1137C43.4102 24.2007 43.4926 24.29 43.575 24.3794C43.7703 24.5933 43.9539 24.7956 44.1304 24.9155C45.2272 25.6562 46.1733 24.7767 46.531 24.3629C48.0231 22.6346 49.9295 21.9833 52.0453 22.4818C54.5635 23.0767 56.2392 25.2447 56.218 27.8759C56.1969 29.5995 55.5638 31.2619 54.2811 32.9643L54.2858 32.969ZM29.3668 8.82952C28.8255 8.23697 28.7338 7.6256 29.0821 6.90137C29.3339 6.37466 29.7222 6.02195 30.8119 5.91378C30.859 5.9373 30.9084 5.96081 30.9602 5.98432C31.2779 6.13246 31.6709 6.31822 31.9439 6.55336C32.7983 7.29641 33.6149 8.14762 34.4034 8.97296L34.6222 9.20105C35.3989 10.0123 35.4318 11.0704 34.7023 11.7711C33.9821 12.4624 32.9654 12.4154 32.1699 11.6489C31.2826 10.7929 30.28 9.82417 29.3692 8.82952H29.3668ZM38.8397 61.3176C34.8082 57.2543 30.7084 53.17 27.2275 49.711C26.0225 48.5118 24.714 47.8487 23.3419 47.7359C22.3958 47.6606 21.4261 47.7076 20.3976 47.7594C19.9293 47.7829 19.4445 47.8064 18.9526 47.8205L17.9688 47.844V46.8611C17.9688 46.6754 17.9712 46.4685 17.9735 46.2498C17.9806 45.7466 17.9876 45.1752 17.9641 44.6132C17.8064 40.8039 14.568 37.6248 10.7459 37.5261C10.6776 37.5261 10.607 37.5237 10.5388 37.5237C8.55712 37.5237 6.70255 38.2644 5.2928 39.6259C3.8501 41.0179 3.04284 42.9037 3.02401 44.9353C3.00283 47.2209 3.00754 49.5488 3.01225 51.7991C3.01695 54.1058 3.02166 56.4925 3.00048 58.8415C2.97694 61.2776 3.82185 63.3069 5.58699 65.0469C7.66043 67.0903 9.7527 69.183 11.7744 71.2076C13.7443 73.178 15.7824 75.2167 17.7993 77.206C18.5336 77.9302 18.8561 78.725 18.8396 79.7761C18.8043 82.1745 18.8137 84.6176 18.8231 86.9784L18.8278 88.0131C18.8302 88.916 19.1691 89.9906 20.7601 89.9929C27.2157 90.0024 33.2054 90.0024 39.0751 89.9929C40.2824 89.9929 40.9767 89.2922 40.9791 88.0789C40.9885 80.1382 40.9885 73.0958 40.9791 66.5494C40.9767 64.5037 40.2565 62.7449 38.8444 61.3199L38.8397 61.3176ZM37.51 80.7049C37.51 82.3086 37.51 83.9146 37.51 85.53V86.4894H22.4122L22.351 85.5958C22.3463 85.5182 22.3393 85.4406 22.3322 85.363C22.3157 85.1702 22.2993 84.9703 22.2993 84.754C22.2993 84.1662 22.2922 83.5807 22.2875 82.9928C22.2734 81.502 22.2569 79.9595 22.3204 78.4334C22.3675 77.3118 22.0357 76.51 21.1743 75.6635C18.9408 73.4696 16.6908 71.2193 14.5162 69.0443C12.4475 66.9774 10.3105 64.8376 8.19232 62.7543C6.99203 61.5715 6.44366 60.2406 6.46485 58.5594C6.50015 55.7988 6.49544 52.9889 6.49073 50.2707C6.48838 48.5212 6.48603 46.7718 6.49073 45.0223C6.50015 42.9225 8.00169 41.2342 10.061 41.0108C12.1086 40.7898 13.9796 42.1442 14.4174 44.1664C14.5091 44.5897 14.5091 45.0059 14.5091 45.3703L14.5138 47.5877C14.5209 49.9133 14.528 52.3187 14.5068 54.6866C14.4856 57.031 15.2834 58.9615 16.945 60.5839C17.7311 61.3528 18.5148 62.1406 19.275 62.9024C19.8751 63.5044 20.4753 64.1087 21.0801 64.7059C21.9415 65.5571 22.9747 65.6253 23.7161 64.8847C24.4621 64.1369 24.3868 63.1258 23.5184 62.2464C22.7582 61.4751 21.9886 60.7109 21.2214 59.9443C20.6118 59.3377 20.0022 58.731 19.3974 58.1197C17.6017 56.3114 17.5028 53.8895 19.155 52.2317C19.9669 51.4182 21.0095 50.9926 22.0945 51.0419C23.163 51.0866 24.1868 51.5686 25.0529 52.4293C26.4791 53.8495 27.903 55.2745 29.3268 56.6971C31.525 58.8956 33.7985 61.1671 36.0438 63.3915C37.0464 64.3862 37.5312 65.5501 37.5265 66.9515C37.5076 71.5203 37.51 76.1878 37.5123 80.7025L37.51 80.7049ZM43.2431 2.57949C43.149 1.29562 43.5256 0.512606 44.5023 0H45.4931C46.4675 0.512606 46.8464 1.29562 46.7522 2.57949C46.6957 3.33664 46.7099 4.08674 46.7216 4.81332C46.7287 5.26715 46.7358 5.69275 46.7287 6.12306C46.7099 7.18119 45.9803 7.9501 44.9989 7.9501C44.0174 7.9501 43.2879 7.18119 43.269 6.12306C43.262 5.69745 43.269 5.25304 43.2761 4.82508C43.2879 4.09379 43.302 3.33664 43.2455 2.57479L43.2431 2.57949ZM55.2766 11.757C54.5682 11.054 54.5941 10.0193 55.3378 9.24102C56.4416 8.08883 57.3124 7.22351 58.1597 6.43109C58.5315 6.08543 58.9081 5.91378 59.3176 5.91378C59.5647 5.91378 59.8213 5.97492 60.0943 6.09954C60.6262 6.33939 60.9886 6.72267 61.1181 7.82783C61.0969 7.87485 61.0733 7.92188 61.0498 7.97126C60.8945 8.30046 60.6991 8.71195 60.4497 9.00118C59.7154 9.85003 58.8681 10.6566 58.0467 11.4372L57.7996 11.6724C57.0159 12.4178 55.978 12.453 55.2743 11.7547L55.2766 11.757ZM80.0497 37.5378C76.1617 37.2768 72.7703 39.9457 72.1513 43.7596C72.0336 44.4792 72.0101 45.2246 71.9866 46.0123C71.9748 46.3862 71.963 46.7624 71.9395 47.1339L71.8948 47.9052L71.1652 48.018C71.0758 48.0392 70.8945 48.0721 70.678 48.0227C70.4944 47.9804 70.3109 47.9357 70.1296 47.8863C69.3318 47.6724 68.5669 47.5642 67.835 47.5642C65.8957 47.5642 64.1776 48.319 62.6337 49.8427C60.0378 52.401 57.4183 55.0252 54.886 57.5624C53.6998 58.7498 52.516 59.9373 51.3275 61.1224C49.76 62.6884 48.9998 64.5484 49.0045 66.8128C49.014 71.2734 49.0116 75.7364 49.0116 80.197C49.0116 82.7647 49.0116 85.3301 49.0116 87.8978C49.0116 89.3463 49.6565 89.9906 51.0992 89.9929C57.0841 89.9976 63.0691 89.9976 69.0564 89.9929C70.5133 89.9929 71.1628 89.3534 71.1652 87.919V86.7903C71.1746 84.4295 71.1817 81.9864 71.1534 79.588C71.144 78.6662 71.4264 77.9702 72.0713 77.3306C74.639 74.7888 77.2372 72.1905 79.7531 69.6768C81.3653 68.0661 82.9775 66.4554 84.592 64.847C86.1759 63.2716 86.9525 61.5339 86.9667 59.5352C86.9972 55.2157 87.0231 50.0896 86.9667 44.8389C86.9243 41.0061 83.8883 37.7965 80.0544 37.5355L80.0497 37.5378ZM81.9866 62.5615C80.3674 64.1651 78.7294 65.8088 77.1455 67.3959C74.5095 70.0366 71.7842 72.7689 69.0612 75.4166C68.0044 76.4442 67.5996 77.4247 67.6679 78.7956C67.7455 80.3592 67.7291 81.9511 67.7102 83.4913C67.7032 84.1662 67.6961 84.8434 67.6961 85.5276V86.487H52.4783V80.3428C52.4807 75.9221 52.483 71.351 52.4713 66.8551C52.4689 65.5313 52.8925 64.4614 53.7704 63.589C57.5195 59.8503 61.264 56.1069 65.0084 52.3634C66.4888 50.882 68.0209 50.9103 69.0423 51.1924C70.5274 51.6039 71.5441 52.662 71.9018 54.1693C72.2337 55.5684 71.8242 56.8946 70.718 58.0044C70.1744 58.55 69.6283 59.0955 69.0823 59.6387C68.2304 60.4875 67.3784 61.3387 66.5311 62.1923C66.0887 62.6367 65.1802 63.7912 66.2793 64.8894C67.1783 65.7876 68.2633 65.3502 68.9788 64.6401C70.0402 63.5867 71.0969 62.5285 72.156 61.4704L73.168 60.457C74.7284 58.8956 75.4839 57.0639 75.4792 54.8512C75.4721 51.4534 75.4745 48.1356 75.4792 45.3374C75.4839 42.8308 77.1596 41.0014 79.466 40.9873H79.4919C80.5604 40.9873 81.5442 41.38 82.2667 42.0972C83.0645 42.8873 83.5046 44.023 83.5046 45.2975C83.5046 46.8376 83.5046 48.3778 83.5046 49.918C83.5023 52.8408 83.4999 55.8647 83.5211 58.8368C83.5329 60.3041 83.0292 61.5221 81.9843 62.5591L81.9866 62.5615Z" fill="#0072DA"></path></svg>
                <div class="mission-value-label">Respect</div>
            </div>
            <div class="mission-value-box">
                <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M84 13.1519V12.138C83.9954 10.4655 83.4767 9.95285 81.7738 9.95057C80.9981 9.95057 80.22 9.95057 79.4282 9.95057H78.6663V9.10979C78.6663 8.26902 78.6686 7.46925 78.6663 6.66949C78.6571 5.22262 78.0833 4.65754 76.6122 4.64843C75.5771 4.64387 74.5443 4.64387 73.5093 4.64387C72.768 4.64387 72.0267 4.64387 71.2854 4.64387C71.0949 4.64387 70.8952 4.6302 70.6863 4.61653C70.6818 4.61653 70.6772 4.61653 70.6726 4.61653C70.6726 4.58007 70.6726 4.54361 70.6726 4.50944C70.6749 4.22234 70.6795 3.97398 70.6657 3.71879C70.6152 2.67522 69.9542 1.97343 69.0224 2.00077C68.1274 2.01444 67.4962 2.6661 67.4182 3.65954C67.4021 3.86233 67.4044 4.06057 67.4067 4.25424C67.4067 4.32032 67.4067 4.38867 67.4067 4.45475V6.78341C67.4067 9.48575 67.409 12.2792 67.3906 15.0249C67.3906 15.2846 67.269 15.5854 67.1956 15.6355C65.488 16.5697 64.687 18.0052 64.749 20.0217C64.7743 20.8101 64.7674 21.6167 64.7605 22.3959C64.7605 22.6374 64.7559 22.879 64.7559 23.1182H60.8727C60.9025 22.9701 60.9346 22.822 60.9668 22.6739C61.1022 22.0427 61.2399 21.3888 61.2972 20.7326C61.373 19.869 61.3707 18.9029 61.2927 17.7842C61.093 14.9337 58.7727 12.6985 55.8947 12.5846C53.0006 12.4706 50.4737 14.5714 50.1432 17.3695C49.9252 19.2242 49.7806 21.1906 50.5357 23.1569C50.3773 23.1569 50.2213 23.1569 50.0675 23.1592C49.3721 23.1638 48.7134 23.1683 48.057 23.1478C46.6088 23.0977 45.397 23.6013 44.3505 24.6745C42.9849 26.0712 41.562 27.4679 40.2263 28.7667C39.9715 29.0151 39.4873 29.3591 39.1683 29.5209C36.7194 30.7513 34.2201 31.984 31.8011 33.1779L31.2365 33.4559C31.1837 33.4833 31.1286 33.5106 31.0736 33.5402C30.9037 33.6314 30.727 33.7225 30.6008 33.7248C28.6477 33.743 26.6762 33.7407 24.7667 33.7385H23.5297C23.5618 33.5698 23.594 33.4035 23.6261 33.2372C23.7592 32.5513 23.8877 31.9042 23.9359 31.2321C24.0048 30.3047 23.9933 29.3135 23.8992 28.1971C23.6582 25.3238 21.0832 23.0749 18.1661 23.1729C15.2078 23.2777 12.7658 25.6725 12.7222 28.5092C12.7176 28.8305 12.7039 29.1518 12.6924 29.4753C12.635 30.8835 12.5777 32.3326 13.1904 33.7453C13.1767 33.7453 13.1652 33.7453 13.1514 33.7476C12.798 33.7726 12.4629 33.7977 12.1301 33.8364C8.62096 34.2602 6.05508 37.0697 6.02754 40.5171C5.99082 45.0901 5.99082 50.0573 6.02754 55.7012C6.04131 57.9069 7.16589 59.5087 9.27964 60.3358C9.78456 60.534 10.3193 60.6593 10.8334 60.7824C10.9826 60.8188 11.1341 60.853 11.2832 60.8917V78.8557H10.6337C9.92685 78.8557 10.6888 78.8671 8.84817 78.842H8.82522C8.22621 78.8375 7.64327 79.0653 7.2095 79.4891C6.77573 79.9152 6.53704 80.4803 6.53704 81.0864V87.8286C6.53704 89.0248 7.51704 90 8.72424 90H80.8259C81.9299 90 82.8318 89.1273 82.8364 88.061V88.0336C82.8226 82.8226 82.8226 77.525 82.8226 72.4006C82.8226 69.8805 82.8226 67.3605 82.8226 64.8404C82.8226 63.5121 82.2144 62.9151 80.8489 62.906C79.2584 62.8946 77.7781 62.8946 76.3276 62.906C75.6253 62.8946 74.923 62.8968 74.1886 62.9037C73.9109 62.9037 73.6309 62.906 73.3463 62.9082V59.1122C73.3463 56.6035 73.3463 54.0926 73.3463 51.5839C73.3486 49.7565 72.589 48.4168 71.0214 47.4894C69.6605 46.6828 68.2857 45.8511 66.9546 45.0445C65.6923 44.2789 64.3864 43.4883 63.0943 42.7227C62.7294 42.5063 62.6261 42.3262 62.649 41.9571C62.6903 41.2941 62.6766 40.6242 62.6605 39.9748L62.6559 39.7971C62.6307 38.6214 61.9904 37.915 60.9759 37.9241C59.9891 37.9446 59.4153 38.6191 59.3969 39.7743C59.39 40.2801 59.39 40.7837 59.3923 41.3032C59.3923 41.4262 59.3923 41.5515 59.3923 41.6746H51.977V38.1839C51.977 35.641 51.977 33.0982 51.977 30.5553C51.977 30.0769 51.977 29.1859 51.1141 28.7097C50.1708 28.188 48.1259 28.491 47.341 29.2657C47.0495 29.5551 46.7511 29.8376 46.4528 30.1224C45.7459 30.7969 45.0138 31.4964 44.3597 32.2506C43.219 33.5698 41.7158 34.2739 40.123 35.019C39.4987 35.3106 38.8561 35.6114 38.2227 35.9577C38.2227 35.8848 38.2227 35.8142 38.225 35.7413C38.2319 35.1671 38.2387 34.6704 38.2066 34.16C38.1791 33.7111 38.2869 33.5243 38.6564 33.3898C40.6807 32.6493 42.4686 31.3642 44.2862 29.3432C44.5915 29.0037 44.9334 28.6756 45.2662 28.3588C45.6266 28.0148 45.9984 27.6593 46.338 27.2743C46.898 26.6385 47.5406 26.3811 48.4839 26.3788C53.2324 26.4153 58.0612 26.4107 62.7294 26.4061H66.0067C67.4228 26.4039 68.0034 25.8297 68.0126 24.4261C68.0172 23.5511 68.0172 22.6762 68.0172 21.8012C68.0172 21.1701 68.0172 20.5366 68.0172 19.9055C68.0172 19.3632 68.1411 18.9599 68.3683 18.7389C68.5336 18.5771 68.7585 18.5042 69.066 18.5087C69.4975 18.5179 70.069 18.6705 70.0713 19.885C70.0759 21.9038 70.0759 23.9203 70.0713 25.939C70.0713 27.7505 69.3644 28.4568 67.5536 28.4614C66.6057 28.4637 65.6579 28.4614 64.7123 28.4614C63.6267 28.4614 62.5412 28.4614 61.4556 28.4637C59.9799 28.4682 59.4015 29.0333 59.39 30.471C59.3809 31.5921 59.3832 32.6265 59.39 33.6314C59.3992 34.8185 60.0166 35.5613 60.9966 35.5658H61.0058C61.9812 35.5658 62.6261 34.8162 62.649 33.6496C62.6582 33.146 62.6559 32.6425 62.6536 32.1116C62.6536 31.9771 62.6536 31.8404 62.6536 31.7014C63.1356 31.7014 63.613 31.7014 64.0857 31.7014C65.3962 31.7014 66.6333 31.706 67.891 31.6969C70.9778 31.6764 73.3165 29.35 73.3349 26.2831C73.344 24.8408 73.3417 23.3734 73.3394 21.9562C73.3394 21.2088 73.3394 20.4614 73.3394 19.7164C73.3394 17.9414 72.5568 16.5925 71.0099 15.7084C70.8401 15.6104 70.7254 15.5102 70.6932 15.4623C70.6588 14.8449 70.6634 14.2092 70.668 13.5939C70.668 13.48 70.668 13.3661 70.6703 13.2522H75.3935C75.3935 13.5233 75.3935 13.7945 75.3935 14.061C75.3935 14.9428 75.3889 15.7745 75.3981 16.6221C75.4096 17.8571 76.0178 18.4677 77.2571 18.4837C78.7856 18.5065 80.3623 18.5065 82.0767 18.4837C83.3712 18.4677 83.9794 17.8616 83.9908 16.572C84 15.4304 83.9977 14.2707 83.9954 13.1474L84 13.1519ZM15.972 28.6983C16.0134 27.4497 17.0255 26.4449 18.2763 26.413C18.2969 26.413 18.3153 26.413 18.336 26.413C19.5248 26.413 20.5369 27.3312 20.654 28.5275C20.6884 28.8738 20.6792 29.2224 20.67 29.5938C20.6655 29.7647 20.6609 29.9379 20.6609 30.1088V30.4505H20.6907C20.6907 30.471 20.6907 30.4938 20.6907 30.5143C20.6953 30.7969 20.6976 31.0885 20.6838 31.3688C20.6195 32.7951 19.7015 33.6815 18.2855 33.6815H18.2786C16.8694 33.6792 15.9835 32.7724 15.9652 31.3187V31.2549C15.9537 30.4141 15.9445 29.546 15.972 28.6961V28.6983ZM21.9668 86.7075H9.83046V82.1687H21.9668V86.7075ZM30.5939 69.9421C30.5893 67.9871 30.5847 65.9637 30.61 63.9769C30.6214 62.988 30.2152 62.3363 29.3293 61.9262C27.4497 61.0558 25.5448 60.158 23.6995 59.2922C22.2674 58.6178 20.8353 57.9456 19.4009 57.2757C18.5173 56.8633 17.8586 56.8086 17.3858 57.1071C16.913 57.4056 16.6835 58.0185 16.6812 58.9846C16.6789 63.5622 16.6789 68.142 16.6789 72.7196V78.7509H14.6593V55.5554C15.0793 55.56 15.4993 55.5691 15.9078 55.5759C17.0645 55.601 18.2602 55.626 19.4284 55.5235C22.0815 55.2888 24.2939 55.8995 26.3939 57.442C27.6883 58.3922 29.1067 59.2011 30.4814 59.9826C30.9841 60.2674 31.4844 60.5545 31.9801 60.8462C32.4988 61.1515 32.7214 61.5183 32.7191 62.072C32.7168 64.2776 32.7145 66.4833 32.7145 68.6889C32.7145 70.236 32.7145 71.7831 32.7145 73.328C32.7145 73.3621 32.7099 73.4009 32.7054 73.4419H30.5962V72.2434C30.5962 71.4755 30.5962 70.7077 30.5939 69.9421ZM24.0369 73.4989C22.6346 73.5125 22.031 74.1049 22.0172 75.4789C22.0104 76.3037 22.0104 77.1263 22.0127 77.9739C22.0127 78.2405 22.0127 78.5094 22.0127 78.7805H19.9999V61.1447L20.273 61.2723C22.4831 62.3044 24.769 63.3708 27.0044 64.444C27.1398 64.5101 27.289 64.6718 27.3051 64.7151C27.3303 67.144 27.3303 69.5342 27.328 72.0634V73.492C27.0434 73.492 26.768 73.492 26.4949 73.492C25.648 73.492 24.8402 73.4897 24.0346 73.4966L24.0369 73.4989ZM43.3131 86.7121H25.3313V76.7959H43.3108V86.7121H43.3131ZM64.7513 68.1511H57.3727V50.5654C57.453 50.5973 57.5288 50.6292 57.6022 50.6634L58.8645 51.2535C60.634 52.0806 62.4631 52.9374 64.2464 53.8123C64.4621 53.9194 64.6848 54.227 64.6848 54.3387C64.7307 58.2646 64.7375 62.2566 64.7467 66.1164L64.7513 68.1375C64.7513 68.1375 64.7513 68.1466 64.7513 68.1511ZM76.1371 66.1415H76.3276C76.4515 66.1415 76.5801 66.1415 76.7086 66.1415C77.3902 66.1438 78.0719 66.146 78.7604 66.1438H79.4971V86.7143H68.0929V66.1415H76.1394H76.1371ZM52.0573 44.9876C52.1216 44.9785 52.1858 44.9693 52.2455 44.9693H53.1658C55.4517 44.9625 57.8133 44.958 60.1382 44.9785C60.4045 44.9807 60.7258 45.0787 60.9966 45.2405C62.5549 46.1679 64.1362 47.1203 65.6625 48.0408C66.8697 48.7677 68.0769 49.4968 69.2864 50.2191C69.8532 50.5586 70.0827 50.9049 70.0827 51.413C70.0805 53.5776 70.0782 55.7423 70.0782 57.9069C70.0782 59.5588 70.0782 61.2107 70.0759 62.8513H67.9782V62.2361C67.9782 61.4249 67.9782 60.616 67.9759 59.8049C67.9713 57.7246 67.969 55.5759 67.9896 53.4614C67.9988 52.4019 67.5742 51.7343 66.6103 51.29C64.4025 50.276 62.1671 49.2211 60.0051 48.2003C59.032 47.74 58.0566 47.2798 57.0812 46.8218L56.8976 46.7352C56.613 46.5985 56.3193 46.4572 56.0002 46.3706C55.3439 46.1952 54.9262 46.3501 54.6921 46.5096C54.4557 46.6714 54.1573 47.0109 54.0907 47.6968C54.0609 47.9952 54.0632 48.2914 54.0632 48.5785V48.7517C54.0632 53.8761 54.0632 58.9983 54.0632 64.1227V68.1625H52.0619V44.983L52.0573 44.9876ZM64.7123 71.4869V86.7075H46.7144V71.4869H64.7123ZM39.4345 38.9768C40.6486 38.3844 41.9063 37.7715 43.118 37.1221C44.3574 36.459 45.4567 35.5681 46.2784 34.8663C46.9715 34.2739 47.6049 33.6131 48.2728 32.9159C48.3921 32.7929 48.5115 32.6653 48.6354 32.5399V68.1967H45.7964C43.8295 68.1967 43.3751 68.6524 43.3751 70.637C43.3751 71.3479 43.3751 72.0588 43.3751 72.7925V73.4282H35.9873V72.7355C35.9873 71.7216 35.985 70.7054 35.9827 69.6914C35.9781 67.3081 35.9712 64.845 35.9988 62.4229C36.0217 60.4041 35.184 58.9345 33.4375 57.9319C32.0421 57.1322 30.6444 56.2777 29.2903 55.4529C28.1749 54.7716 27.0228 54.0698 25.8752 53.3976C25.3818 53.1083 25.228 52.8303 25.2693 52.2926C25.3267 51.5384 25.2992 50.7773 25.2739 50.0391C25.2418 49.1094 24.6336 48.4487 23.7615 48.394C23.3254 48.3666 22.91 48.5079 22.591 48.7882C22.2605 49.0798 22.0654 49.4968 22.0379 49.9616C22.0035 50.5586 22.0104 51.1465 22.0195 51.7685C22.0195 51.928 22.0241 52.0875 22.0241 52.2493H14.6111V49.3715C14.6111 47.5259 14.6111 45.6802 14.6111 43.8346C14.6111 43.0166 14.4344 42.447 14.0717 42.0916C13.794 41.8227 13.4199 41.6883 12.9563 41.6974C11.8891 41.7156 11.3475 42.4516 11.3475 43.8848C11.3475 47.0884 11.3475 50.292 11.3475 53.4979V57.5332C10.7003 57.5696 10.1816 57.4329 9.83964 57.1367C9.47473 56.82 9.28883 56.3119 9.28653 55.6306C9.27046 50.0436 9.26358 45.3521 9.28883 40.6265C9.3003 38.4687 10.9045 37.0127 13.2799 37.0036C16.4173 36.9922 19.6074 36.9899 22.6943 36.9899C24.0231 36.9899 25.3497 36.9899 26.6762 36.9899C26.7336 36.9899 26.791 36.9899 26.8484 36.9876C26.9585 36.9854 27.0618 36.9831 27.1559 36.9922C30.2106 37.293 32.7627 36.7211 34.9614 35.24C34.9614 35.3403 34.9614 35.4382 34.9637 35.5339C34.9706 36.156 34.9775 36.7438 34.9362 37.3135C34.9293 37.4183 34.7824 37.6348 34.6286 37.7304C32.8132 38.8401 31.1562 39.2867 29.4119 39.1363C28.0762 39.0224 26.7152 39.052 25.3979 39.0816C24.9228 39.093 24.4454 39.1021 23.9703 39.1067C22.614 39.1181 22.0287 39.7014 22.0149 41.0525C22.0035 41.9822 21.9966 43.085 22.0172 44.2174C22.0379 45.3362 22.6667 46.0311 23.6582 46.0311C24.652 46.0311 25.2556 45.3521 25.2739 44.2129C25.2831 43.7321 25.2808 43.2491 25.2785 42.7364C25.2785 42.6065 25.2785 42.4743 25.2785 42.3399C25.6756 42.3399 26.0657 42.3331 26.4513 42.3262C27.5506 42.3103 28.5903 42.2966 29.6323 42.3581C31.6473 42.4766 33.5339 42.0392 35.3975 41.0229C36.7171 40.3029 38.0965 39.6307 39.4299 38.9791L39.4345 38.9768ZM58.0589 19.9374C58.0589 20.2062 58.0635 20.4751 58.0566 20.7417C58.0222 22.143 57.0858 23.0635 55.6743 23.084C55.6583 23.084 55.6445 23.084 55.6284 23.084C54.2812 23.084 53.3747 22.2068 53.3127 20.8351C53.2737 19.9966 53.2737 19.1194 53.3127 18.2262C53.3701 16.8409 54.3799 15.8201 55.6766 15.8201C55.7019 15.8201 55.7271 15.8201 55.7524 15.8201C57.056 15.8611 58.0268 16.8978 58.0589 18.2877C58.0658 18.5771 58.0635 18.8642 58.0612 19.1536C58.0612 19.288 58.0612 19.4224 58.0612 19.5569C58.0612 19.6822 58.0612 19.8075 58.0612 19.9351L58.0589 19.9374ZM75.3476 9.8731H70.7345V7.93407H75.3476V9.8731ZM80.6699 15.2071H78.7214V13.2362H80.6699V15.2071Z" fill="#0072DA"></path></svg>
                <div class="mission-value-label">Growing Together</div>
            </div>
            <div class="mission-value-box">
                <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M89.9953 48.1099C89.6145 46.6007 89.0469 44.8777 87.741 43.4696C87.8648 43.2627 87.9863 43.0605 88.1077 42.8584C88.4535 42.2848 88.7782 41.7418 89.0959 41.18C89.3669 40.7004 89.451 40.1973 89.3295 39.7625C89.2244 39.3863 88.9791 39.0807 88.617 38.8762C87.8461 38.4437 86.9957 38.7164 86.4561 39.5744C86.1547 40.0516 85.8721 40.5264 85.573 41.0295C85.4749 41.1917 85.3791 41.3563 85.2787 41.5232L85.2109 41.4926C85.0334 41.4127 84.8745 41.3398 84.7133 41.2693C80.3144 39.3746 75.2987 41.3069 73.2943 45.6652C73.1752 45.9237 73.0841 46.2152 72.9976 46.4997C72.8388 47.0145 72.6752 47.5457 72.3762 47.7173C71.9791 47.9454 71.36 47.8889 70.7596 47.8349C70.484 47.809 70.2013 47.7832 69.9303 47.7832H69.921C69.4888 47.7832 69.0589 47.7832 68.6267 47.7808C67.6759 47.7761 66.6924 47.7714 65.7229 47.7973C65.223 47.8114 64.8072 47.983 64.5198 48.2909C64.2488 48.5848 64.1157 48.9773 64.1343 49.4263C64.1741 50.3173 64.7651 50.8485 65.7603 50.8885C65.9916 50.8979 66.2252 50.8955 66.4495 50.8932H72.5795C72.5888 50.9261 72.6005 50.9614 72.6098 50.9943C72.6566 51.1565 72.7009 51.2999 72.736 51.4456C73.7102 55.3431 76.9434 57.9736 80.9708 58.1476C81.09 58.1523 81.2091 58.1546 81.3306 58.1546C85.0077 58.1546 88.3507 55.7169 89.5445 52.1203C89.6659 51.7559 89.7617 51.3798 89.8575 51.0154C89.9019 50.8462 89.9463 50.6769 89.9907 50.51L90 48.1593L89.993 48.1005L89.9953 48.1099ZM81.2605 43.6694C82.0945 43.6694 82.9168 43.8645 83.641 44.2735L81.0526 48.5801C81.0036 48.5189 80.9545 48.4602 80.9078 48.4014C80.6695 48.1052 80.4452 47.8278 80.2046 47.5598C79.5575 46.8382 78.7235 46.723 78.0297 47.2589C77.7049 47.5105 77.497 47.8631 77.448 48.2486C77.3919 48.6788 77.5321 49.1372 77.8381 49.5368C78.5156 50.4183 79.2211 51.2928 79.9383 52.1391C80.3681 52.6445 80.8564 52.8937 81.347 52.856C81.8492 52.8184 82.3048 52.487 82.6645 51.8993C83.3327 50.8133 83.9961 49.699 84.6386 48.6224C84.9586 48.0841 85.281 47.5481 85.6034 47.0098C85.7249 46.8053 85.8534 46.6054 85.9819 46.4009L85.9912 46.3845C87.4162 48.1804 87.171 51.2881 85.3978 53.2157C83.3444 55.4466 79.9149 55.6652 77.5905 53.7141C75.3314 51.817 74.9179 48.439 76.649 46.0248C77.733 44.5109 79.5201 43.6694 81.2559 43.6694H81.2605ZM42.941 19.2358C43.4058 19.3534 43.4689 19.4968 43.4643 19.9693C43.4479 21.5607 43.4526 23.1757 43.4549 24.7389V25.7732C43.4572 25.9472 43.4549 26.1211 43.4549 26.2951C43.4502 26.8499 43.4479 27.4235 43.4923 27.9876C43.5624 28.8574 44.1254 29.3746 44.9991 29.3746H45.0038C45.8541 29.3746 46.4054 28.8692 46.5152 27.9923C46.5479 27.7314 46.5479 27.4705 46.5456 27.2166V25.4535C46.5526 23.6834 46.555 21.8522 46.5363 20.0516C46.5316 19.5156 46.6063 19.351 47.153 19.21C49.7671 18.5353 51.8954 16.7417 52.9933 14.2852C54.082 11.8451 54.0096 9.08067 52.7901 6.69703C52.6476 6.41729 52.4467 6.14696 52.2551 5.88603C51.9327 5.44879 51.6314 5.03741 51.6454 4.67305C51.6641 4.22406 51.9981 3.76096 52.3532 3.26966C52.5308 3.02518 52.713 2.7713 52.8578 2.51272C53.1312 2.02612 53.2129 1.54187 53.0938 1.10698C52.991 0.730866 52.7387 0.420569 52.3696 0.209003C51.594 -0.232935 50.7693 0.0373991 50.2157 0.911872C49.912 1.39142 49.627 1.86862 49.3256 2.37403C49.2298 2.53388 49.134 2.69373 49.0383 2.85593C47.2605 1.9744 45.4406 1.68526 43.6231 1.99321C39.4554 2.70078 36.4185 6.16811 36.2386 10.4206C36.0657 14.5085 38.8831 18.2133 42.9363 19.2311L42.941 19.2358ZM47.3352 5.71442C46.6367 6.88274 45.9359 8.0487 45.2 9.27343L44.8215 9.90578C44.7772 9.85172 44.7351 9.8 44.6907 9.74828C44.4548 9.46149 44.2305 9.19116 43.9969 8.92788C43.3147 8.15684 42.455 8.04165 41.7542 8.62698C41.065 9.20056 41.016 10.1173 41.6281 10.9095C42.3079 11.7864 42.9947 12.6373 43.6722 13.4413C44.116 13.9678 44.6113 14.2264 45.1089 14.1865C45.6135 14.1489 46.0784 13.8056 46.4522 13.1968C47.1133 12.1178 47.7744 11.0106 48.4122 9.94104C48.7509 9.37216 49.092 8.80094 49.4331 8.23206C49.5405 8.05575 49.6527 7.8818 49.7695 7.70549C51.2249 9.81175 50.9048 12.8442 48.9752 14.7506C46.8797 16.8193 43.5998 16.9392 41.343 15.0304C39.1518 13.178 38.7056 9.86112 40.3245 7.47982C41.4108 5.88368 43.1676 5.00685 44.9641 5.00685C45.7864 5.00685 46.618 5.19021 47.396 5.57338C47.3749 5.6298 47.3562 5.67446 47.3352 5.70972V5.71442ZM24.4084 47.8067C24.1608 47.7785 23.9132 47.7832 23.6725 47.7879C23.5884 47.7879 23.502 47.7879 23.4226 47.7902H17.4701C17.386 47.5598 17.3066 47.3318 17.2271 47.1109C17.0122 46.502 16.8113 45.9284 16.5473 45.376C16.4048 45.0775 16.1969 44.7907 15.996 44.5156C15.6876 44.0949 15.398 43.6976 15.4096 43.345C15.4283 42.8913 15.7624 42.4235 16.1175 41.9298C16.2904 41.6877 16.4702 41.4385 16.6151 41.1823C17.1244 40.282 16.9748 39.4216 16.2249 38.935C15.8699 38.7046 15.4821 38.6247 15.1059 38.707C14.6714 38.801 14.2813 39.1019 13.9799 39.5768C13.6739 40.0563 13.3772 40.5523 13.0899 41.0295C12.9987 41.1823 12.9076 41.3327 12.8165 41.4855C9.69312 40.0751 7.15141 40.5288 5.55583 41.1682C3.32015 42.0638 1.47695 43.9021 0.624258 46.0859C-1.13018 50.5805 0.932627 55.5735 5.31755 57.4541C6.45758 57.943 7.60462 58.1875 8.75166 58.1875C9.79591 58.1875 10.8402 57.9853 11.8751 57.5787C14.7952 56.4315 16.6595 54.1866 17.4187 50.9002H18.3462C18.9092 50.9002 19.4745 50.9002 20.0399 50.9002C21.4322 50.9002 22.8736 50.9049 24.2893 50.8885C25.205 50.8791 25.8078 50.3314 25.8615 49.4616C25.9152 48.5754 25.3289 47.9101 24.4061 47.8067H24.4084ZM8.77035 43.6741C9.6464 43.6741 10.4804 43.8809 11.1602 44.2759L8.57411 48.5824L7.95738 47.8325C6.94583 46.596 6.08847 46.8452 5.54882 47.2707C5.20541 47.541 5.00918 47.8678 4.96479 48.2416C4.91106 48.7047 5.10029 49.2148 5.5278 49.7531C6.13753 50.5218 6.7496 51.2811 7.34998 52.0074C7.84524 52.6069 8.35452 52.896 8.87548 52.856C9.38475 52.8184 9.84497 52.4705 10.2421 51.8194C10.8846 50.7662 11.527 49.692 12.1484 48.6529C12.5222 48.0276 12.896 47.4047 13.2697 46.7817C13.3468 46.6548 13.4309 46.5326 13.5197 46.4056C14.9424 48.2768 14.6527 51.4315 12.8282 53.3309C10.7724 55.4701 7.39437 55.644 5.13533 53.7305C2.84125 51.7865 2.42308 48.4813 4.16116 46.0413C5.31988 44.4169 7.11637 43.6764 8.77269 43.6764L8.77035 43.6741ZM67.3302 29.5227C72.1566 31.8734 76.0813 31.3656 79.0015 28.0111C81.8095 24.7836 81.7745 20.7403 78.9034 16.3092L79.2374 15.7497C79.5458 15.2396 79.8565 14.7224 80.1602 14.2006C80.7489 13.1921 80.6204 12.32 79.8121 11.8122C78.9968 11.2998 78.1441 11.5842 77.5297 12.5715C77.2447 13.0299 76.9667 13.4907 76.6911 13.9443L76.4224 14.3863C71.9253 12.7972 68.4725 13.4436 65.8724 16.3585C64.646 17.7361 63.9124 19.3369 63.6952 21.1211C63.4242 23.3473 63.9334 25.4347 65.2136 27.3294L64.3867 28.1451C63.4639 29.0549 62.5925 29.9129 61.7141 30.8015L61.6627 30.8532C61.3894 31.1282 61.0483 31.4691 61.0787 31.8217C61.1137 32.2378 61.2399 32.9994 61.6791 33.4084C61.9267 33.6411 62.214 33.754 62.5154 33.754C62.9102 33.754 63.3284 33.5565 63.7138 33.1686C64.6016 32.2777 65.4846 31.3844 66.3607 30.4982L67.3325 29.5156L67.3302 29.5227ZM72.194 21.4714C72.1426 21.4079 72.0912 21.3468 72.0422 21.2857C71.7875 20.9754 71.5492 20.6815 71.2899 20.4018C70.6895 19.7554 69.8205 19.6519 69.1734 20.1503C68.4609 20.7004 68.3557 21.603 68.9187 22.3506C69.5238 23.1545 70.206 24.0055 71.068 25.0304C71.4932 25.5358 71.9861 25.7803 72.486 25.745C72.993 25.7074 73.4485 25.383 73.8013 24.8094C74.5044 23.667 75.2053 22.494 75.8804 21.3609C76.1981 20.8296 76.5158 20.2984 76.8336 19.7695C76.9293 19.6096 77.0275 19.4521 77.1279 19.2946C78.4572 20.9307 78.282 24.0619 76.6957 25.9448C74.7965 28.2015 71.4348 28.6012 69.0449 26.8593C66.655 25.1174 66.0056 21.777 67.5638 19.257C68.6267 17.5386 70.4372 16.5913 72.3272 16.5913C73.1495 16.5913 73.9882 16.7723 74.7871 17.146L72.194 21.4737V21.4714ZM27.5248 33.7281C27.1043 33.7281 26.6698 33.5377 26.282 33.1569C25.2705 32.1672 24.2636 31.14 23.3735 30.2326C23.1703 30.0257 22.9904 29.8047 22.8572 29.6449C22.8526 29.6378 22.8479 29.6331 22.8432 29.6261C22.4461 29.7671 22.0629 29.9176 21.6915 30.061C20.7898 30.4136 19.9371 30.745 19.0423 30.9049C15.7834 31.4855 12.945 30.3995 10.8308 27.7667C8.79138 25.2279 8.32882 22.3106 9.46185 19.0972C9.84497 18.0111 10.6322 17.5222 11.52 17.8231C11.9171 17.957 12.2115 18.2062 12.375 18.5424C12.5829 18.9679 12.5759 19.5297 12.354 20.1644C11.3051 23.1851 12.5876 26.2669 15.405 27.4916C18.1686 28.6952 21.3668 27.5128 22.682 24.8024C23.5861 22.9383 23.4997 21.1117 22.4157 19.2264L22.2755 19.4545C22.0443 19.8282 21.8317 20.1714 21.6238 20.517C21.3177 21.0295 21.0117 21.5419 20.7057 22.0544C20.1824 22.9335 19.6404 23.8433 19.0984 24.7319C18.7176 25.3525 18.248 25.7004 17.7341 25.738C17.2271 25.7756 16.7155 25.5029 16.26 24.9552C15.5101 24.0525 14.8606 23.2486 14.2719 22.494C13.9262 22.052 13.7743 21.5842 13.8281 21.1446C13.8771 20.7591 14.0827 20.4159 14.4238 20.1503C15.1223 19.6096 15.9797 19.7554 16.6595 20.5335C16.8837 20.7897 17.0963 21.0506 17.3206 21.328C17.358 21.375 17.3954 21.4197 17.4351 21.4667V21.462C17.5519 21.2786 17.6547 21.1141 17.7551 20.9472C18.3602 19.9387 18.9652 18.9279 19.5843 17.8959L20.0422 17.1319C18.7713 16.5113 17.4234 16.4103 15.9259 16.8216C15.8138 16.8522 15.7017 16.8969 15.5825 16.9415C15.4797 16.9815 15.3793 17.0191 15.2741 17.052C14.3654 17.3365 13.5945 17.0026 13.2651 16.1822C12.931 15.3501 13.2954 14.5391 14.1692 14.1653C16.3675 13.2274 18.6592 13.2133 20.9766 14.1183C21.1565 14.1888 21.3341 14.2687 21.5326 14.3581C21.5724 14.3769 21.6144 14.3957 21.6588 14.4145C21.7569 14.2523 21.8527 14.0901 21.9508 13.9302C22.2498 13.4319 22.5325 12.9594 22.8362 12.4845C23.4132 11.5772 24.2939 11.3162 25.0789 11.8216C25.8381 12.3106 25.997 13.1686 25.49 14.0595C25.1793 14.6073 24.8593 15.1362 24.5206 15.698C24.3991 15.9002 24.2776 16.1023 24.1538 16.3092C26.873 19.7789 27.102 23.4836 24.8359 27.3247L27.943 30.4347C28.1112 30.6016 28.2817 30.7709 28.4429 30.9449C29.1718 31.73 29.2115 32.6515 28.541 33.2932C28.2397 33.5824 27.8869 33.7258 27.5225 33.7258L27.5248 33.7281ZM56.9998 51.9839C56.9998 51.7536 56.9951 51.5279 56.9928 51.3046C56.9834 50.5852 56.9764 49.9059 57.0208 49.2359C57.0419 48.9233 57.2077 48.5519 57.4156 48.352C60.3732 45.4865 61.8192 41.9557 61.7118 37.856C61.6744 36.4597 61.1534 35.9378 59.7845 35.9355C55.3715 35.9261 50.8861 35.9284 46.6694 35.9355C44.1674 35.9378 42.1911 36.9439 40.7964 38.9209C40.766 38.9656 40.738 39.0102 40.7123 39.0549C38.8831 37.7949 36.9371 37.7925 35.4303 37.9383C32.676 38.2016 30.4356 40.1127 29.7231 42.802C29.0083 45.4959 29.9871 48.2839 32.2158 49.9082C33.1222 50.5688 33.2367 50.8438 32.9493 51.9769C29.4194 52.4635 27.5248 54.2195 27.4664 57.0592C27.4407 58.3333 27.8332 59.4781 28.6345 60.4654C29.6834 61.7583 31.1248 62.3953 33.0311 62.4094C33.1479 64.6074 33.7646 66.5914 34.9093 68.4555C34.8252 68.519 34.7528 68.5707 34.6804 68.6201C30.8141 71.2764 28.7256 74.9106 28.471 79.424C28.3612 81.4034 28.3752 83.4179 28.3892 85.3691C28.3962 86.1777 28.4009 86.984 28.3985 87.7927C28.3962 88.7094 28.5247 89.2172 28.8471 89.5416C29.1695 89.866 29.6717 90 30.5875 90C38.6004 90 46.6157 90 54.6286 90H59.4364C59.4995 90 59.5602 90 59.6233 90C59.6864 90 59.7518 90 59.8172 90C59.9293 90 60.0461 89.9977 60.1606 89.9835C61.053 89.8848 61.5973 89.3371 61.6183 88.5167C61.6604 86.8195 61.6908 84.8119 61.5856 82.7832C61.5576 82.2684 60.9385 81.622 60.4783 81.495C59.9363 81.347 59.2425 81.7395 58.9178 81.9958C58.6304 82.2238 58.579 82.7245 58.5673 83.2322C58.5463 84.1584 58.5487 85.0635 58.5533 86.0249C58.5533 86.3093 58.5533 86.5961 58.5557 86.8829H57.0302C54.6987 86.8853 52.4981 86.8876 50.2671 86.8618C50.1806 86.8618 49.947 86.6878 49.8699 86.5209C48.9729 84.6098 48.0688 82.6257 47.111 80.4584C47.0479 80.3173 47.0689 80.0728 47.1133 80.0141C47.4917 79.5392 47.9076 79.0832 48.3094 78.6413C48.4052 78.5355 48.4986 78.432 48.5944 78.3263C49.5359 79.149 50.0732 79.5016 50.6105 79.4429C51.1618 79.3794 51.587 78.9069 52.3299 77.8679L55.771 73.0583C56.9321 74.323 57.7357 75.8251 58.2216 77.6422C58.6351 79.1866 59.5205 79.3065 60.1886 79.1349C61.1371 78.8904 61.5085 78.0653 61.2072 76.8711C60.511 74.1114 59.0883 71.7583 56.9811 69.8754C56.5443 69.4851 56.084 69.1302 55.6402 68.7893C55.4977 68.6788 55.3575 68.5707 55.222 68.4649C55.3388 68.1969 55.4556 67.936 55.5748 67.6774C55.8995 66.9605 56.2079 66.2834 56.4298 65.5618C56.6587 64.8189 56.7966 64.0385 56.9321 63.2839C56.9811 63.0018 57.0325 62.7174 57.0886 62.4353C60.7119 61.7512 62.4009 60.2562 62.5575 57.5975C62.6322 56.3234 62.3215 55.1833 61.63 54.2101C60.5717 52.7174 59.0556 51.9863 56.9975 51.9863L56.9998 51.9839ZM30.8959 58.2909C30.438 57.5857 30.4286 56.8617 30.8632 56.1377C31.2696 55.463 31.8607 55.1386 32.7017 55.1386C32.7975 55.1386 32.8956 55.1433 32.996 55.1504V59.243C32.0546 59.3276 31.3678 59.015 30.8959 58.2886V58.2909ZM53.4045 71.0437L50.3372 75.3244L48.6621 73.6436C48.9074 73.4931 49.1551 73.338 49.4074 73.1805C50.5497 72.4706 51.7318 71.7348 52.9466 71.0202C53.0097 70.9826 53.2013 71.0108 53.3718 71.0366C53.3811 71.0366 53.3928 71.039 53.4045 71.0413V71.0437ZM46.4428 76.1236L44.9641 77.5317L43.6044 76.2106L45.0482 74.7343L46.4405 76.1236H46.4428ZM36.6871 71.0131C36.8296 70.999 37.0282 70.9826 37.0703 71.0037C38.4089 72.1556 39.897 72.9924 41.4972 73.4908L39.6727 75.275L36.6241 71.0178C36.6451 71.0178 36.6638 71.0131 36.6848 71.0131H36.6871ZM39.4251 79.4334C39.9741 79.4946 40.5231 79.1302 41.4972 78.2886C41.5883 78.3944 41.6794 78.5002 41.7706 78.6083C42.1584 79.0597 42.5578 79.5251 42.9223 80.0094C42.9783 80.0846 42.9947 80.3314 42.9363 80.4631C41.9341 82.7245 41.0207 84.732 40.1399 86.5961C40.0862 86.7113 39.8339 86.8665 39.7007 86.8688C37.5071 86.89 35.2855 86.8876 33.1362 86.8876H31.557L31.5476 86.8782C31.5663 86.0273 31.5757 85.1622 31.585 84.323C31.606 82.2261 31.6294 80.0564 31.8163 77.9407C31.9565 76.3328 32.7391 74.7743 34.2692 73.0536L35.2761 74.4593C36.1008 75.6112 36.9114 76.7395 37.7174 77.8702C38.3598 78.7705 38.827 79.3653 39.4274 79.4358L39.4251 79.4334ZM44.9991 70.9026C44.9664 70.9026 44.9337 70.9026 44.9033 70.9026C40.0418 70.8533 36.1942 66.9534 36.1428 62.0263C36.1171 59.5063 36.1241 57.0357 36.1288 54.4169C36.1311 53.3379 36.1335 52.2519 36.1358 51.1518C38.521 51.2787 40.4039 50.5688 41.885 48.9914C45.9312 53.07 50.6198 51.9863 53.8998 50.7098C53.8998 50.7239 53.9021 50.7357 53.9021 50.7474C53.9021 51.8781 53.9068 53.0112 53.9138 54.1443C53.9278 56.7794 53.9395 59.5016 53.8811 62.1767C53.7713 67.0874 49.8793 70.9026 44.9991 70.9026ZM39.8082 46.5702C39.1588 47.6022 38.1753 48.0441 36.5236 48.0441H36.5049C35.1219 48.164 33.9141 47.6045 33.1899 46.5161C32.4657 45.423 32.398 44.0008 33.0124 42.8043C33.7039 41.4597 35.1032 41.0953 35.8765 40.9989C36.0938 40.9707 36.3134 40.959 36.5306 40.959C37.6917 40.959 38.8107 41.3327 39.341 41.9369C39.5162 42.1343 39.5793 42.534 39.6003 42.6633C39.7568 43.6482 39.904 44.612 39.9881 45.5923C40.0185 45.9613 39.9484 46.3469 39.8082 46.5702ZM46.5947 86.843H43.4549L45.0225 83.3921L46.5947 86.843ZM46.9988 48.5448C44.3099 47.99 42.4667 45.3901 42.7144 42.4987C42.8709 40.6628 44.1464 39.3276 45.9663 39.0972C46.3447 39.0478 46.7372 39.0431 47.0712 39.0408C49.0383 39.0408 51.0029 39.0384 52.97 39.0384C53.6661 39.0384 54.3623 39.0384 55.0585 39.0384H58.593C58.4342 41.6971 57.4834 43.8645 55.6892 45.6558C53.1101 48.2298 50.1876 49.2007 47.0012 48.5448H46.9988ZM59.2074 58.2087C58.7823 58.9633 58.1095 59.2806 57.0372 59.2195V55.1786C57.8572 55.0375 58.5136 55.2844 59.0392 55.9285C59.5509 56.5585 59.6186 57.4752 59.2051 58.2087H59.2074ZM50.0498 61.2341C50.6782 61.9369 50.5754 62.8302 49.7835 63.5636C48.4986 64.7531 46.8937 65.3878 45.0154 65.4489H44.9991C43.1068 65.3855 41.4972 64.732 40.2147 63.5049C39.4835 62.8067 39.397 61.9181 39.9904 61.2411C40.5651 60.5853 41.4739 60.5618 42.2541 61.1847C44.1674 62.715 45.8751 62.7174 47.7908 61.1917C48.5804 60.5618 49.4681 60.5806 50.0521 61.2341H50.0498ZM40.787 57.1226C40.7029 56.3351 40.7029 55.4936 40.787 54.6191C40.8665 53.7705 41.4762 53.2392 42.3476 53.2698C43.1863 53.298 43.7399 53.8316 43.8264 54.699C43.8544 54.9741 43.8474 55.2491 43.8404 55.5147C43.838 55.6323 43.8334 55.7498 43.8334 55.865C43.8334 55.9802 43.8357 56.0954 43.8404 56.2129C43.8474 56.4809 43.8544 56.7559 43.8264 57.0333C43.7329 57.9054 43.1956 58.4226 42.3546 58.4555C42.3266 58.4555 42.3009 58.4555 42.2728 58.4555C41.4412 58.4555 40.8758 57.9501 40.787 57.1226ZM46.2396 57.1391C46.1508 56.3633 46.1485 55.5218 46.2302 54.6379C46.3073 53.8128 46.896 53.2862 47.7651 53.2627C48.578 53.2486 49.2088 53.8786 49.2695 54.7813C49.2859 55.0375 49.2812 55.2938 49.2766 55.5429C49.2766 55.6464 49.2719 55.7498 49.2719 55.8509C49.2719 55.9731 49.2719 56.093 49.2766 56.2152C49.2812 56.4738 49.2859 56.7442 49.2649 57.0098C49.1948 57.8913 48.6808 58.4038 47.8188 58.4508C47.7791 58.4508 47.7417 58.4531 47.7043 58.4531C46.889 58.4531 46.3307 57.9571 46.2372 57.1367L46.2396 57.1391Z" fill="#0072DA"></path></svg>
                <div class="mission-value-label">Proactiveness</div>
            </div>
            <div class="mission-value-box">
                <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none"><path d="M74.5528 87.4897C75.1658 88.9174 74.5411 89.8661 73.0005 89.8802C69.4849 89.9108 65.9694 89.9319 62.4538 89.9413C53.7224 89.9648 44.9933 89.9836 36.2619 90C34.8059 90.0023 34.4912 89.7487 34.0849 88.375C33.3334 85.8365 32.3377 83.4084 30.8136 81.2221C30.6304 80.9591 30.4331 80.7008 30.2288 80.4519C28.6413 78.524 28.4839 78.531 26.1214 79.2684C22.9135 80.2687 19.6469 81.0906 16.4554 82.1403C13.2263 83.2017 9.4101 81.8233 8.64217 77.1291C8.43551 75.861 8.51066 74.499 8.71262 73.2216C9.01557 71.303 8.99678 71.0471 7.36698 70.089C5.52348 69.0041 5.27689 68.3489 5.84756 66.2589C5.90157 66.057 5.77945 65.7071 5.61741 65.5615C3.54846 63.7275 3.47096 63.3212 4.81895 60.8861C6.14346 58.4932 6.06361 58.1809 3.6377 56.9245C2.07365 56.1167 0.671647 55.1633 0 53.435V52.2045C0.657557 51.1079 1.29633 49.9995 1.97737 48.9193C3.94064 45.8031 6.04013 42.7668 7.8531 39.5661C9.1236 37.3258 9.4923 37.6922 7.17676 35.6092C5.46711 34.0711 4.91993 32.1761 5.84521 30.1425C7.22138 27.1179 8.03628 23.9266 9.00383 20.7681C11.7022 11.9574 17.1575 5.65699 26.2882 3.23592C30.769 2.0477 35.393 1.38783 39.963 0.5448C41.4496 0.270052 42.9713 0.176121 44.4767 0H49.3989C50.1786 0.112717 50.9559 0.225434 51.7356 0.338152C52.0245 0.380421 52.318 0.413296 52.6022 0.4767C53.5674 0.688045 54.0958 1.37374 53.9455 2.20738C53.7952 3.04806 53.0625 3.50128 52.102 3.42378C50.1504 3.2641 48.1895 2.98466 46.2403 3.03397C41.5482 3.15608 36.9007 3.80421 32.3283 4.84684C29.4867 5.49496 26.6357 6.23702 23.9257 7.29139C18.3059 9.48233 14.9265 13.9229 12.665 19.3193C12.1906 20.4464 11.7891 21.6253 11.5237 22.8158C10.9295 25.4858 10.0489 28.0407 8.91459 30.5252C8.09969 32.3076 8.22181 32.5306 9.69426 33.8269C10.2156 34.2848 10.7182 34.8014 11.0939 35.3791C11.9323 36.673 12.0075 38.0186 11.31 39.4557C9.31147 43.584 6.90669 47.4469 4.10738 51.0773C2.66076 52.9512 2.5175 52.7282 4.73676 54.1066C5.18296 54.3837 5.67143 54.595 6.12702 54.8557C8.53884 56.2365 9.26685 58.4368 8.14196 60.9871C7.86015 61.6234 7.51493 62.234 7.1932 62.8657C7.92825 63.5161 8.76664 64.0586 9.33026 64.8053C10.0371 65.74 9.10011 66.529 8.71262 67.3368C8.86997 67.4495 8.95686 67.527 9.05549 67.581C11.5589 69.0041 12.3362 70.7582 11.6646 73.6043C11.3593 74.8935 11.3616 76.1452 11.7914 77.4062C12.355 79.057 13.5738 79.7192 15.2858 79.32C21.5091 77.8664 27.4859 75.6309 33.5072 73.5433C35.0243 73.0173 36.5508 72.4725 37.9786 71.7515C38.9767 71.249 39.9325 70.5351 40.6863 69.7156C41.1936 69.1637 41.4049 68.2784 41.5881 67.5059C41.8159 66.5431 42.3443 65.9983 43.2461 66.0711C44.0751 66.1368 44.5988 66.7709 44.5776 67.7243C44.5424 69.4174 43.8684 70.8428 42.6966 72.0427C40.9094 73.872 38.6573 74.9123 36.3042 75.7647C34.766 76.3213 33.2113 76.8379 31.5768 77.4015C31.7717 77.6575 31.9173 77.8641 32.0794 78.0567C34.1154 80.4871 35.6325 83.2064 36.5601 86.2404C36.7292 86.7946 36.9406 87.0224 37.57 87.0177C48.3797 86.9566 59.1895 86.9261 70.0016 86.8885C70.2036 86.8885 70.4032 86.8603 70.6239 86.8463C70.6169 86.7335 70.6404 86.6349 70.6004 86.5715C67.6109 81.7458 66.5541 76.3354 65.8473 70.8216C65.7322 69.9222 66.2723 69.2107 67.0849 69.0886C67.942 68.9594 68.6043 69.4737 68.8016 70.4506C69.3605 73.2286 69.8349 76.0277 70.4901 78.7846C71.0607 81.1869 72.0799 83.4248 73.482 85.4819C73.907 86.1065 74.2569 86.7946 74.5552 87.4897H74.5528ZM58.5202 5.68752C58.5272 6.8053 59.0862 7.4863 59.9856 7.50039C60.9109 7.51448 61.505 6.8006 61.5121 5.6429C61.5215 4.44294 61.4745 3.24062 61.5262 2.04535C61.5661 1.13187 61.2608 0.460262 60.4717 0.00469655H59.5934C58.7973 0.39451 58.4662 1.01445 58.5037 1.91619C58.5554 3.17252 58.5108 4.43119 58.5178 5.68987L58.5202 5.68752ZM78.8739 25.6244C80.8419 34.2308 78.3667 41.5175 71.2697 47.0547C69.9358 48.095 68.656 49.2245 67.4724 50.4315C66.8688 51.0468 66.5306 51.9227 66.0375 52.7376C68.4211 53.8812 69.1491 55.7222 69.0153 58.0306C68.98 58.6435 69.0223 59.2611 69.0059 59.8763C68.9589 61.7268 68.144 63.1028 66.5142 64.0281C66.2394 64.1831 66.0163 64.6386 65.9835 64.9768C65.6876 67.9051 63.4636 70.2017 60.6032 70.4717C57.71 70.7441 55.0516 68.9383 54.2437 66.1509C54.0065 65.3314 53.0178 63.9365 52.5012 63.2719C51.9423 62.5533 51.2894 61.7291 51.172 60.8814C50.9559 59.3362 50.9865 57.73 51.1344 56.1684C51.2871 54.5622 52.3814 53.5172 54.0042 52.7141C53.4194 51.8147 52.9944 50.8519 52.2969 50.1615C51.0334 48.9099 49.6526 47.7616 48.2459 46.672C37.5253 38.3755 37.9715 21.5971 49.1711 13.9769C61.2444 5.76267 76.0347 13.2184 78.8739 25.6244ZM62.9188 64.547H57.0289C57.2426 66.4257 58.5014 67.5787 60.1617 67.4941C61.7469 67.4143 62.9399 66.1579 62.9211 64.547H62.9188ZM65.9882 57.1312C65.9506 56.0439 65.4245 55.5344 64.3349 55.5203C62.9023 55.5015 61.4674 55.5156 60.0326 55.5156C58.5695 55.5156 57.1064 55.5015 55.6434 55.5203C54.5842 55.5344 54.0253 56.0885 53.9948 57.15C53.969 58.0564 53.9666 58.9652 53.9948 59.8716C54.03 60.9166 54.6054 61.4943 55.6622 61.5013C58.5601 61.5201 61.4557 61.5225 64.3537 61.5013C65.4128 61.4943 65.9529 60.9307 65.9858 59.8528C66.014 58.9464 66.0163 58.0376 65.9858 57.1312H65.9882ZM55.4931 31.427C55.3991 30.5276 54.7486 29.9828 53.9009 30.0321C53.1517 30.0744 52.5904 30.6239 52.5341 31.3683C52.4683 32.2207 53.0014 32.8852 53.8962 32.9862C54.3823 33.0402 54.8801 32.9956 55.5025 32.9956C55.5025 32.4038 55.5447 31.9107 55.4931 31.4246V31.427ZM61.451 52.4675V36.0883H58.546V52.4675H61.451ZM64.5204 33.052C65.2367 33.005 65.8261 33.0355 66.3874 32.9158C67.1553 32.7514 67.6109 31.9929 67.4818 31.2579C67.3573 30.5346 66.7396 30.0227 65.9975 30.025C65.2155 30.0274 64.6002 30.5581 64.5274 31.3542C64.4805 31.8684 64.5181 32.3921 64.5181 33.0496L64.5204 33.052ZM73.7262 21.2942C68.9918 14.0004 60.1007 11.3915 52.7924 15.3272C41.008 21.6746 40.738 37.3845 50.2538 44.4528C52.0245 45.7679 53.5486 47.4187 55.1901 48.9099C55.2606 48.9756 55.3639 49.0061 55.4696 49.0625V36.0343C53.9666 36.0343 52.4918 36.0578 51.2659 35.0644C50.0119 34.05 49.4318 32.749 49.5375 31.1381C49.6831 28.9307 51.4937 27.1508 53.7177 27.0286C54.8778 26.9652 55.9181 27.2729 56.8246 28.0008C58.4145 29.2759 58.6235 31.0512 58.5131 32.9627H61.4534C61.5097 32.1103 61.4675 31.3072 61.6271 30.5464C62.125 28.191 64.2315 26.768 66.6645 27.0615C68.8838 27.3292 70.5652 29.3581 70.4924 31.6876C70.4196 33.9819 68.5832 35.8394 66.2066 35.9991C65.6641 36.0343 65.1169 36.0037 64.5721 36.0037V48.6891C65.3001 48.0621 66.1737 47.2849 67.0755 46.5428C68.0007 45.7796 68.9378 45.0282 69.9006 44.3119C76.7157 39.2514 78.6907 28.9354 73.7262 21.2918V21.2942ZM16.6972 26.7938C16.9203 26.0705 17.0519 25.3168 17.2374 24.5817C18.1674 20.9043 19.8018 17.6121 22.6575 15.043C24.3789 13.4932 26.4197 12.4811 28.653 11.8752C29.4397 11.6615 29.9376 11.213 30.0245 10.431C30.0362 9.36492 29.1133 8.60173 28.0988 8.94693C26.5559 9.47059 24.9707 9.98017 23.5664 10.7809C17.6131 14.1718 15.0533 19.7725 13.8322 26.1809C13.6795 26.977 14.2314 27.6533 14.9782 27.7965C15.7649 27.9468 16.4554 27.5781 16.6972 26.7962V26.7938ZM79.2849 8.65809C78.0872 9.77117 76.9059 10.9124 75.8327 12.1453C75.5486 12.4717 75.5086 13.2349 75.6824 13.667C75.8539 14.0943 76.4386 14.3574 76.8097 14.665C77.2887 14.4654 77.6457 14.4137 77.8594 14.2094C79.0407 13.0775 80.2196 11.9386 81.3351 10.7434C81.8775 10.1633 81.7531 9.29212 81.2012 8.76141C80.6728 8.25419 79.8391 8.14382 79.2849 8.66044V8.65809ZM40.9329 8.83656C40.6534 8.5712 40.2425 8.4444 39.7094 8.15086C39.2867 8.52424 38.7817 8.82247 38.4929 9.26394C38.1101 9.84632 38.3637 10.4592 38.8264 10.9336C39.8268 11.9598 40.8343 12.9766 41.8605 13.9746C42.6073 14.7002 43.4856 14.7119 44.1103 14.0544C44.6927 13.4415 44.6598 12.568 43.9788 11.8658C42.9807 10.8373 41.9732 9.82049 40.9329 8.83656ZM88.54 28.5104C87.0182 28.4658 85.4941 28.4658 83.9723 28.5104C83.0846 28.5362 82.4717 29.2501 82.5116 30.065C82.5515 30.8563 83.1574 31.4457 84.017 31.4786C84.7473 31.5068 85.4824 31.4833 86.2127 31.4833C86.9149 31.4857 87.6194 31.4857 88.3216 31.4857C88.3803 31.4857 88.439 31.4857 88.4977 31.481C89.3666 31.4223 89.9608 30.8587 89.9984 30.0626C90.0359 29.2501 89.423 28.5339 88.54 28.5081V28.5104ZM31.403 28.5198C30.5506 28.548 30.0104 29.2313 30.0339 30.0462C30.055 30.8681 30.6163 31.4387 31.497 31.4763C32.2273 31.5068 32.96 31.4833 33.6927 31.481C34.4841 31.481 35.2756 31.5091 36.0623 31.4716C36.8936 31.434 37.4361 30.8704 37.476 30.0837C37.516 29.2666 36.9876 28.5574 36.1445 28.5245C34.5663 28.4634 32.9835 28.4658 31.403 28.5198ZM12.0028 31.4645C11.9816 32.2653 12.6791 32.9909 13.4846 33.0027C14.3019 33.0144 14.9782 32.331 14.9759 31.4951C14.9759 30.6403 14.33 30.0133 13.4729 30.0274C12.6744 30.0391 12.0262 30.6755 12.0028 31.4645Z" fill="#0072DA"></path></svg>
                <div class="mission-value-label">Openness</div>
            </div>
        </div>
    </div>
</section>

<!-- Leadership Team Section -->
<section class="leadership-section" id="leadership-team">
    <div class="leadership-bg-shape leadership-bg-shape-blue"></div>
    <div class="leadership-bg-shape leadership-bg-shape-orange"></div>
    <div class="leadership-container">
        <h2 class="leadership-title">Leadership team</h2>
        <div class="leadership-desc">
            Krystelis was founded in 2022 by Dr Pooja Phogat and Dr Stuart Donald, pharmaceutical industry executives with decades of experience gained within pharma companies and service delivery organisations.<br />
            Their vision is for Krystelis to become a leading provider of high-quality, cost-effective services to the global life sciences industry. They are equally enthusiastic about exceeding the expectations of their clients and employees. Pooja and Stuart constantly strive to ensure that Krystelis is not only great to work with, but also a great place to work.
        </div>
    </div>
</section>

<!-- Dr. Pooja Phogat Profile Card Section -->
<section class="profile-section">
    <div class="profile-card">
        <div class="profile-img-wrap">
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Dr-Pooja-Phogat.png" alt="Dr. Pooja Phogat" />
        </div>
        <div class="profile-content">
            <h2 class="profile-name">Dr. Pooja Phogat</h2>
            <div class="profile-title">CO FOUNDER AND CO-CEO</div>
            <div class="profile-desc">
                Pooja holds a First-Class Honours degree and a PhD in Microbiology. Her experience spans academic research and operational, management, and business development positions in organisations delivering services to the pharmaceutical industry worldwide, including leadership at board level. She is a globally recognised subject matter expert in the field of medical writing and clinical trial transparency.<br><br>
                Pooja is driven professionally to build services that exceed client expectations and to deliver these services through a high level of customer focus and collaboration.<br><br>
                Pooja lives in Delhi, India. She has a passion for traveling and experiencing diverse cultures. She enjoys experimenting with cooking dishes from various global cuisines. To stay fit, she practices yoga regularly and enjoys walking.<br><br>
                Pooja is responsible for Krystelis' growth strategy.
            </div>
            <hr class="profile-divider" />
            <a href="#" class="profile-linkedin-btn" target="https://www.linkedin.com/in/poojaphogat/">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Linkedin.png" alt="LinkedIn" class="profile-linkedin-icon" />
                LET'S CONNECT
            </a>
        </div>
    </div>
</section>

<!-- Dr. Stuart Donald Profile Card Section -->
<section class="profile-section">
    <div class="profile-card">
        <div class="profile-img-wrap">
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Dr-Stuart-Donald.png" alt="Dr. Stuart Donald" />
        </div>
        <div class="profile-content">
            <h2 class="profile-name">Dr. Stuart Donald</h2>
            <div class="profile-title">CO FOUNDER AND CO-CEO</div>
            <div class="profile-desc">
                Stuart holds a First-Class Honours degree in Pharmacology and a PhD in Cardiovascular Pharmacology. His extensive experience in the UK and USA spans academic research, pharmaceutical project and portfolio management, management consultancy, and executive roles in contract research organisations, reaching board level.<br><br>
                Stuart is passionate about building environments where individuals and teams can thrive and achieve their full potential.<br><br>
                Residing near Reading, UK, Stuart also supports his local community as a volunteer and trustee of Ridgeline Trust, a local charity that provides social and therapeutic horticulture for people facing mental or physical health challenges and other special needs. Learn more at <a href="https://ridgelinetrust.org.uk/" target="_blank" rel="noopener">https://ridgelinetrust.org.uk/</a>.<br><br>
                Stuart leads Krystelis' business operations, driving success and innovation within the company.
            </div>
            <hr class="profile-divider" />
            <a href="#" class="profile-linkedin-btn" target="https://www.linkedin.com/in/stuart-donald-6374684/">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Linkedin.png" alt="LinkedIn" class="profile-linkedin-icon" />
                LET'S CONNECT
            </a>
        </div>
    </div>
</section>

<!-- Shalini Dwivedi Profile Card Section -->
<section class="profile-section">
    <div class="profile-card">
        <div class="profile-img-wrap">
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Shalini Dwivedi.jpg" alt="Shalini Dwivedi" />
        </div>
        <div class="profile-content">
            <h2 class="profile-name">Shalini Dwivedi</h2>
            <div class="profile-title">HEAD OF MEDICAL WRITING AND TRIAL TRANSPARENCY</div>
            <div class="profile-desc">
                Shalini holds a First-Class Master's degree in Pharmaceutical Sciences, with a specialisation in Pharmacognosy and Phytochemistry. Shalini has expertise in regulatory, disclosure, and publication writing and in training clinical research professionals in various aspects of clinical research.<br><br>
                Shalini is passionate about clinical research data integrity and ethics. She is recognised within the industry as a subject matter expert for the redaction and anonymisation of clinical trial documents.<br><br>
                Shalini lives in Gurugram, India. She is a classical music enthusiast and a vocalist in MPGM Music Academy projects (<a href="http://www.mpgmmusic.com" target="_blank" rel="noopener">www.mpgmmusic.com</a>).<br><br>
                Shalini writes poetry in Hindi, and many of her poems have been composed as classical songs.<br><br>
                At Krystelis, Shalini is focused on operational delivery of Medical Writing and Clinical Trial Transparency projects, resourcing, and people management.
            </div>
            <hr class="profile-divider" />
            <a href="#" class="profile-linkedin-btn" target="https://www.linkedin.com/in/shalini-dwivedi-494a4014/">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Linkedin.png" alt="LinkedIn" class="profile-linkedin-icon" />
                LET'S CONNECT
            </a>
        </div>
    </div>
</section>

<!-- Vidhi Vashisht Profile Card Section -->
<section class="profile-section">
    <div class="profile-card">
        <div class="profile-img-wrap">
            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Vidhi Vashisht.png" alt="Vidhi Vashisht" />
        </div>
        <div class="profile-content">
            <h2 class="profile-name">Vidhi Vashisht</h2>
            <div class="profile-title">HEAD OF PLAIN LANGUAGE SERVICES</div>
            <div class="profile-desc">
                Vidhi holds a First-Class Master's degree in Pharmacy, specialising in Medicinal Chemistry, and is a gold medallist. Vidhi has spent more than a decade working in clinical research and has expertise in clinical trial transparency and plain language writing. She has a deep understanding of clinical trial regulations. Vidhi's strengths include ensuring excellence in project delivery and people management.<br><br>
                Vidhi is passionate about patient communication and patient engagement and creating a positive and thriving environment for Krystelis teams. She is a recognized subject matter expert on plain language communications and regularly speaks and publishes on global platforms.<br><br>
                Vidhi lives in Gurugram, India. When not at work, she is engaged in reading, writing, and gardening. She is also a <a href="#" style="color: #8e44ad; text-decoration: underline;">Kathak</a> dancer and a Hindustani music (vocal) enthusiast.<br><br>
                At Krystelis, Vidhi leads plain language writing services.
            </div>
            <hr class="profile-divider" />
            <a href="#" class="profile-linkedin-btn" target="https://www.linkedin.com/in/vidhi-vashisht-a0721864/%20">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Linkedin.png" alt="LinkedIn" class="profile-linkedin-icon" />
                LET'S CONNECT
            </a>
        </div>
    </div>
</section>

<!-- Our Locations Section -->
<section class="locations-section">
    <div class="locations-title">Our locations</div>
    <div class="locations-map-wrap">
        <img class="locations-map-img" src="<?php echo get_template_directory_uri(); ?>/assets/images/Location-map-bg.png" alt="World map" />
        <!-- You can add marker/flag overlays here if needed -->
    </div>
</section>

<!-- Company News Section -->
<section class="news-section">
    <div class="news-title">Company news</div>
    <div class="news-subtitle">In this section, we share the latest updates about our company.</div>
    <div class="news-cards-row">
        <!-- News Card 1 -->
        <div class="news-card">
            <div class="news-card-img-wrap" style="background-color: #FFD24C;">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Krystelis-Launch-News-Image-970x728.png" alt="Krystelis-Phesi Partnership Announcement" />
            </div>
            <div class="news-card-info">
                <div class="news-card-date">September 13, 2023</div>
                <a href="http://localhost/wordpress/company-news/krystelis-phesi-partnership-announcement/" class="news-card-title">Krystelis-Phesi Partnership Announcement</a>
            </div>
        </div>
        <!-- News Card 2 -->
        <div class="news-card">
            <div class="news-card-img-wrap">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/Untitled-design-1-970x728.png" alt="Krystelis: Making Clinical Research Crystal Clear" />
            </div>
            <div class="news-card-info">
                <div class="news-card-date">July 1, 2022</div>
                <a href="http://localhost/wordpress/company-news/krystelis-making-clinical-research-crystal-clear/" class="news-card-title">Krystelis: Making Clinical Research Crystal Clear</a>
            </div>
        </div>
    </div>
</section>

<!-- Get In Touch CTA Section -->
<section class="cta-section">
    <div class="cta-bg-shape cta-bg-shape-orange"></div>
    <div class="cta-bg-shape cta-bg-shape-blue"></div>
    <div class="cta-container">
        <div class="cta-content">
            <div class="cta-subtitle">INTERESTED IN LEARNING MORE?</div>
            <div class="cta-title">Get In Touch</div>
            <a href="/contact" class="cta-btn">CONTACT US</a>
        </div>
    </div>
</section>

<style>
.site-main {
    padding: 5px 0;
    min-height: 60vh;
    width: 100%;
}
.aboutus-hero-container {
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    justify-content: space-between;
    background: #fff;
    /* padding: 10px 0 0 0; */
    min-height: 600px;
}
.aboutus-hero-left {
    flex: 1 1 50%;
    padding: 40px 40px 0 3vw;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}
.aboutus-tagline {
    color: #FF6B35;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(-60px);
    transition: opacity 0.7s cubic-bezier(0.23, 1, 0.32, 1), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.aboutus-tagline.slide-in {
    opacity: 1;
    transform: translateY(0);
}
.aboutus-headline {
    color: #0074D9;
    font-size: 3.1rem;
    font-weight: 700;
    margin-bottom: 30px;
    line-height: 1.1;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 0.7s cubic-bezier(0.23, 1, 0.32, 1), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}

.aboutus-headline.slide-in {
    opacity: 1;
    transform: translateY(0);
}

.aboutus-description {
    color: #525252;
    font-size: 1.35rem;
    font-family: 'Georgia', serif;
    margin-bottom: 40px;
    line-height: 1.6;
}
.aboutus-icon-row {
    display: flex;
    gap: 40px;
    margin-top: auto;
    padding-bottom: 30px;
}
.aboutus-icon-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1 1 0;
    opacity: 0;
    transform: translateX(-60px);
    transition: opacity 0.7s cubic-bezier(0.23, 1, 0.32, 1), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.aboutus-icon-box.slide-in {
    opacity: 1;
    transform: translateX(0);
}
.aboutus-icon-box img {
    width: 70px;
    height: 70px;
    object-fit: contain;
}
.aboutus-icon-label {
    color: #FF6B35;
    font-size: 1.3rem;
    font-weight: 700;
    margin-top: 10px;
}
.aboutus-hero-right {
    flex: 1 1 45%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 5vw 0 0;
}
.aboutus-hero-right img {
    max-width: 100%;
    border-radius: 20px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.08);
    opacity: 0;
    transform: scale(0.85);
    transition: opacity 0.7s cubic-bezier(0.23, 1, 0.32, 1), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.aboutus-hero-right img.zoom-in {
    opacity: 1;
    transform: scale(1);
}

@media (max-width: 769px) {
    .aboutus-hero-container {
        flex-direction: column;
        padding: 20px 0 0 0;
    }
    .aboutus-hero-left, .aboutus-hero-right {
        flex: 1 1 100%;
        padding: 20px 5vw 0 5vw;
    }
    .aboutus-icon-row {
        gap: 20px;
        padding-bottom: 10px;
    }
    .aboutus-headline {
        font-size: 2.2rem;
    }
    .aboutus-tagline {
        font-size: 1.2rem;
    }
}
.mission-section {
    background: #fafbfc;
    padding: 60px 0 40px 0;
    width: 100%;
}
.mission-container {
    /* max-width: 1200px; */
    margin: 0 auto;
    text-align: center;
    padding: 0 20px;
}
.mission-title {
    color: #0074D9;
    font-family: 'Maven Pro', sans-serif;
    letter-spacing: 2px;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 18px;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 0.7s cubic-bezier(0.23, 1, 0.32, 1), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.mission-title.slide-in {
    opacity: 1;
    transform: translateY(0);
}
.mission-desc {
    color: #525252;
    font-size: 1.5rem;
    font-family: 'Georgia', serif;
    margin-bottom: 18px;
    line-height: 1.3;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 0.7s cubic-bezier(0.23, 1, 0.32, 1), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.mission-desc.slide-in {
    opacity: 1;
    transform: translateY(0);
}
.mission-values-title {
    color: #4ab0f7;
    font-size: 2rem;
    font-weight: 600;
    letter-spacing: 2px;
    margin-bottom: 38px;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 1.2s cubic-bezier(0.23, 1, 0.32, 1), transform 1.2s cubic-bezier(0.23, 1, 0.32, 1);
}
.mission-values-title.slide-in {
    opacity: 1;
    transform: translateY(0);
}

.mission-values-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 18px;
    flex-wrap: wrap;
}
.mission-value-box {
    flex: 1 1 140px;
    min-width: 120px;
    max-width: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 10px;
    opacity: 0;
    transform: translateX(-60px);
    transition: opacity 0.8s cubic-bezier(0.23, 1, 0.32, 1), transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}
.mission-value-box.slide-in {
    opacity: 1;
    transform: translateX(0);
}
.mission-value-box img {
    width: 90px;
    height: 90px;
    object-fit: contain;
    margin-bottom: 10px;
}
.mission-value-label {
    color: #FF6B35;
    font-size: 1.25rem;
    font-weight: 700;
    margin-top: 6px;
}
/* Default styles for desktop */
.desktop-only {
  display: block;
}
.mobile-only {
  display: none;
}

/* Mobile styles */
@media (max-width: 769px) {
  .desktop-only {
    display: none;
  }
  .mobile-only {
    display: block;
  }
}

/* Responsive styles for mobile-only image and text */
@media (max-width: 769px) {
  /* Responsive mobile-only image */
  .mobile-only img {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.08);
    margin: 0 auto 20px auto;
  }
  
  /* Responsive text for mobile/tablet */
  .aboutus-headline {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
  }
  
  .aboutus-tagline {
    font-size: clamp(1rem, 2.5vw, 1.3rem);
  }
  
  .aboutus-description {
    font-size: clamp(0.9rem, 2vw, 1.1rem);
  }
}

/* Desktop responsive styles */
/* @media (min-width: 769px) {
  /* Responsive text for desktop */
  /* .aboutus-headline {
    font-size: clamp(2.5rem, 3vw, 3rem);
  }
  
  .aboutus-tagline {
    font-size: clamp(1.3rem, 1.5vw, 1.5rem);
  }
  
  .aboutus-description {
    font-size: clamp(1.1rem, 1.2vw, 1.3rem);
  }
} */


@media (max-width: 900px) {
    .mission-title {
        font-size: 2.1rem;
    }
    .mission-desc {
        font-size: 1.1rem;
    }
    .mission-values-title {
        font-size: 1.2rem;
    }
    .mission-values-row {
        gap: 10px;
    }
    .mission-value-box img {
        width: 60px;
        height: 60px;
    }
}
@media (max-width: 700px) {
    .mission-values-row {
        flex-wrap: wrap;
        justify-content: center;
    }
    .mission-value-box {
        min-width: 100px;
        max-width: 140px;
        margin: 10px 5px;
    }
}
.leadership-section {
    position: relative;
    background: #fff;
    padding: 90px 0 60px 0;
    overflow: hidden;
    width: 100%;
    z-index: 1;
}
.leadership-container {
    /* max-width: 1300px; */
    margin: 0 auto;
    text-align: center;
    padding: 0 30px;
    position: relative;
    z-index: 2;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 0.7s cubic-bezier(0.23, 1, 0.32, 1), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.leadership-container.slide-in {
    opacity: 1;
    transform: translateY(0);
}
.leadership-title {
    color: #0074D9;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 30px;
}
.leadership-desc {
    color: #525252;
    font-size: 1.5rem;
    font-family: 'Georgia', serif;
    line-height: 1.4;
    /* max-width: 1200px; */
    margin: 0 auto;
}
.leadership-bg-shape {
    position: absolute;
    z-index: 1;
    opacity: 0.18;
}
.leadership-bg-shape-blue {
    top: 0;
    right: 7vw;
    width: 220px;
    height: 120px;
    background: url('<?php echo get_template_directory_uri(); ?>/assets/images/Bg-Shape-blue.png') no-repeat center/contain;
}
.leadership-bg-shape-orange {
    top: 120px;
    left: 18vw;
    width: 120px;
    height: 120px;
    background: url('<?php echo get_template_directory_uri(); ?>/assets/images/Bg-Shape-Orange.png') no-repeat center/contain;
}
@media (max-width: 900px) {
    .leadership-title {
        font-size: 2.1rem;
    }
    .leadership-desc {
        font-size: 1.1rem;
    }
    .leadership-bg-shape-blue {
        width: 120px;
        height: 60px;
        right: 2vw;
    }
    .leadership-bg-shape-orange {
        width: 60px;
        height: 60px;
        left: 5vw;
        top: 80px;
    }
}
@media (max-width: 600px) {
    .leadership-section {
        padding: 50px 0 30px 0;
    }
    .leadership-title {
        font-size: 1.3rem;
    }
    .leadership-desc {
        font-size: 0.95rem;
    }
}
.profile-section {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 60px 0 40px 0;
    background: #fafbfc;
}
.profile-card {
    display: flex;
    background: #fff;
    border-radius: 28px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    max-width: 1200px;
    width: 100%;
    padding: 40px 40px 30px 40px;
    gap: 40px;
    align-items: flex-start;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 0.7s cubic-bezier(0.23, 1, 0.32, 1), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.profile-card.slide-in {
    opacity: 1;
    transform: translateY(0);
}
.profile-img-wrap {
    flex: 0 0 260px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
}
.profile-img-wrap img {
    width: 260px;
    height: 300px;
    object-fit: cover;
    border-radius: 20px;
    background: #f4f4f4;
}
.profile-content {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}
.profile-name {
    color: #0074D9;
    font-size: 2.3rem;
    font-weight: 700;
    margin-bottom: 8px;
}
.profile-title {
    color: #FF6B35;
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 3px;
    margin-bottom: 22px;
}
.profile-desc {
    color: #525252;
    font-size: 1.25rem;
    font-family: 'Georgia', serif;
    line-height: 1.6;
    margin-bottom: 18px;
}
.profile-divider {
    border: none;
    border-top: 1px solid #eee;
    margin: 18px 0 18px 0;
}
.profile-linkedin-btn {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(90deg, #2196f3 0%, #0074D9 100%);
    color: #fff;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 12px;
    padding: 10px 28px 10px 18px;
    text-decoration: none;
    box-shadow: 0 4px 16px rgba(33,150,243,0.10);
    transition: background 0.2s, box-shadow 0.2s;
    margin-top: 8px;
    border: none;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.profile-linkedin-btn.slide-in {
    opacity: 1;
    transform: translateY(0);
}

.profile-linkedin-btn:hover {
    background: linear-gradient(90deg, #0074D9 0%, #2196f3 100%);
    box-shadow: 0 6px 24px rgba(33,150,243,0.18);
}
.profile-linkedin-icon {
    width: 32px;
    height: 32px;
    margin-right: 10px;
}

/* Fix overlapping issues for very small screens (200px - 480px) */
@media (max-width: 480px) {
    /* Hero section fixes */
    .aboutus-hero-container {
        padding: 15px 10px;
        min-height: auto;
    }
    
    .aboutus-hero-left {
        padding: 0;
        margin-bottom: 20px;
    }
    
    .aboutus-headline {
        font-size: clamp(1.5rem, 6vw, 2rem);
        margin-bottom: 15px;
        line-height: 1.2;
    }
    
    .aboutus-tagline {
        font-size: clamp(0.9rem, 4vw, 1.1rem);
        margin-bottom: 15px;
    }
    
    .aboutus-description {
        font-size: clamp(0.8rem, 3.5vw, 1rem);
        margin-bottom: 20px;
        line-height: 1.4;
    }
    
    .aboutus-icon-row {
        gap: 15px;
        margin-top: 20px;
        padding-bottom: 15px;
    }
    
    .aboutus-icon-box img {
        width: 50px;
        height: 50px;
    }
    
    .aboutus-icon-label {
        font-size: 0.9rem;
        margin-top: 5px;
    }
    
    /* Mission section fixes */
    .mission-section {
        padding: 30px 0 20px 0;
    }
    
    .mission-title {
        font-size: clamp(1.8rem, 8vw, 2.5rem);
        margin-bottom: 15px;
    }
    
    .mission-desc {
        font-size: clamp(0.9rem, 4vw, 1.2rem);
        margin-bottom: 15px;
        padding: 0 10px;
    }
    
    .mission-values-title {
        font-size: clamp(1.2rem, 5vw, 1.5rem);
        margin-bottom: 25px;
    }
    
    .mission-values-row {
        gap: 10px;
        justify-content: center;
    }
    
    .mission-value-box {
        flex: 1 1 100px;
        min-width: 80px;
        max-width: 120px;
        margin: 0 5px;
    }
    
    .mission-value-box img {
        width: 60px;
        height: 60px;
    }
    
    .mission-value-label {
        font-size: 0.8rem;
        margin-top: 5px;
    }
    
    /* Profile section fixes */
    .profile-section {
        padding: 20px 0 15px 0;
    }
    
    .profile-card {
        padding: 15px 10px;
        border-radius: 12px;
        gap: 15px;
    }
    
    .profile-img-wrap {
        flex: 0 0 100px;
    }
    
    .profile-img-wrap img {
        width: 100px;
        height: 100px;
        border-radius: 10px;
    }
    
    .profile-name {
        font-size: 1.2rem;
        margin-bottom: 5px;
    }
    
    .profile-title {
        font-size: 0.8rem;
        margin-bottom: 15px;
    }
    
    .profile-desc {
        font-size: 0.8rem;
        margin-bottom: 15px;
        line-height: 1.4;
    }
    
    .profile-linkedin-btn {
        font-size: 0.8rem;
        padding: 6px 12px 6px 10px;
        border-radius: 6px;
        margin-top: 10px;
    }
    
    .profile-linkedin-icon {
        width: 18px;
        height: 18px;
        margin-right: 5px;
    }
    
    /* News section fixes */
    .news-section {
        padding: 20px 0 15px 0;
    }
    
    .news-title {
        font-size: clamp(1rem, 4vw, 1.2rem);
        padding-top: 0;
        margin-bottom: 10px;
    }
    
    .news-subtitle {
        font-size: clamp(0.7rem, 3vw, 0.9rem);
        margin-bottom: 15px;
        padding: 0 10px;
    }
    
    .news-cards-row {
        gap: 15px;
        padding: 0 10px;
    }
    
    .news-card {
        width: 100%;
        max-width: 300px;
        border-radius: 10px;
    }
    
    .news-card-info {
        left: 5px;
        right: 5px;
        padding: 8px 6px 6px 8px;
        border-radius: 8px;
    }
    
    .news-card-title {
        font-size: 0.8rem;
        margin-bottom: 5px;
    }
    
    .news-card-date {
        font-size: 0.6rem;
    }
    
    /* CTA section fixes */
    .cta-section {
        margin: 2rem 0;
    }
    
    .cta-container {
        margin: 2rem auto;
    }
    
    .cta-content {
        max-width: 98vw;
        padding: 15px 0;
        border-radius: 16px;
    }
    
    .cta-title {
        font-size: clamp(1rem, 4vw, 1.2rem);
        margin-bottom: 20px;
    }
    
    .cta-subtitle {
        font-size: clamp(0.7rem, 3vw, 0.9rem);
        margin-bottom: 15px;
    }
    
    .cta-btn {
        font-size: 0.8rem;
        padding: 6px 12px;
        border-radius: 6px;
    }
    
    .cta-bg-shape-orange, 
    .cta-bg-shape-blue {
        width: 80px;
        height: 60px;
    }
    
    /* Mobile-only image fixes */
    .mobile-only img {
        max-width: 100%;
        margin: 0 auto 15px auto;
    }
}

@media (max-width: 900px) {
    .profile-card {
        flex-direction: column;
        align-items: center;
        padding: 30px 10px 20px 10px;
        gap: 20px;
        text-align: center;
    }
    .profile-img-wrap {
        flex: 0 0 180px;
    }
    .profile-img-wrap img {
        width: 180px;
        height: 180px;
    }
    .profile-content {
        width: 100%;
        align-items: center;
    }
    .profile-name {
        font-size: 1.5rem;
        text-align: center;
    }
    .profile-desc {
        font-size: 1rem;
        text-align: center;
        padding: 0 15px;
    }
    .profile-title {
        text-align: center;
        padding: 0 10px;
    }
    .profile-linkedin-btn {
        margin: 20px auto;
    }
}
@media (max-width: 600px) {
    .profile-section {
        padding: 30px 0 20px 0;
    }
    .profile-card {
        padding: 10px 2vw 10px 2vw;
        border-radius: 16px;
    }
    .profile-img-wrap img {
        width: 120px;
        height: 120px;
        border-radius: 12px;
    }
    .profile-linkedin-btn {
        font-size: 1rem;
        padding: 8px 16px 8px 12px;
        border-radius: 8px;
    }
    .profile-linkedin-icon {
        width: 22px;
        height: 22px;
        margin-right: 7px;
    }
}
.locations-section {
    width: 100%;
    background: #fff;
    padding: 0 0 0 0;
    margin-top: 0;
    position: relative;
    overflow: hidden;
}
.locations-title {
    text-align: center;
    color: #0074D9;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 18px;
    margin-top: 0;
    padding-top: 30px;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 0.7s cubic-bezier(0.23, 1, 0.32, 1), transform 0.7s cubic-bezier(0.23, 1, 0.32, 1);
}
.locations-title.slide-in {
    opacity: 1;
    transform: translateY(0);
}
.locations-map-wrap {
    position: relative;
    width: 100%;
    max-width: 1600px;
    margin: 0 auto;
    min-height: 500px;
}
.locations-map-img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 0;
}
@media (max-width: 900px) {
    .locations-title {
        font-size: 2rem;
        padding-top: 18px;
    }
    .locations-map-wrap {
        min-height: 300px;
    }
}
@media (max-width: 600px) {
    /* Global mobile centering */
    .aboutus-hero-left,
    .mission-section,
    .leadership-section,
    .profile-section,
    .news-section,
    .cta-section {
        text-align: center !important;
    }
    
    /* Specific mobile adjustments */
    .aboutus-hero-container {
        padding: 20px 15px;
    }
    .aboutus-hero-left {
        align-items: center;
    }
    .aboutus-description,
    .mission-desc,
    .leadership-desc,
    .profile-desc {
        padding: 0 15px;
    }
    
    .profile-content {
        align-items: center;
    }
    .profile-linkedin-btn {
        margin: 20px auto;
    }
    
    .news-card-info {
        text-align: center;
    }
    
    .locations-title {
        font-size: 1.2rem;
        padding-top: 10px;
    }
    .locations-map-wrap {
        min-height: 180px;
    }
    
    /* Font size adjustments */
    .aboutus-headline {
        font-size: 1.8rem;
        line-height: 1.3;
    }
    .mission-values-title,
    .news-title,
    .cta-title {
        font-size: 1.6rem;
    }
}
.news-section {
    width: 100%;
    background: #fff;
    padding: 0 0 60px 0;
    margin-top: 0;
    position: relative;
    overflow: hidden;
}
.news-title {
    text-align: center;
    color: #0074D9;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 8px;
    margin-top: 0;
    padding-top: 10px;
    opacity: 0;
    transform: translateY(-60px);
    transition: opacity 1.1s cubic-bezier(0.23, 1, 0.32, 1), transform 1.1s cubic-bezier(0.23, 1, 0.32, 1);
}
.news-title.slide-in {
    opacity: 1;
    transform: translateY(0);
}
.news-subtitle {
    text-align: center;
    color: #525252;
    font-size: 1.3rem;
    font-family: 'Georgia', serif;
    margin-bottom: 38px;
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 0.9s cubic-bezier(0.23, 1, 0.32, 1), transform 0.9s cubic-bezier(0.23, 1, 0.32, 1);
}
.news-subtitle.slide-in {
    opacity: 1;
    transform: translateY(0);
}
.news-cards-row {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 48px;
    flex-wrap: wrap;
    /* max-width: 1200px; */
    margin: 0 auto;
}
.news-card {
    background: none;
    border-radius: 18px;
    box-shadow: none;
    width: 420px;
    max-width: 95vw;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
    opacity: 0;
    transform: translateX(60px);
    transition: opacity 0.8s cubic-bezier(0.23, 1, 0.32, 1), transform 0.8s cubic-bezier(0.23, 1, 0.32, 1);
}
.news-card.slide-in {
    opacity: 1;
    transform: translateX(0);
}

.news-card:hover {
    transform: translateY(-10px);
}

.news-card-img-wrap {
    width: 100%;
    aspect-ratio: 4/3;
    border-radius: 18px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.news-card-img-wrap img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.news-card:hover .news-card-img-wrap img {
    transform: scale(1.05);
}

.news-card-info {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 6px 32px rgba(0,0,0,0.10);
    padding: 18px 24px 14px 24px;
    position: absolute;
    left: 18px;
    right: 18px;
    bottom: -38px;
    z-index: 2;
    min-width: 220px;
    transition: all 0.3s ease;
}

.news-card:hover .news-card-info {
    background: #0072DA;
    box-shadow: 0 15px 40px rgba(0, 114, 218, 0.3);
}

.news-card-date {
    color: #FF6B35;
    font-size: 1.05rem;
    margin-bottom: 4px;
    font-family: 'Georgia', serif;
    transition: all 0.3s ease;
}

.news-card:hover .news-card-date {
    color: #FFFFFF;
}

.news-card-title {
    color: #0074D9;
    font-size: 1.18rem;
    font-weight: 700;
    text-decoration: none;
    transition: all 0.3s ease;
}

.news-card:hover .news-card-title {
    color: white;
}
/* 
.news-card-title:hover {
    color: #FF6B35;
} */

/* .news-card:hover .news-card-title:hover {
    color: #FF6B35;
} */

@media (max-width: 769px) {
    /* Center all content on mobile */
    .aboutus-hero-container {
        display: flex;
        flex-direction: column;
    }
    .aboutus-hero-right {
        order: 2;
        margin-top: 2rem;
    }
    .aboutus-hero-left,
    .mission-section,
    .leadership-section,
    .profile-section,
    .news-section,
    .cta-section {
        text-align: center;
    }

    .news-title {
        font-size: 2rem;
        padding-top: 8px;
    }
    .news-subtitle {
        font-size: 1rem;
        margin-bottom: 18px;
        padding: 0 15px;
    }
    .news-cards-row {
        gap: 5rem;
        justify-content: center;
    }
    .news-card {
        width: 95vw;
        max-width: 420px;
        align-items: center;
    }
    .news-card-info {
        left: 8px;
        right: 8px;
        padding: 12px 10px 10px 14px;
        text-align: center;
    }
}
@media (max-width: 600px) {
    .news-title {
        font-size: 1.2rem;
        padding-top: 4px;
    }
    .news-subtitle {
        font-size: 0.95rem;
    }
    .news-card-info {
        font-size: 0.95rem;
        left: 2px;
        right: 2px;
        padding: 8px 4px 8px 8px;
    }
}
.cta-section {
    width: 100%;
    background: #fafbfc;
    /* padding: 0 0 80px 0; */
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transform: translateY(-80px);
    transition: opacity 1.5s cubic-bezier(0.23, 1, 0.32, 1), transform 1.5s cubic-bezier(0.23, 1, 0.32, 1);
}
.cta-section.slide-in {
    opacity: 1;
    transform: translateY(0);
}
.cta-container {
    width: 100%;
    max-width: 1300px;
    margin: 4rem auto;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 2;
}
.cta-content {
    width: 100%;
    max-width: 70vw;
    min-width: 320px;
    background: linear-gradient(180deg, #4ab0f7 0%, #0074D9 100%);
    border-radius: 24px;
    padding: 70px 0 70px 0;
    text-align: center;
    color: #fff;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    position: relative;
    z-index: 2;
}
.cta-subtitle {
    font-size: 1.5rem;
    font-family: 'Georgia', serif;
    letter-spacing: 0.25em;
    margin-bottom: 24px;
    color: #fff;
    opacity: 0.95;
}
.cta-title {
    font-size: 3.2rem;
    font-weight: 700;
    margin-bottom: 38px;
    color: #fff;
}
.cta-btn {
    display: inline-block;
    background: white;
    color:rgb(71, 67, 67);
    padding: 1.5rem 2rem;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 1.3rem;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    box-shadow: 0 8px 20px 0072DA;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 10px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translateY(100px);
    transition: opacity 2s cubic-bezier(0.23, 1, 0.32, 1), transform 2s cubic-bezier(0.23, 1, 0.32, 1);
}
.cta-btn.slide-in {
    opacity: 1;
    transform: translateY(0);
}

.cta-btn:hover {
    background: #ffffff;
    color: #0072DA;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 136, 255, 0.4); /* Brighter effect */
    text-decoration: none;
    padding: 25px 40px 25px 40px;
    box-shadow: 0px 0px 20px 0px #FFFFFF;
    text-shadow: 0px 0px 0px rgba(0, 0, 0, 0.3);
}
.cta-bg-shape {
    position: absolute;
    z-index: 1;
    opacity: 0.18;
    pointer-events: none;
}
.cta-bg-shape-orange {
    left: 0;
    bottom: 0;
    width: 220px;
    height: 180px;
    background: url('<?php echo get_template_directory_uri(); ?>/assets/images/Bg-Shape-Orange.png') no-repeat center/contain;
}
.cta-bg-shape-blue {
    right: 0;
    top: 0;
    width: 220px;
    height: 180px;
    background: url('<?php echo get_template_directory_uri(); ?>/assets/images/Bg-Shape-blue.png') no-repeat center/contain;
}
@media (max-width: 900px) {
    .cta-content {
        max-width: 95vw;
        padding: 40px 0 40px 0;
    }
    .cta-title {
        font-size: 2rem;
    }
    .cta-subtitle {
        font-size: 1rem;
    }
    .cta-btn {
        font-size: 1.1rem;
        padding: 12px 24px;
        border-radius: 12px;
    }
    .cta-bg-shape-orange, .cta-bg-shape-blue {
        width: 120px;
        height: 90px;
    }
}
@media (max-width: 600px) {
    .cta-content {
        min-width: 0;
        padding: 20px 0 20px 0;
    }
    .cta-title {
        font-size: 1.2rem;
    }
    .cta-subtitle {
        font-size: 0.85rem;
    }
    .cta-btn {
        font-size: 0.95rem;
        padding: 8px 14px;
        border-radius: 8px;
    }
}

/* Tablet styles for 769px - 1024px - Added at end for highest specificity */
@media (min-width: 769px) and (max-width: 1024px) {
     .aboutus-hero-container {
       flex-direction: column;
       align-items: center;
       justify-content: center;
       min-height: 500px;
       padding: 30px 0 0 0;
       text-align: center;
     }
     .aboutus-hero-left, .aboutus-hero-right {
       flex: 1 1 100%;
       padding: 0 5vw 0 5vw;
       display: flex;
       flex-direction: column;
       align-items: center;
       justify-content: center;
     }
     .aboutus-tagline {
       font-size: 2rem;
       margin-bottom: 18px;
       text-align: center;
       order: 1;
     }
     .mobile-only {
       display: block;
       margin-bottom: 18px;
       order: 2;
     }
     .desktop-only {
       display: none;
     }
     .aboutus-headline {
       font-size: 2.3rem;
       margin-bottom: 22px;
       text-align: center;
       order: 3;
     }
     .aboutus-description {
       font-size: 1.15rem;
       margin-bottom: 30px;
       text-align: center;
       order: 4;
     }
     .aboutus-icon-row {
       flex-direction: row;
       justify-content: center;
       align-items: center;
       gap: 24px;
       margin-top: 18px;
       padding-bottom: 18px;
       order: 5;
     }
     .aboutus-icon-box {
       flex: 1 1 0;
       min-width: 90px;
       max-width: 120px;
     }
     .aboutus-icon-label {
       font-size: 1.1rem;
     }
   }

   @media (min-width: 1025px) {
  .profile-linkedin-btn {
    width: 28%;
    min-width: 120px;
    max-width: 220px;
    font-size: 1rem;
    padding: 8px 16px 8px 12px;
    box-sizing: border-box;
  }
}
</style>
<script>
document.addEventListener("DOMContentLoaded", function() {
    function animateOnScroll(selector, offset = 60, className = 'slide-in') {
    var el = document.querySelector(selector);
    if (!el) return;

    var rect = el.getBoundingClientRect();
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;

    if (rect.top < windowHeight - offset) {
        el.classList.add(className);
    } else {
        el.classList.remove(className);
    }
    }

    function animateIconBoxes() {
        var boxes = document.querySelectorAll('.aboutus-icon-box');
        var windowHeight = window.innerHeight || document.documentElement.clientHeight;
        boxes.forEach(function(box, i) {
            var rect = box.getBoundingClientRect();
            if (rect.top < windowHeight - 60) {
                setTimeout(function() {
                    box.classList.add('slide-in');
                }, i * 150); // 150ms delay between each box
            } else {
                box.classList.remove('slide-in');
            }
        });
    }

    function animateAll() {
    animateOnScroll('.aboutus-headline', 60, 'slide-in');
    animateOnScroll('.aboutus-tagline', 60, 'slide-in');
    animateOnScroll('.aboutus-description', 60, 'slide-in');
    animateIconBoxes();
    animateOnScroll('.aboutus-hero-right img', 60, 'zoom-in');
    animateOnScroll('.mission-title', 60, 'slide-in');
    animateOnScroll('.mission-desc', 60, 'slide-in');
    animateOnScroll('.mission-values-title', 60, 'slide-in');
    animateMissionValueBoxes();
    animateOnScroll('.leadership-container', 60, 'slide-in');
    animateProfileCards();
    animateProfileLinkedinBtns();
    animateOnScroll('.locations-title', 60, 'slide-in');
    animateOnScroll('.news-title', 60, 'slide-in');
    animateOnScroll('.news-subtitle', 60, 'slide-in');
    animateNewsCards(); // <-- Add this line
    animateOnScroll('.cta-section', 60, 'slide-in');
    animateOnScroll('.cta-btn', 60, 'slide-in');
}

    // Initial check
    animateAll();

    // On scroll
    window.addEventListener('scroll', animateAll);
});

function animateMissionValueBoxes() {
    var boxes = document.querySelectorAll('.mission-value-box');
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;
    boxes.forEach(function(box, i) {
        var rect = box.getBoundingClientRect();
        if (rect.top < windowHeight - 60) {
            setTimeout(function() {
                box.classList.add('slide-in');
            }, i * 180); // 180ms delay between each box for a nice stagger
        } else {
            box.classList.remove('slide-in');
        }
    });
}

function animateProfileCards() {
    var cards = document.querySelectorAll('.profile-card');
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;
    cards.forEach(function(card, i) {
        var rect = card.getBoundingClientRect();
        if (rect.top < windowHeight - 60) {
            setTimeout(function() {
                card.classList.add('slide-in');
            }, i * 150); // 150ms delay between each card
        } else {
            card.classList.remove('slide-in');
        }
    });
}

function animateProfileLinkedinBtns() {
    var btns = document.querySelectorAll('.profile-linkedin-btn');
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;
    btns.forEach(function(btn, i) {
        var rect = btn.getBoundingClientRect();
        if (rect.top < windowHeight - 60) {
            setTimeout(function() {
                btn.classList.add('slide-in');
            }, i * 120); // 120ms delay between each button
        } else {
            btn.classList.remove('slide-in');
        }
    });
}

function animateNewsCards() {
    var cards = document.querySelectorAll('.news-card');
    var windowHeight = window.innerHeight || document.documentElement.clientHeight;
    cards.forEach(function(card, i) {
        var rect = card.getBoundingClientRect();
        if (rect.top < windowHeight - 60) {
            setTimeout(function() {
                card.classList.add('slide-in');
            }, i * 180); // 180ms delay between each card
        } else {
            card.classList.remove('slide-in');
        }
    });
}
</script>
<?php get_footer(); ?>
