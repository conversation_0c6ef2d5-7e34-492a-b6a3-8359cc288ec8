<?php
/**
 * The template for displaying single service posts
 *
 * @package Krystelis_Custom
 */

get_header();
?>

<main id="primary" class="site-main">
    <div class="container">
        <div class="content-area">
            <div class="main-content">
                <?php while (have_posts()) : the_post(); ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class('service-single'); ?>>
                        <header class="entry-header">
                            <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                        </header>

                        <?php if (has_post_thumbnail()) : ?>
                            <div class="post-thumbnail">
                                <?php the_post_thumbnail('large'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="entry-content">
                            <?php
                            the_content();

                            // Display ACF fields
                            $service_icon = krystelis_get_field('icon');
                            $service_description = krystelis_get_field('description');
                            
                            if ($service_icon || $service_description) :
                            ?>
                                <div class="service-details">
                                    <?php if ($service_icon) : ?>
                                        <div class="service-icon-large">
                                            <img src="<?php echo esc_url($service_icon['url']); ?>" alt="<?php echo esc_attr($service_icon['alt']); ?>">
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($service_description) : ?>
                                        <div class="service-description">
                                            <h3>Service Details</h3>
                                            <p><?php echo esc_html($service_description); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <?php
                            wp_link_pages(array(
                                'before' => '<div class="page-links">' . esc_html__('Pages:', 'krystelis-custom'),
                                'after'  => '</div>',
                            ));
                            ?>
                        </div>

                        <footer class="entry-footer">
                            <div class="service-navigation">
                                <a href="<?php echo esc_url(get_post_type_archive_link('service')); ?>" class="back-to-services">
                                    &larr; <?php esc_html_e('Back to Services', 'krystelis-custom'); ?>
                                </a>
                            </div>
                        </footer>
                    </article>

                    <?php
                    // Related services
                    $related_services = new WP_Query(array(
                        'post_type' => 'service',
                        'posts_per_page' => 3,
                        'post__not_in' => array(get_the_ID()),
                        'orderby' => 'rand'
                    ));
                    
                    if ($related_services->have_posts()) :
                    ?>
                        <section class="related-services">
                            <h3><?php esc_html_e('Other Services', 'krystelis-custom'); ?></h3>
                            <div class="services-grid">
                                <?php while ($related_services->have_posts()) : $related_services->the_post(); ?>
                                    <div class="service-card">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <div class="service-thumbnail">
                                                <a href="<?php the_permalink(); ?>">
                                                    <?php the_post_thumbnail('medium'); ?>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                        <h4><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                                        <p><?php echo wp_trim_words(get_the_excerpt(), 15); ?></p>
                                        <a href="<?php the_permalink(); ?>" class="service-link">Learn More</a>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        </section>
                    <?php 
                    endif;
                    wp_reset_postdata();
                    ?>

                <?php endwhile; ?>
            </div>

            <?php
            // Only show sidebar on services if it has widgets
            if (is_active_sidebar('sidebar-1')) :
                get_sidebar();
            endif;
            ?>
        </div>
    </div>
</main>

<style>
.service-single .service-details {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin: 2rem 0;
}

.service-icon-large img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin-bottom: 1rem;
}

.service-description h3 {
    color: #333;
    margin-bottom: 1rem;
}

.back-to-services {
    color: #0078d4;
    text-decoration: none;
    font-weight: 600;
}

.back-to-services:hover {
    text-decoration: underline;
}

.related-services {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.related-services h3 {
    margin-bottom: 2rem;
    color: #333;
}

.related-services .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.related-services .service-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.related-services .service-thumbnail img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 1rem;
}
</style>

<?php get_footer(); ?>
