/* Careers Page Styles */

/* Hero Section */
.careers-hero {
    background-color: #ffffff;
  margin: 0px 5px 0 5px;
  padding: 1rem 0;
}

.careers-hero__content {
    margin: 0 auto;
    display: flex;
    align-items: center;
    padding: 0 25px;
    gap: 25px;
}

.careers-hero__text {
    text-align: left;
    flex: 1;
}

.careers-hero__image {
    flex: 1;
}

.careers-hero__paragraph {
    font-size: 1rem;
    line-height: 1.6;
    margin: 1rem 0;
}

.careers-hero__highlight {
    font-size: 1.5rem;
    margin: 1.5rem 0;
    color: #007acc;
}

/* Values Section */
.values-section {
    padding: 2rem 1rem;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.value-card {
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

/* Culture Section */
.culture-section {
    padding: 2rem 1rem;
}

.culture-grid {
    display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.75rem;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 5px;
  opacity: 0;
  transform: translateY(120px);
  transition: opacity 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s, transform 1.5s cubic-bezier(0.22, 1, 0.36, 1) 0.2s;
}
.culture-grid.animated {
  opacity: 1;
  transform: translateY(0);
}


.culture-card {
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Testimonials Section */
.testimonials-section {
    padding: 2rem 0;
}

.testimonial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.testimonial-card {
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
}

/* Contact Form Section */
.contact-form-section {
    padding: 2rem 1rem;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 1rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Culture Box Specific Styles */
.culture-box {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.culture-box h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: #007acc;
}

.culture-box p {
   .culture-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.75rem;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 5px;
}

.culture-box {
  background: white;
  padding: 2rem 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  text-align: left;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid #f0f0f0;
  height: 400px;
  display: flex;
  flex-direction: column;
}

.culture-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: rgba(74, 144, 226, 0.3);
  background-color: #0072DA;
  color: white;
}
}

/* Value Arc Wrapper Responsive Styles */
.value-arc-wrapper {
    position: relative;
    width: 100%;
    height: 600px;
    margin: 2rem auto;
    max-width: 1200px;
}

@media (max-width: 1024px) {
    .value-arc-wrapper {
        height: 500px;
        max-width: 900px;
    }

    .semicircle {
        width: 480px;
        height: 300px;
    }

    .semicircle-content {
        width: 350px;
    }

    .semicircle-heading {
        font-size: 2rem;
    }

    .semicircle-paragraph {
        font-size: 18px;
    }
}

@media (max-width: 768px) {
    .value-arc-wrapper {
        height: 450px;
        max-width: 700px;
    }

    .semicircle {
        width: 400px;
        height: 250px;
    }

    .semicircle-content {
        width: 300px;
    }

    .semicircle-heading {
        font-size: 1.75rem;
    }

    .semicircle-paragraph {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .value-arc-wrapper {
        height: 400px;
        max-width: 100%;
        padding: 0 15px;
    }

    .semicircle {
        width: 300px;
        height: 200px;
    }

    .semicircle-content {
        width: 250px;
        bottom: 15px;
    }

    .semicircle-heading {
        font-size: 1.5rem;
        margin-bottom: 8px;
    }

    .semicircle-paragraph {
        font-size: 14px;
        margin-top: 15px;
    }
}

@media (max-width: 320px) {
    .value-arc-wrapper {
        height: 350px;
    }

    .semicircle {
        width: 250px;
        height: 180px;
    }

    .semicircle-content {
        width: 200px;
        bottom: 10px;
    }

    .semicircle-heading {
        font-size: 1.25rem;
    }

    .semicircle-paragraph {
        font-size: 13px;
        margin-top: 10px;
    }
}

/* Responsive Styles */
@media (min-width: 225px) and (max-width: 768px) {
    .careers-hero__title {
        font-size: 1.75rem;
    }

    .careers-hero__paragraph {
        font-size: 0.9rem;
    }

    .careers-hero__highlight {
        font-size: 1.25rem;
    }

    .values-grid,
    .culture-grid,
    .testimonial-grid {
        grid-template-columns: 1fr;
    }

    .value-card,
    .culture-card,
    .testimonial-card {
        padding: 1rem;
    }

    .form-control {
        font-size: 0.9rem;
    }

    /* Adjust spacing for mobile */
    .careers-hero,
    .values-section,
    .culture-section,
    .testimonials-section,
    .contact-form-section {
        padding: 1.5rem 1rem;
    }

    /* Make images responsive */
    img {
        max-width: 100%;
        height: auto;
    }

    /* Adjust button sizes */
    .btn {
        width: 100%;
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    /* Culture Box adjustments */
    .culture-box {
        padding: 1.25rem;
        margin-bottom: 1rem;
    }

    .culture-box h3 {
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
    }

    .culture-box p {
        font-size: 20px;
        line-height: 1.5;
    }
}

/* Additional responsive adjustments */
@media (min-width: 225px) and (max-width: 480px) {
    .careers-hero__title {
        font-size: 1.5rem;
    }

    .careers-hero__paragraph {
        font-size: 0.85rem;
    }

    .careers-hero__highlight {
        font-size: 1.1rem;
    }
}

/* Specific adjustments for small mobile screens */
@media (min-width: 320px) and (max-width: 424px) {
    .culture-box {
        height: 650px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .culture-title {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .culture-description {
        font-size: 1rem;
        line-height: 1.5;
    }

    .culture-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 1rem;
    }

    .positive-culture__heading {
        font-size: 1.75rem;
        margin-bottom: 2rem;
    }

    .culture-grid {
        gap: 2rem;
    }
} 